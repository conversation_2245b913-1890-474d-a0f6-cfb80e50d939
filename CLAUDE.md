# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a 2D digital human training and inference system that generates realistic human video from audio input. The system supports both training/fine-tuning models on specific speakers and zero-shot inference for new speakers.

## Core Architecture

### Main Entry Points
- `app.py` - Multi-process FastAPI + Gradio application using Cython modules
- `app_local.py` - Flask-based local service for digital human processing
- `run_local.py` - WebSocket server for streaming digital human service
- `worker.py` - Main worker interface that dispatches between train/inference/stream modes
- `worker_train.py` - Training pipeline worker
- `worker_stream_infer.py` - Streaming inference worker

### Service Layer
- `service/stream_dh_service.py` - Streaming digital human service with WebSocket support
- `service/trans_dh_service.py` - Traditional batch processing service
- `service/server.py` - Host registration and reporting functionality

### Core Processing Pipeline
- `landmark2face_wy/digitalhuman_interface.py` - Main digital human model interface
- `face_detect_utils/` - Face detection and landmark processing
- `face_lib/` - Face alignment, parsing, and restoration utilities
- `wenet/` - Audio feature extraction using WeNet model
- `preprocess_audio_and_3dmm.py` - Audio preprocessing and 3DMM operations

## Common Development Commands

### Running the Application
```bash
# Local Flask service
python app_local.py

# Multi-process FastAPI + Gradio
python app.py

# WebSocket streaming service
python run_local.py

# Direct worker execution
python worker.py
```

### Configuration
- Main config: `config/config.ini`
- WebSocket config: `service/server_config.yaml`
- Model paths and parameters are defined in individual worker files

### Dependencies
```bash
# Install Python dependencies
pip install -r req.txt

# Key dependencies include:
# - torch, torchvision (PyTorch)
# - cv2 (OpenCV)
# - librosa (audio processing)
# - loguru (logging)
# - flask, fastapi (web frameworks)
# - websockets (streaming)
```

## Key Components

### Digital Human Model Pipeline
1. **Audio Processing**: WeNet-based feature extraction from input audio
2. **Face Detection**: SCRFD-based face detection and landmark extraction
3. **Face Alignment**: 5-point landmark alignment and cropping
4. **Neural Rendering**: Landmark-to-face model for generating facial expressions
5. **Face Restoration**: GFPGAN-based enhancement (optional)
6. **Video Composition**: Blending generated faces back into original video

### Processing Modes
- **Training Mode** (`is_train='1'`): Fine-tune model on new speaker data
- **Zero-shot Mode** (`is_train='0'`): Direct inference without training
- **Inference Mode** (`is_train='2'`): Use pre-trained speaker models
- **Streaming Mode** (`is_stream=True`): Real-time WebSocket-based processing

### File Structure Patterns
- `spk_models/` - Speaker-specific model storage
- `workspace/` - Temporary processing workspace
- `data/temp/` - Temporary file storage
- `log/` - Application logs
- `pretrain_models/` - Base model weights

## Architecture Notes

### Model Loading Strategy
- Models are lazily loaded using singleton pattern (`TransDhTask.instance()`)
- ONNX models are used for optimized inference
- GPU acceleration with CUDA support
- Half-precision (FP16) optimization available

### Streaming Architecture
- WebSocket server handles real-time audio streams
- Audio queue-based processing for continuous input
- Separate processes for audio reception and video generation
- Configuration-driven timeout and connection management

### Face Processing Pipeline
- Face detection → Landmark extraction → Alignment → Cropping
- Multiple face restoration options (GFPGAN, face parsing)
- Dynamic blending strategies (landmark-based, XSeg-based)
- Batch processing for efficiency

### Error Handling
- Comprehensive logging with `loguru`
- Custom error classes in `h_utils/custom.py`
- Callback-based error reporting for job management
- Graceful degradation for missing models/files

## Development Patterns

### Task Management
- Job-based processing with unique job IDs
- Status tracking (pending, running, success, error)
- Progress reporting and callbacks
- File cleanup and workspace management

### Model Management
- OSS-based model storage and retrieval
- Local caching of frequently used models
- Version-controlled model deployment
- Automatic model downloading and unpacking

### Video Processing
- FFmpeg-based video manipulation
- Frame-by-frame processing with batch optimization
- Mask video processing for advanced blending
- Multi-format output support