#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WebRTC客户端测试工具
用于测试服务器的连接状态检测功能
"""

import asyncio
import aiohttp
import time
from loguru import logger
from aiortc import RTCPeerConnection, RTCSessionDescription

class WebRTCTestClient:
    """WebRTC测试客户端"""
    
    def __init__(self, server_url="http://localhost:8080/offer", client_id=None):
        self.server_url = server_url
        self.client_id = client_id or f"client_{int(time.time())}"
        self.pc = None
        self.is_connected = False
        self.connection_start_time = None
        
    async def connect(self):
        """连接到WebRTC服务器"""
        try:
            logger.info(f"【客户端 {self.client_id}】开始连接到 {self.server_url}")
            
            # 创建RTCPeerConnection
            self.pc = RTCPeerConnection()
            
            # 设置事件处理器
            self._setup_event_handlers()
            
            # 创建offer
            offer = await self.pc.createOffer()
            await self.pc.setLocalDescription(offer)
            
            # 发送offer到服务器
            async with aiohttp.ClientSession() as session:
                async with session.post(self.server_url, json={
                    "sdp": self.pc.localDescription.sdp,
                    "type": self.pc.localDescription.type
                }, headers={
                    'Content-Type': 'application/json'
                }) as response:
                    
                    if response.status == 200:
                        answer_data = await response.json()
                        answer = RTCSessionDescription(
                            sdp=answer_data["sdp"],
                            type=answer_data["type"]
                        )
                        await self.pc.setRemoteDescription(answer)
                        
                        self.connection_start_time = time.time()
                        logger.info(f"【客户端 {self.client_id}】连接请求发送成功")
                        return True
                    else:
                        error_text = await response.text()
                        logger.error(f"【客户端 {self.client_id}】连接失败: {response.status} - {error_text}")
                        return False
                        
        except Exception as e:
            logger.error(f"【客户端 {self.client_id}】连接异常: {e}")
            return False
    
    def _setup_event_handlers(self):
        """设置事件处理器"""
        @self.pc.on("connectionstatechange")
        async def on_connectionstatechange():
            logger.info(f"【客户端 {self.client_id}】连接状态: {self.pc.connectionState}")
            if self.pc.connectionState == "connected":
                self.is_connected = True
                connection_time = time.time() - self.connection_start_time if self.connection_start_time else 0
                logger.info(f"【客户端 {self.client_id}】✅ 连接建立成功！耗时: {connection_time:.2f}s")
            elif self.pc.connectionState in ["failed", "closed", "disconnected"]:
                self.is_connected = False
                logger.warning(f"【客户端 {self.client_id}】❌ 连接断开: {self.pc.connectionState}")

        @self.pc.on("iceconnectionstatechange")
        async def on_iceconnectionstatechange():
            logger.info(f"【客户端 {self.client_id}】ICE连接状态: {self.pc.iceConnectionState}")

        @self.pc.on("track")
        async def on_track(track):
            logger.info(f"【客户端 {self.client_id}】接收到媒体轨道: {track.kind}")
            
            # 简单地消费媒体帧
            try:
                while True:
                    frame = await track.recv()
                    # 这里可以处理接收到的帧
                    logger.debug(f"【客户端 {self.client_id}】接收到 {track.kind} 帧")
            except Exception as e:
                logger.debug(f"【客户端 {self.client_id}】媒体轨道结束: {e}")
    
    async def disconnect(self, method="normal"):
        """断开连接"""
        if not self.pc:
            return
            
        try:
            if method == "normal":
                logger.info(f"【客户端 {self.client_id}】正常断开连接")
                await self.pc.close()
            elif method == "abrupt":
                logger.info(f"【客户端 {self.client_id}】突然断开连接（模拟网络中断）")
                # 不调用close()，直接设置为None模拟突然断开
                self.pc = None
            
            self.is_connected = False
            
        except Exception as e:
            logger.error(f"【客户端 {self.client_id}】断开连接异常: {e}")
    
    async def stay_connected(self, duration):
        """保持连接指定时间"""
        logger.info(f"【客户端 {self.client_id}】保持连接 {duration} 秒")
        
        start_time = time.time()
        while time.time() - start_time < duration:
            if not self.is_connected and self.pc and self.pc.connectionState != "connected":
                logger.warning(f"【客户端 {self.client_id}】连接意外断开")
                break
            await asyncio.sleep(1)
        
        logger.info(f"【客户端 {self.client_id}】连接保持完成")

async def test_single_client_connection():
    """测试单个客户端连接和断开"""
    logger.info("=== 测试单个客户端连接 ===")
    
    client = WebRTCTestClient()
    
    try:
        # 连接
        success = await client.connect()
        if not success:
            logger.error("客户端连接失败")
            return
        
        # 等待连接建立
        await asyncio.sleep(3)
        
        # 保持连接一段时间
        await client.stay_connected(10)
        
        # 正常断开
        await client.disconnect("normal")
        
        # 等待服务器检测到断开
        await asyncio.sleep(5)
        
        logger.info("✅ 单个客户端测试完成")
        
    except Exception as e:
        logger.error(f"单个客户端测试失败: {e}")

async def test_multiple_clients():
    """测试多个客户端连接"""
    logger.info("=== 测试多个客户端连接 ===")
    
    clients = []
    
    try:
        # 创建多个客户端
        for i in range(3):
            client = WebRTCTestClient(client_id=f"client_{i}")
            clients.append(client)
        
        # 依次连接
        for client in clients:
            success = await client.connect()
            if success:
                logger.info(f"客户端 {client.client_id} 连接成功")
            await asyncio.sleep(2)
        
        # 保持连接
        await asyncio.sleep(10)
        
        # 依次断开（使用不同方式）
        for i, client in enumerate(clients):
            method = "normal" if i % 2 == 0 else "abrupt"
            await client.disconnect(method)
            logger.info(f"客户端 {client.client_id} 已断开 ({method})")
            await asyncio.sleep(3)
        
        logger.info("✅ 多个客户端测试完成")
        
    except Exception as e:
        logger.error(f"多个客户端测试失败: {e}")

async def test_connection_timeout():
    """测试连接超时检测"""
    logger.info("=== 测试连接超时检测 ===")
    
    client = WebRTCTestClient()
    
    try:
        # 连接
        success = await client.connect()
        if not success:
            logger.error("客户端连接失败")
            return
        
        # 等待连接建立
        await asyncio.sleep(3)
        
        # 模拟客户端突然断开（不发送关闭信号）
        logger.info("模拟客户端突然断开...")
        await client.disconnect("abrupt")
        
        # 等待服务器检测到超时（应该在15秒内检测到）
        logger.info("等待服务器检测到连接超时...")
        await asyncio.sleep(20)
        
        logger.info("✅ 连接超时测试完成")
        
    except Exception as e:
        logger.error(f"连接超时测试失败: {e}")

if __name__ == "__main__":
    logger.info("开始WebRTC客户端测试...")
    
    try:
        # 运行各种测试
        asyncio.run(test_single_client_connection())
        await asyncio.sleep(2)
        
        asyncio.run(test_multiple_clients())
        await asyncio.sleep(2)
        
        asyncio.run(test_connection_timeout())
        
        logger.info("🎉 所有客户端测试完成！")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
