import os
import glob
from moviepy.editor import ImageSe<PERSON>Clip, AudioFileClip, concatenate_audioclips,VideoFileClip

def combine_debug_output(base_dir, speaker_id, output_filename="debug_video.mp4", fps=25):
    """
    将调试输出的图片和音频合并成一个视频文件。

    :param base_dir: 项目的基础目录，这里是 'workspace/models'
    :param speaker_id: speaker_id, 比如 'v3720p'
    :param output_filename: 输出视频文件的名称
    :param fps: 输出视频的帧率
    """
    print(f"开始处理 Speaker ID: {speaker_id}")

    # 定义路径
    model_dir = os.path.join(base_dir, speaker_id)
    image_dir = os.path.join(model_dir, 'face_data_result')
    audio_dir = os.path.join(model_dir, 'audio_data_result')
    output_path = os.path.join(model_dir, output_filename)
    temp_audio_path = os.path.join(model_dir, "temp_concatenated_audio.wav")
    temp_video_path = os.path.join(model_dir, "temp_silent_video.mp4")

    # --- 1. 音频拼接 ---
    print(f"\n步骤 1: 正在从 {audio_dir} 拼接音频...")
    audio_files = sorted(glob.glob(os.path.join(audio_dir, '*.wav')))
    if not audio_files:
        print(f"错误：在 {audio_dir} 中没有找到 .wav 文件。")
        return

    try:
        audio_clips = [AudioFileClip(f) for f in audio_files]
        concatenated_audio = concatenate_audioclips(audio_clips)
        concatenated_audio.write_audiofile(temp_audio_path)
        print(f"音频已成功拼接并保存到: {temp_audio_path}")
    except Exception as e:
        print(f"音频拼接失败: {e}")
        return

    # --- 2. 图片合成视频 ---
    print(f"\n步骤 2: 正在从 {image_dir} 合成视频...")
    image_files = sorted(glob.glob(os.path.join(image_dir, '*.jpg')))
    if not image_files:
        print(f"错误：在 {image_dir} 中没有找到 .jpg 文件。")
        # 清理临时音频文件
        if os.path.exists(temp_audio_path):
            os.remove(temp_audio_path)
        return

    try:
        video_clip = ImageSequenceClip(image_files, fps=fps)
        video_clip.write_videofile(temp_video_path, codec='libx264')
        print(f"无声视频已成功创建并保存到: {temp_video_path}")
    except Exception as e:
        print(f"图片合成视频失败: {e}")
        # 清理临时音频文件
        if os.path.exists(temp_audio_path):
            os.remove(temp_audio_path)
        return


    # --- 3. 音视频合并 ---
    print(f"\n步骤 3: 正在合并音频和视频...")
    try:
        # 重新加载视频以确保没有问题
        video_clip = VideoFileClip(temp_video_path)
        final_clip = video_clip.set_audio(concatenated_audio)
        final_clip.write_videofile(output_path, codec='libx264', audio_codec='aac')
        print(f"🎉 成功！最终视频已保存到: {output_path}")
    except Exception as e:
        print(f"音视频合并失败: {e}")
    finally:
        # --- 4. 清理临时文件 ---
        print("\n步骤 4: 正在清理临时文件...")
        if os.path.exists(temp_audio_path):
            os.remove(temp_audio_path)
            print(f"已删除临时音频文件: {temp_audio_path}")
        if os.path.exists(temp_video_path):
            os.remove(temp_video_path)
            print(f"已删除临时视频文件: {temp_video_path}")


if __name__ == '__main__':
    # 使用示例
    # 请将 'v3720p' 替换为您的 speaker_id
    speaker_id_to_process = 'v3720p' 
    models_base_dir = os.path.join('workspace', 'models')
    
    combine_debug_output(models_base_dir, speaker_id_to_process)
