#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的WebRTC连接测试脚本
"""

import asyncio
import aiohttp
import json
import logging
from aiortc import RTCPeerConnection, RTCSessionDescription

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def simple_webrtc_test():
    """简单的WebRTC连接测试"""
    signaling_url = "http://127.0.0.1:8080/offer"
    
    logger.info("开始WebRTC连接测试...")
    
    # 创建对等连接
    pc = RTCPeerConnection()
    
    # 设置事件处理器
    @pc.on("connectionstatechange")
    async def on_connectionstatechange():
        logger.info(f"连接状态: {pc.connectionState}")
    
    @pc.on("iceconnectionstatechange")
    async def on_iceconnectionstatechange():
        logger.info(f"ICE连接状态: {pc.iceConnectionState}")
    
    try:
        # 创建offer
        pc.addTransceiver("video", direction="recvonly")
        pc.addTransceiver("audio", direction="recvonly")
        offer = await pc.createOffer()
        await pc.setLocalDescription(offer)
        
        logger.info("发送offer到信令服务器...")
        
        # 发送offer到信令服务器
        async with aiohttp.ClientSession() as session:
            async with session.post(
                signaling_url,
                json={"sdp": pc.localDescription.sdp, "type": pc.localDescription.type},
                timeout=aiohttp.ClientTimeout(total=10)
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info("✅ 成功接收到answer")
                    
                    # 设置远程描述
                    answer = RTCSessionDescription(sdp=data["sdp"], type=data["type"])
                    await pc.setRemoteDescription(answer)
                    logger.info("✅ 成功设置远程描述")
                    
                    # 等待连接建立
                    await asyncio.sleep(3)
                    
                    logger.info("🎉 WebRTC连接测试成功!")
                    return True
                else:
                    error_text = await response.text()
                    logger.error(f"❌ 信令服务器响应失败: {response.status}")
                    logger.error(f"错误信息: {error_text}")
                    return False
                    
    except asyncio.TimeoutError:
        logger.error("❌ 连接超时")
        return False
    except Exception as e:
        logger.error(f"❌ 连接测试失败: {e}")
        return False
    finally:
        await pc.close()

async def test_server_availability():
    """测试服务器可用性"""
    signaling_url = "http://127.0.0.1:8080/offer"
    
    try:
        async with aiohttp.ClientSession() as session:
            # 尝试连接服务器
            async with session.post(
                signaling_url,
                json={"test": "connection"},
                timeout=aiohttp.ClientTimeout(total=5)
            ) as response:
                logger.info(f"服务器响应状态: {response.status}")
                return response.status != 404
    except Exception as e:
        logger.error(f"无法连接到服务器: {e}")
        return False

async def main():
    """主函数"""
    logger.info("=" * 50)
    logger.info("简单WebRTC连接测试")
    logger.info("=" * 50)
    
    # 测试服务器可用性
    logger.info("测试服务器可用性...")
    server_ok = await test_server_availability()
    if not server_ok:
        logger.error("❌ 服务器不可用，请确保数字人系统已启动")
        return
    
    logger.info("✅ 服务器可用")
    
    # 测试WebRTC连接
    logger.info("测试WebRTC连接...")
    success = await simple_webrtc_test()
    
    if success:
        logger.info("🎉 所有测试通过!")
    else:
        logger.error("💥 连接测试失败")

if __name__ == "__main__":
    asyncio.run(main()) 