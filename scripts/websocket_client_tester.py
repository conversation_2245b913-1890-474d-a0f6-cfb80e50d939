import asyncio
import websockets
import argparse
import wave
import time
import os
import json
import base64

async def send_audio(uri: str, audio_path: str,test_finish:float):
    """
    连接到WebSocket服务器，并循环流式传输指定的WAV音频文件。

    Args:
        uri (str): WebSocket服务器的完整URI，包含任务代码 (e.g., "ws://localhost:8765/task123").
        audio_path (str): 本地WAV音频文件的路径.
    """
    if not os.path.exists(audio_path):
        print(f"错误: 音频文件未找到 -> {audio_path}")
        return
    audio_path = convert_16k_wav(audio_path)
    # 设置流式传输参数
    # 每次发送的音频时长（毫秒）
    chunk_duration_ms = 100
    
    print(f"准备连接到: {uri}")
    print(f"将要流式传输的音频文件: {audio_path}")
    print(f"每次发送 {chunk_duration_ms}毫秒 的音频数据")
    start_time = time.time()
    send_count = 0
    while True: # 无限循环以支持持续测试
        try:
            async with websockets.connect(uri) as websocket:
                print("\n成功连接到WebSocket服务器！开始传输音频...")
                start_time = time.time()
                # msg = json.dumps({'status':'start', 'data':None})
                # await websocket.send(msg)
                
                while True:

                    with wave.open(audio_path, 'rb') as wf:
                        # 校验音频格式
                        if wf.getnchannels() != 1 or wf.getsampwidth() != 2 or wf.getframerate() != 16000:
                            print("错误: 音频文件必须是 16kHz, 16-bit, 单声道 (mono) 的WAV格式。")


                        # 计算每个数据块的大小
                        chunk_size = int(wf.getframerate() * (chunk_duration_ms / 1000) * wf.getsampwidth())

                        while True:
                            data = wf.readframes(chunk_size // wf.getsampwidth())
                            if not data:
                                print("音频文件传输完毕。")
                                break
                                # continue
                            
                            # 将字节数据编码为base64字符串进行JSON序列化
                            data_b64 = base64.b64encode(data).decode('utf-8')
                            msg = json.dumps({'status':'data', 'data':data_b64})
                            await websocket.send(msg)
                            send_count += 1
                            if send_count % 20 == 0:
                                print("发送音频数据",len(data),send_count)
                            # 等待与发送的音频时长相等的时间，以模拟实时流
                            await asyncio.sleep(chunk_duration_ms / 2000)
        
                    
                        # print("等待3秒后重新开始...")
                        # await asyncio.sleep(3)
                    # 测试发送结束信号
                        if test_finish>0 and time.time() - start_time > test_finish: 
                            print("测试结束，发送结束信号")
                            msg = json.dumps({'status':'end', 'data':None})
                            await websocket.send(msg)
                            break

                    msg = json.dumps({'status':'silence', 'data':None})
                    await websocket.send(msg)
                    print("发送静音信号")
                    print("等待10秒后重新开始...")
                    time.sleep(15)

        except websockets.exceptions.ConnectionClosed as e:
            print(f"连接已关闭: {e}. 5秒后尝试重连...")
            await asyncio.sleep(5)
        except ConnectionRefusedError:
            print("连接被拒绝。请确保服务器正在运行。5秒后尝试重连...")
            await asyncio.sleep(5)
        except Exception as e:
            print(f"发生未知错误: {e}. 5秒后尝试重连...")
            await asyncio.sleep(5)

def convert_16k_wav(input_path):
    # 检查音频文件是否为单通道16K的wav格式，如果不是则进行转换
    with wave.open(input_path, 'rb') as wf:
        channels = wf.getnchannels()
        sample_rate = wf.getframerate()
        sample_width = wf.getsampwidth()
        
        # 检查是否为单通道、16K采样率、16位深度
        if channels != 1 or sample_rate != 16000 or sample_width != 2:
            print(f"音频格式不符合要求 (当前: {channels}通道, {sample_rate}Hz, {sample_width*8}位)")
            print("正在转换为16K单通道WAV格式...")
            
            # 使用ffmpeg进行格式转换
            output_path = input_path.replace(".wav", "_16k.wav")
            ffmpeg_cmd = f'ffmpeg -i "{input_path}" -ac 1 -ar 16000 -acodec pcm_s16le -y "{output_path}"'
            
            try:
                import subprocess
                result = subprocess.run(ffmpeg_cmd, shell=True, capture_output=True, text=True)
                if result.returncode == 0:
                    print(f"转换成功: {output_path}")
                    return output_path
                else:
                    print(f"转换失败: {result.stderr}")
                    return input_path
            except Exception as e:
                print(f"转换过程中发生错误: {e}")
                return input_path
        else:
            print("音频格式符合要求，无需转换")
            return input_path
   


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="WebSocket音频流测试客户端")
    parser.add_argument(
        "--audio-file",
        default="11_16k.wav",
        help="要流式传输的本地WAV音频文件路径 (必须是 16kHz, 16-bit, 单声道)."
    )
    parser.add_argument(
        "--spk_id",
        # default="walk",
        default="v3720p",
        help="要连接的数字人id (由easy_submit返回或指定)."
    )
    parser.add_argument(
        "--host",
        default="localhost",
        help="WebSocket服务器的主机名或IP地址."
    )
    parser.add_argument(
        "--test_finish",
        default=0,
        type=float,
        help="测试是否结束."
    )
    parser.add_argument(
        "--port",
        default=8765,
        type=int,
        help="WebSocket服务器的端口."
    )

    args = parser.parse_args()

    # 构建完整的URI
    full_uri = f"ws://{args.host}:{args.port}/{args.spk_id}"

    # 运行asyncio事件循环
    try:
        asyncio.run(send_audio(full_uri, args.audio_file,args.test_finish))
    except KeyboardInterrupt:
        print("\n客户端已停止。") 