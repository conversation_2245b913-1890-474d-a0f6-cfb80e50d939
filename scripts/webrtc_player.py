import asyncio
import json
import logging
import platform
import time
import sys

import aiohttp
import cv2
import numpy as np
import pyaudio
from aiortc import RTCPeerConnection, RTCSessionDescription, MediaStreamTrack
from aiortc.contrib.media import MediaStreamTrack
from av import AudioFrame, VideoFrame

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
# 启用aiortc和aioice的详细调试日志
logging.getLogger("aiortc").setLevel(logging.DEBUG)
logging.getLogger("aioice").setLevel(logging.DEBUG)
logger = logging.getLogger("webrtc_client")

# --- Audio Player ---
class AudioPlayer:
    """A class to play audio frames using PyAudio."""
    def __init__(self, track: MediaStreamTrack):
        self.track = track
        self.p = None
        self.stream = None
        self.is_playing = False
        self.frame_count = 0

    async def play(self):
        logger.info("【AudioPlayer】音频播放器启动")
        first_frame = True
        
        try:
            while True:
                try:
                    logger.debug("【AudioPlayer】等待接收音频帧...")
                    frame: AudioFrame = await self.track.recv()
                    
                    if first_frame:
                        logger.info(f"【AudioPlayer】接收到第一帧，正在初始化PyAudio...")
                        logger.info(f"【AudioPlayer】音频参数: Samples={frame.samples}, Sample Rate={frame.sample_rate}, Format={frame.format}")
                        
                        self.p = pyaudio.PyAudio()
                        self.stream = self.p.open(
                            format=pyaudio.paInt16,
                            channels=1,
                            rate=frame.sample_rate,
                            output=True,
                            frames_per_buffer=1024,  # 添加缓冲区大小
                        )
                        first_frame = False
                        self.is_playing = True
                        logger.info("【AudioPlayer】PyAudio初始化成功")

                    # 转换音频数据
                    audio_data = frame.to_ndarray()
                    if audio_data.dtype != np.int16:
                        # 确保数据类型正确
                        audio_data = np.clip(audio_data * 32767, -32768, 32767).astype(np.int16)
                    
                    data = audio_data.tobytes()
                    self.stream.write(data)
                    
                    self.frame_count += 1
                    if self.frame_count % 100 == 0:
                        logger.info(f"【AudioPlayer】已处理 {self.frame_count} 个音频帧")
                        
                except asyncio.CancelledError:
                    logger.info("【AudioPlayer】音频播放被取消")
                    break
                except Exception as e:
                    logger.error(f"【AudioPlayer】音频播放错误: {e}")
                    break
                    
        except Exception as e:
            logger.error(f"【AudioPlayer】音频播放器初始化失败: {e}")
        finally:
            self.stop()

    def stop(self):
        self.is_playing = False
        if self.stream:
            try:
                self.stream.stop_stream()
                self.stream.close()
            except Exception as e:
                logger.error(f"【AudioPlayer】停止音频流时出错: {e}")
            self.stream = None
        if self.p:
            try:
                self.p.terminate()
            except Exception as e:
                logger.error(f"【AudioPlayer】终止PyAudio时出错: {e}")
            self.p = None
        logger.info(f"【AudioPlayer】音频播放器已停止，共处理 {self.frame_count} 个音频帧")

# --- Video Player ---
class VideoPlayer:
    """A class to display video frames using OpenCV."""
    def __init__(self, track: MediaStreamTrack):
        self.track = track
        self.window_name = f"WebRTC Stream - {self.track.kind}"
        self.is_playing = False
        self.frame_count = 0

    async def play(self):
        logger.info("【VideoPlayer】视频播放器启动")
        start_time = time.time()
        
        try:
            # 创建窗口
            cv2.namedWindow(self.window_name, cv2.WINDOW_NORMAL)
            cv2.resizeWindow(self.window_name, 1280, 720)
            
            while True:
                try:
                    logger.debug("【VideoPlayer】等待接收下一帧...")
                    frame: VideoFrame = await self.track.recv()
                    
                    # 转换为OpenCV格式
                    img = frame.to_ndarray(format="bgr24")
                    
                    # 显示帧信息
                    height, width = img.shape[:2]
                    logger.debug(f"【VideoPlayer】接收到帧: {width}x{height}, Timestamp: {frame.pts}")
                    
                    # 显示图像
                    cv2.imshow(self.window_name, img)
                    
                    self.frame_count += 1
                    elapsed = time.time() - start_time
                    
                    # 每秒计算一次FPS
                    if elapsed > 1:
                        fps = self.frame_count / elapsed
                        logger.info(f"【VideoPlayer】接收FPS: {fps:.2f}, 总帧数: {self.frame_count}")
                        start_time = time.time()
                        self.frame_count = 0
                    
                    # 检查按键
                    key = cv2.waitKey(1) & 0xFF
                    if key == ord("q"):
                        logger.info("【VideoPlayer】用户按下Q键，退出播放")
                        break
                    elif key == ord("s"):
                        # 保存当前帧
                        timestamp = int(time.time())
                        filename = f"frame_{timestamp}.jpg"
                        cv2.imwrite(filename, img)
                        logger.info(f"【VideoPlayer】已保存帧到: {filename}")
                        
                except asyncio.CancelledError:
                    logger.info("【VideoPlayer】视频播放被取消")
                    break
                except Exception as e:
                    logger.error(f"【VideoPlayer】视频播放错误: {e}")
                    break
                    
        except Exception as e:
            logger.error(f"【VideoPlayer】视频播放器初始化失败: {e}")
        finally:
            self.stop()

    def stop(self):
        self.is_playing = False
        try:
            cv2.destroyWindow(self.window_name)
            cv2.destroyAllWindows()
        except Exception as e:
            logger.error(f"【VideoPlayer】关闭窗口时出错: {e}")
        logger.info(f"【VideoPlayer】视频播放器已停止，共处理 {self.frame_count} 个视频帧")

def create_peer_connection():
    """创建优化的WebRTC对等连接"""
    # 创建RTCPeerConnection并设置优化配置
    pc = RTCPeerConnection()
    
    # 注意：aiortc的RTCPeerConnection不支持直接设置这些配置
    # 这些配置会在连接建立时自动处理
    # 我们只需要创建基本的连接即可
    
    logger.info("【Client】创建WebRTC对等连接")
    return pc

async def run(pc: RTCPeerConnection, signaling_server: str):
    """Main function to run the WebRTC client."""
    players = {}
    closed_event = asyncio.Event()

    @pc.on("track")
    async def on_track(track: MediaStreamTrack):
        logger.info(f"【Client】接收到轨道: {track.kind}")
        if track.kind == "audio":
            logger.info("【Client】检测到音频轨道，正在初始化 AudioPlayer...")
            player = AudioPlayer(track)
            players["audio"] = asyncio.create_task(player.play())
        elif track.kind == "video":
            logger.info("【Client】检测到视频轨道，正在初始化 VideoPlayer...")
            player = VideoPlayer(track)
            players["video"] = asyncio.create_task(player.play())

    @pc.on("connectionstatechange")
    async def on_connectionstatechange():
        logger.info(f"【Client】连接状态变化: {pc.connectionState}")
        if pc.connectionState in ("failed", "closed", "disconnected"):
            closed_event.set()

    @pc.on("iceconnectionstatechange")
    async def on_iceconnectionstatechange():
        logger.info(f"【Client】ICE连接状态: {pc.iceConnectionState}")

    @pc.on("icegatheringstatechange")
    async def on_icegatheringstatechange():
        logger.info(f"【Client】ICE收集状态: {pc.iceGatheringState}")

    try:
        # 创建优化的媒体约束
        offer_options = {
            "offerToReceiveAudio": True,
            "offerToReceiveVideo": True
        }
        
        # 添加媒体约束以优化性能
        constraints = {
            "audio": {
                "echoCancellation": True,
                "noiseSuppression": True,
                "autoGainControl": True
            },
            "video": {
                "width": {"ideal": 1280, "max": 1920},
                "height": {"ideal": 720, "max": 1080},
                "frameRate": {"ideal": 25, "max": 30}
            }
        }
        
        # Create offer with optimized constraints
        pc.addTransceiver("video", direction="recvonly")
        pc.addTransceiver("audio", direction="recvonly")
        offer = await pc.createOffer()
        await pc.setLocalDescription(offer)
        
        logger.info(f"【Client】发送offer到信令服务器: {signaling_server}")
        
        # Exchange SDP with the server
        async with aiohttp.ClientSession() as session:
            async with session.post(
                signaling_server,
                json={"sdp": pc.localDescription.sdp, "type": pc.localDescription.type},
                timeout=aiohttp.ClientTimeout(total=30)  # 添加超时
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    answer = RTCSessionDescription(sdp=data["sdp"], type=data["type"])
                    await pc.setRemoteDescription(answer)
                    logger.info("【Client】成功设置远程描述")
                else:
                    error_text = await response.text()
                    logger.error(f"【Client】从服务器获取answer失败: {response.status}, {error_text}")
                    return

        # Wait for the connection to be closed
        logger.info("【Client】等待连接建立并播放媒体...")
        await closed_event.wait()

    except asyncio.TimeoutError:
        logger.error("【Client】连接超时")
    except Exception as e:
        logger.error(f"【Client】发生错误: {e}")
    finally:
        logger.info("【Client】关闭对等连接")
        await pc.close()
        for task in players.values():
            task.cancel()
        
        # 等待所有播放器任务完成
        if players:
            await asyncio.gather(*players.values(), return_exceptions=True)

if __name__ == "__main__":
    signaling_url = "http://127.0.0.1:8080/offer"
    
    # Create optimized peer connection
    peer_connection = create_peer_connection()
    
    # Run the client
    loop = asyncio.get_event_loop()
    try:
        loop.run_until_complete(
            run(pc=peer_connection, signaling_server=signaling_url)
        )
    except KeyboardInterrupt:
        logger.info("【Client】用户中断程序")
    except Exception as e:
        logger.error(f"【Client】程序异常: {e}")
    finally:
        logger.info("【Client】程序关闭")
        # The `run` function's finally block already handles closing
        if peer_connection.connectionState != "closed":
             loop.run_until_complete(peer_connection.close()) 