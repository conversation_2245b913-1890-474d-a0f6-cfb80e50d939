#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WebRTC问题诊断脚本
用于分析WebRTC连接问题，包括网络延迟、丢包、码率等
"""

import asyncio
import json
import logging
import time
import socket
import subprocess
import platform
from typing import Dict, List

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class WebRTCDiagnostics:
    """WebRTC连接诊断工具"""
    
    def __init__(self):
        self.results = {}
        
    async def run_network_tests(self):
        """运行网络测试"""
        logger.info("开始网络诊断...")
        
        # 测试本地网络
        await self._test_local_network()
        
        # 测试STUN服务器连接
        await self._test_stun_servers()
        
        # 测试端口可用性
        await self._test_ports()
        
        # 测试系统性能
        await self._test_system_performance()
        
        return self.results
    
    async def _test_local_network(self):
        """测试本地网络配置"""
        logger.info("测试本地网络配置...")
        
        try:
            # 获取本地IP
            hostname = socket.gethostname()
            local_ip = socket.gethostbyname(hostname)
            
            # 测试回环地址
            loopback_test = await self._ping_host("127.0.0.1")
            
            self.results['local_network'] = {
                'hostname': hostname,
                'local_ip': local_ip,
                'loopback_ping': loopback_test,
                'status': 'OK' if loopback_test['success'] else 'FAILED'
            }
            
            logger.info(f"本地网络测试完成: {self.results['local_network']['status']}")
            
        except Exception as e:
            logger.error(f"本地网络测试失败: {e}")
            self.results['local_network'] = {'status': 'ERROR', 'error': str(e)}
    
    async def _test_stun_servers(self):
        """测试STUN服务器连接"""
        logger.info("测试STUN服务器连接...")
        
        stun_servers = [
            "stun.l.google.com:19302",
            "stun1.l.google.com:19302",
            "stun.stunprotocol.org:3478"
        ]
        
        stun_results = {}
        
        for server in stun_servers:
            try:
                host, port = server.split(':')
                ping_result = await self._ping_host(host)
                stun_results[server] = ping_result
            except Exception as e:
                stun_results[server] = {'success': False, 'error': str(e)}
        
        self.results['stun_servers'] = stun_results
        logger.info(f"STUN服务器测试完成，可用服务器: {len([s for s in stun_results.values() if s.get('success', False)])}")
    
    async def _test_ports(self):
        """测试端口可用性"""
        logger.info("测试端口可用性...")
        
        ports_to_test = [8080, 8081, 8082, 9000, 9001]
        port_results = {}
        
        for port in ports_to_test:
            try:
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                sock.settimeout(2)
                result = sock.connect_ex(('127.0.0.1', port))
                sock.close()
                
                port_results[port] = {
                    'available': result != 0,
                    'status': 'OPEN' if result != 0 else 'CLOSED'
                }
            except Exception as e:
                port_results[port] = {'available': False, 'error': str(e)}
        
        self.results['ports'] = port_results
        logger.info(f"端口测试完成，可用端口: {len([p for p in port_results.values() if p.get('available', False)])}")
    
    async def _test_system_performance(self):
        """测试系统性能"""
        logger.info("测试系统性能...")
        
        try:
            # CPU信息
            cpu_count = await self._get_cpu_info()
            
            # 内存信息
            memory_info = await self._get_memory_info()
            
            # 网络接口信息
            network_info = await self._get_network_info()
            
            self.results['system_performance'] = {
                'cpu': cpu_count,
                'memory': memory_info,
                'network': network_info,
                'platform': platform.platform(),
                'python_version': platform.python_version()
            }
            
            logger.info("系统性能测试完成")
            
        except Exception as e:
            logger.error(f"系统性能测试失败: {e}")
            self.results['system_performance'] = {'error': str(e)}
    
    async def _ping_host(self, host: str) -> Dict:
        """ping主机并返回结果"""
        try:
            if platform.system() == "Windows":
                cmd = ["ping", "-n", "1", host]
            else:
                cmd = ["ping", "-c", "1", host]
            
            process = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await process.communicate()
            
            if process.returncode == 0:
                # 解析ping结果
                output = stdout.decode()
                if "time=" in output or "time<" in output:
                    return {'success': True, 'reachable': True}
                else:
                    return {'success': True, 'reachable': False}
            else:
                return {'success': False, 'reachable': False, 'error': stderr.decode()}
                
        except Exception as e:
            return {'success': False, 'reachable': False, 'error': str(e)}
    
    async def _get_cpu_info(self) -> Dict:
        """获取CPU信息"""
        try:
            import psutil
            return {
                'count': psutil.cpu_count(),
                'count_logical': psutil.cpu_count(logical=True),
                'usage_percent': psutil.cpu_percent(interval=1)
            }
        except ImportError:
            return {'error': 'psutil not available'}
    
    async def _get_memory_info(self) -> Dict:
        """获取内存信息"""
        try:
            import psutil
            memory = psutil.virtual_memory()
            return {
                'total_gb': round(memory.total / (1024**3), 2),
                'available_gb': round(memory.available / (1024**3), 2),
                'percent_used': memory.percent
            }
        except ImportError:
            return {'error': 'psutil not available'}
    
    async def _get_network_info(self) -> Dict:
        """获取网络接口信息"""
        try:
            import psutil
            interfaces = {}
            for name, stats in psutil.net_if_stats().items():
                if stats.isup:
                    interfaces[name] = {
                        'speed': stats.speed,
                        'mtu': stats.mtu,
                        'duplex': stats.duplex
                    }
            return interfaces
        except ImportError:
            return {'error': 'psutil not available'}
    
    def generate_report(self) -> str:
        """生成诊断报告"""
        report = []
        report.append("=" * 60)
        report.append("WebRTC连接诊断报告")
        report.append("=" * 60)
        report.append("")
        
        # 本地网络
        if 'local_network' in self.results:
            net = self.results['local_network']
            report.append("【本地网络】")
            report.append(f"  主机名: {net.get('hostname', 'N/A')}")
            report.append(f"  本地IP: {net.get('local_ip', 'N/A')}")
            report.append(f"  回环测试: {net.get('status', 'N/A')}")
            report.append("")
        
        # STUN服务器
        if 'stun_servers' in self.results:
            report.append("【STUN服务器】")
            for server, result in self.results['stun_servers'].items():
                status = "✓" if result.get('success', False) else "✗"
                report.append(f"  {status} {server}")
            report.append("")
        
        # 端口
        if 'ports' in self.results:
            report.append("【端口可用性】")
            for port, result in self.results['ports'].items():
                status = "✓" if result.get('available', False) else "✗"
                report.append(f"  {status} 端口 {port}: {result.get('status', 'N/A')}")
            report.append("")
        
        # 系统性能
        if 'system_performance' in self.results:
            perf = self.results['system_performance']
            report.append("【系统性能】")
            report.append(f"  平台: {perf.get('platform', 'N/A')}")
            report.append(f"  Python版本: {perf.get('python_version', 'N/A')}")
            
            if 'cpu' in perf and 'error' not in perf['cpu']:
                cpu = perf['cpu']
                report.append(f"  CPU核心数: {cpu.get('count', 'N/A')}")
                report.append(f"  CPU使用率: {cpu.get('usage_percent', 'N/A')}%")
            
            if 'memory' in perf and 'error' not in perf['memory']:
                mem = perf['memory']
                report.append(f"  总内存: {mem.get('total_gb', 'N/A')} GB")
                report.append(f"  可用内存: {mem.get('available_gb', 'N/A')} GB")
                report.append(f"  内存使用率: {mem.get('percent_used', 'N/A')}%")
            
            report.append("")
        
        # 建议
        report.append("【优化建议】")
        suggestions = self._generate_suggestions()
        for i, suggestion in enumerate(suggestions, 1):
            report.append(f"  {i}. {suggestion}")
        
        report.append("")
        report.append("=" * 60)
        
        return "\n".join(report)
    
    def _generate_suggestions(self) -> List[str]:
        """根据诊断结果生成优化建议"""
        suggestions = []
        
        # 检查STUN服务器
        if 'stun_servers' in self.results:
            available_stun = len([s for s in self.results['stun_servers'].values() if s.get('success', False)])
            if available_stun == 0:
                suggestions.append("STUN服务器不可用，建议检查网络连接或使用TURN服务器")
            elif available_stun < 2:
                suggestions.append("STUN服务器可用性较低，建议添加更多STUN服务器")
        
        # 检查端口
        if 'ports' in self.results:
            available_ports = len([p for p in self.results['ports'].values() if p.get('available', False)])
            if available_ports == 0:
                suggestions.append("没有可用端口，建议检查防火墙设置")
        
        # 检查系统性能
        if 'system_performance' in self.results:
            perf = self.results['system_performance']
            if 'memory' in perf and 'error' not in perf['memory']:
                mem = perf['memory']
                if mem.get('percent_used', 0) > 80:
                    suggestions.append("内存使用率过高，建议关闭不必要的程序")
            
            if 'cpu' in perf and 'error' not in perf['cpu']:
                cpu = perf['cpu']
                if cpu.get('usage_percent', 0) > 80:
                    suggestions.append("CPU使用率过高，建议优化系统性能")
        
        # 通用建议
        suggestions.extend([
            "降低视频码率到1Mbps以下以减少网络压力",
            "使用720p分辨率而不是1080p",
            "确保网络MTU设置正确（建议1200字节）",
            "检查防火墙是否阻止了WebRTC流量",
            "考虑使用有线网络而不是WiFi以减少延迟"
        ])
        
        return suggestions

async def main():
    """主函数"""
    logger.info("开始WebRTC连接诊断...")
    
    diagnostics = WebRTCDiagnostics()
    await diagnostics.run_network_tests()
    
    # 生成报告
    report = diagnostics.generate_report()
    print(report)
    
    # 保存报告到文件
    timestamp = time.strftime("%Y%m%d_%H%M%S")
    filename = f"webrtc_diagnostics_{timestamp}.txt"
    
    with open(filename, 'w', encoding='utf-8') as f:
        f.write(report)
    
    logger.info(f"诊断报告已保存到: {filename}")

if __name__ == "__main__":
    asyncio.run(main()) 