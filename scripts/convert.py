# -*-coding: utf-8 -*-
# @Time :2025-05-28
# <AUTHOR> lishengjing
# @Email : <EMAIL>
# @File : convert.py
    
"""
目前版本能转，但是没有验证过效果
"""


import sys
import os
import torch
import numpy as np
import time
from loguru import logger
import inspect
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from landmark2face_wy.digitalhuman_interface import DigitalHumanModel
from y_utils.config import GlobalConfig

"""engine"""
pth_model_path = r'landmark2face_wy/checkpoints/anylang/dinet_v1_20240131.pth'
onnx_model_path = pth_model_path.replace(".pth", ".onnx")
slimonnx_model_path = pth_model_path.replace(".pth", "_slim.onnx")
trt_model_path = pth_model_path.replace(".pth", ".engine")




def convert_pth_to_onnx(onnx_path,half=False):
    """将模型转换为ONNX格式
    
    Args:
        model: 模型
        inputs:list
        onnx_path: ONNX模型保存路径
        half: 是否使用半精度
    """
    # 创建示例输入
    digital_human_model = DigitalHumanModel(GlobalConfig.instance().blend_dynamic, GlobalConfig.instance().chaofen_before, face_blur_detect=True,half=False)

    source_img = torch.randn(1, 3, 256, 256).cuda()
    ref_img = torch.randn(1, 3, 256, 256).cuda()
    audio_feature = torch.randn(1, 256, 20).cuda()

    if half:
        source_img = source_img.half()
        ref_img = ref_img.half()
        audio_feature = audio_feature.half()
    
    # 打印模型信息
    logger.info(f"Model type: {type(digital_human_model.model.netG)}")
    logger.info(f"Model forward signature: {inspect.signature(digital_human_model.model.netG.forward)}")
    
    # 打印输入信息
    logger.info(f"Input shapes:")
    logger.info(f"source_img: {source_img.shape}")
    logger.info(f"ref_img: {ref_img.shape}")
    logger.info(f"audio_feature: {audio_feature.shape}")
        
    # 导出ONNX模型
    torch.onnx.export(
        digital_human_model.model.netG,
        (source_img, ref_img, audio_feature),
        onnx_path,
        input_names=['source_img', 'ref_img', 'audio_feature'],
        output_names=['output'],
        dynamic_axes={
            'source_img': {0: 'batch_size'},
            'ref_img': {0: 'batch_size'},
            'audio_feature': {0: 'batch_size'},
            'output': {0: 'batch_size'}
        },
        opset_version=16,  # 提升opset版本到16
        do_constant_folding=False,  # 禁用常量折叠
        verbose=True,  # 打印详细信息
        training=torch.onnx.TrainingMode.EVAL,  # 推理模式
        operator_export_type=torch.onnx.OperatorExportTypes.ONNX,  # 使用ONNX算子
        export_params=True,  # 导出模型参数
        keep_initializers_as_inputs=True,  # 保持初始化器作为输入


        custom_opsets=None  # 使用默认的opset
    )
    logger.info(f"ONNX model saved to {onnx_path}")

    # 删除digital_human_model，释放显存
    del digital_human_model
    torch.cuda.empty_cache()

def slim_onnx():
    pass

def check_onnx_model(onnx_path):
    # 验证ONNX模型
    try:
        import onnx
        model = onnx.load(onnx_path)
        onnx.checker.check_model(model)
        logger.info("ONNX model check passed")
        
        # 打印模型结构信息
        logger.info("Model inputs:")
        for input in model.graph.input:
            logger.info(f"  Name: {input.name}, Shape: {[d.dim_value for d in input.type.tensor_type.shape.dim]}")
        
        logger.info("Model outputs:")
        for output in model.graph.output:
            logger.info(f"  Name: {output.name}, Shape: {[d.dim_value for d in output.type.tensor_type.shape.dim]}")
            
    except Exception as e:
        logger.error(f"ONNX model check failed: {e}")
        raise
        
def convert_to_trt(onnx_path, trt_path, fp16_mode=True, min_batch=1, max_batch=8):
    """将ONNX模型转换为TensorRT模型
    
    Args:
        onnx_path: ONNX模型路径
        trt_path: TensorRT模型保存路径
        fp16_mode: 是否使用FP16精度
        min_batch: 最小batch size
        max_batch: 最大batch size
    """
    import tensorrt as trt
    
    logger.info(f"Converting ONNX model to TensorRT: {onnx_path}")
    logger.info(f"TensorRT version: {trt.__version__}")
    
    Logger = trt.Logger(trt.Logger.WARNING)
    builder = trt.Builder(Logger)
    network = builder.create_network(1 << int(trt.NetworkDefinitionCreationFlag.EXPLICIT_BATCH))
    config = builder.create_builder_config()
    parser = trt.OnnxParser(network, Logger)
    
    # 解析ONNX文件
    with open(onnx_path, 'rb') as model:
        if not parser.parse(model.read()):
            for error in range(parser.num_errors):
                logger.error(f"ONNX parse error: {parser.get_error(error)}")
            raise RuntimeError("Failed to parse ONNX model")
    logger.info("ONNX model parsed successfully")
    
    # 打印网络信息
    logger.info("Network information:")
    for i in range(network.num_inputs):
        tensor = network.get_input(i)
        logger.info(f"Input {i}: {tensor.name}, Shape: {tensor.shape}")
    
    for i in range(network.num_outputs):
        tensor = network.get_output(i)
        logger.info(f"Output {i}: {tensor.name}, Shape: {tensor.shape}")
    
    # 设置配置
    config.set_memory_pool_limit(trt.MemoryPoolType.WORKSPACE, 1 << 30)  # 1GB工作空间
    if fp16_mode and builder.platform_has_fast_fp16:
        config.set_flag(trt.BuilderFlag.FP16)
        logger.info("Using FP16 mode")
        
    # 设置动态batch范围
    profile = builder.create_optimization_profile()
    for input_name in ['source_img', 'ref_img', 'audio_feature']:
        shape = (3, 256, 256) if input_name != 'audio_feature' else (256, 20)
        profile.set_shape(
            input_name,
            (min_batch,) + shape,
            (max_batch,) + shape,
            (max_batch,) + shape
        )
        logger.info(f"Setting shape for {input_name}: {(min_batch,) + shape}")
    config.add_optimization_profile(profile)
    
    # 设置优化策略
    config.set_flag(trt.BuilderFlag.TF32)  # 启用TF32
    config.set_flag(trt.BuilderFlag.SPARSE_WEIGHTS)  # 启用权重稀疏化
    config.set_flag(trt.BuilderFlag.OBEY_PRECISION_CONSTRAINTS)  # 遵守精度约束
        
    # 构建引擎
    logger.info("Building TensorRT engine...")
    serialized_engine = builder.build_serialized_network(network, config)
    if serialized_engine is None:
        raise RuntimeError("Failed to build TensorRT engine")
        
    # 保存引擎
    with open(trt_path, 'wb') as f:
        f.write(serialized_engine)
    logger.info(f"TensorRT model saved to {trt_path}")
    
    # 验证引擎
    runtime = trt.Runtime(Logger)
    engine = runtime.deserialize_cuda_engine(serialized_engine)
    if engine is None:
        raise RuntimeError("Failed to deserialize TensorRT engine")
    
    # 打印引擎信息
    logger.info("Engine information:")
    for i in range(engine.num_io_tensors):
        name = engine.get_tensor_name(i)
        mode = "Input" if engine.get_tensor_mode(name) == trt.TensorIOMode.INPUT else "Output"
        shape = engine.get_tensor_shape(name)
        logger.info(f"{mode} {i}: {name}, Shape: {shape}")
    
    logger.info("TensorRT engine verification successful")

def load_trt_model(trt_path, max_batch_to_allocate_for=8):
    """加载TensorRT模型
    
    Args:
        trt_path: TensorRT模型路径
        max_batch_to_allocate_for: 用于内存分配的最大批处理大小。
                                   引擎构建时也应考虑此最大值。
    """
    import tensorrt as trt
    import pycuda.driver as cuda
    import numpy as np

    logger.info(f"Loading TRT model from {trt_path} for max batch to allocate up to: {max_batch_to_allocate_for}")

    G_LOGGER = trt.Logger(trt.Logger.WARNING)
    runtime = trt.Runtime(G_LOGGER)
    
    with open(trt_path, 'rb') as f:
        engine = runtime.deserialize_cuda_engine(f.read())
        
    if not engine:
        raise RuntimeError("Failed to deserialize CUDA engine from file.")

    trt_execution_ctx = engine.create_execution_context()
    if not trt_execution_ctx:
        raise RuntimeError("Failed to create TensorRT execution context from engine.")

    trt_inputs, trt_outputs, trt_bindings = [], [], []
    input_network_shapes, output_network_shapes = [], []

    active_profile_idx = 0
    if engine.num_optimization_profiles > 0:
        if trt_execution_ctx.active_optimization_profile < 0:
            trt_execution_ctx.active_optimization_profile = active_profile_idx
        else:
            active_profile_idx = trt_execution_ctx.active_optimization_profile
        logger.info(f"Engine has {engine.num_optimization_profiles} optimization profile(s). Using profile index: {active_profile_idx}.")
    else:
        logger.info("Engine has no optimization profiles (static shapes expected).")

    can_use_profile_shapes_api = False
    if engine.num_optimization_profiles > 0:
        try:
            if engine.num_io_tensors > 0:
                _ = engine.get_profile_shape(active_profile_idx, 0) 
                can_use_profile_shapes_api = True
                logger.info("engine.get_profile_shape() API is available and test call succeeded.")
            else:
                logger.warning("Engine has profiles but no I/O tensors to test get_profile_shape; assuming API not usable if needed.")
        except AttributeError:
            logger.warning("engine.get_profile_shape() API is not available (AttributeError). Will rely on network-defined shapes and max_batch_to_allocate_for.")
        except Exception as e_test_profile:
            logger.warning(f"engine.get_profile_shape() API test call failed (Error: {e_test_profile}). Assuming not reliably usable.")

    try:
        for binding_idx in range(engine.num_io_tensors):
            tensor_name = engine.get_tensor_name(binding_idx)
            is_input = engine.get_tensor_mode(tensor_name) == trt.TensorIOMode.INPUT
            network_defined_shape = tuple(engine.get_tensor_shape(tensor_name)) 
            dtype = trt.nptype(engine.get_tensor_dtype(tensor_name))
            
            alloc_shape_list = list(network_defined_shape)
            profile_max_dims_for_log = "N/A (API not used or not available)"

            if alloc_shape_list[0] == -1: 
                resolved_batch_dim = max_batch_to_allocate_for
                if engine.num_optimization_profiles > 0 and can_use_profile_shapes_api:
                    profile_max_dims_binding = engine.get_profile_shape(active_profile_idx, binding_idx)[2] 
                    profile_max_dims_for_log = str(profile_max_dims_binding)
                    profile_max_batch_for_binding = profile_max_dims_binding[0]
                    if resolved_batch_dim > profile_max_batch_for_binding:
                        logger.warning(f"Requested max_batch_for_allocation ({resolved_batch_dim}) for '{tensor_name}' "
                                         f"exceeds profile max batch ({profile_max_batch_for_binding}). Using profile max.")
                        resolved_batch_dim = profile_max_batch_for_binding
                alloc_shape_list[0] = resolved_batch_dim
            
            for i in range(1, len(alloc_shape_list)):
                if alloc_shape_list[i] == -1: 
                    if engine.num_optimization_profiles > 0 and can_use_profile_shapes_api:
                        profile_max_dims_binding = engine.get_profile_shape(active_profile_idx, binding_idx)[2]
                        if i < len(profile_max_dims_binding):
                           alloc_shape_list[i] = profile_max_dims_binding[i]
                        else:
                            raise RuntimeError(
                                f"Tensor '{tensor_name}' dimension {i} is dynamic, but profile_max_dims {profile_max_dims_binding} doesn't provide this dimension."
                            )
                        if i==1:
                            profile_max_dims_for_log = str(profile_max_dims_binding)
                    else:
                        raise RuntimeError(
                            f"Tensor '{tensor_name}' dimension {i} is dynamic (value: -1 from get_tensor_shape), "
                            f"but its maximum size cannot be determined because get_profile_shape API is not available/usable. "
                            f"Ensure engine is built with fixed max dimensions for these dynamic axes in older TRT versions."
                        )
            
            final_alloc_shape = tuple(alloc_shape_list)
            logger.debug(f"Tensor '{tensor_name}': NetworkShape={network_defined_shape}, ProfileMaxShapeForLog={profile_max_dims_for_log}, ResolvedAllocShape={final_alloc_shape}")

            if any(d == -1 for d in final_alloc_shape):
                raise RuntimeError(f"FATAL: Failed to resolve all dynamic dimensions for allocation of tensor '{tensor_name}'. "
                                 f"NetworkShape: {network_defined_shape}, ResolvedAllocShape: {final_alloc_shape}")
            
            volume = trt.volume(final_alloc_shape)
            if volume <= 0:
                 raise RuntimeError(f"Calculated volume for tensor '{tensor_name}' is non-positive ({volume}) with alloc_shape {final_alloc_shape}. Check shape resolution.")
            
            logger.info(f"Allocating for '{tensor_name}': Shape={final_alloc_shape}, DType={dtype}, Volume={volume}")
            try:
                host_mem = cuda.pagelocked_empty(volume, dtype)
                device_mem = cuda.mem_alloc(host_mem.nbytes)
            except cuda.MemoryError as e:
                mem_mb = volume * np.dtype(dtype).itemsize / (1024**2)
                logger.error(f"CUDA MemoryError on allocating {mem_mb:.2f} MB for '{tensor_name}' (Shape: {final_alloc_shape}). Error: {e}")
                raise
            except pycuda._driver.LogicError as e:
                 logger.error(f"PyCUDA LogicError (e.g. context issue) allocating memory for '{tensor_name}' (Shape: {final_alloc_shape}). Error: {e}")
                 raise
            except Exception as e:
                logger.error(f"Generic error allocating memory for '{tensor_name}' (Shape: {final_alloc_shape}). Error: {e}")
                raise
            
            trt_bindings.append(int(device_mem))
            mem_info_dict = {
                'host': host_mem, 'device': device_mem, 
                'alloc_shape': final_alloc_shape, 'network_shape': network_defined_shape, 
                'name': tensor_name, 'dtype': dtype
            }
            if is_input:
                trt_inputs.append(mem_info_dict)
                input_network_shapes.append(network_defined_shape)
            else:
                trt_outputs.append(mem_info_dict)
                output_network_shapes.append(network_defined_shape)
            
        logger.info("TensorRT model loaded and memory allocated successfully for all bindings.")
        return trt_execution_ctx, trt_inputs, trt_outputs, trt_bindings, input_network_shapes, output_network_shapes
    except Exception as e:
        logger.error(f"Error during TRT model loading or memory allocation phase: {e}")
        for mem_dict_list in [trt_inputs, trt_outputs]:
            for mem_dict in mem_dict_list:
                if mem_dict.get('device') is not None:
                    try: 
                        mem_dict['device'].free()
                        mem_dict['device'] = None
                    except Exception as free_e:
                        logger.error(f"Error freeing device memory for '{mem_dict.get('name')}' during cleanup: {free_e}")
        if 'trt_execution_ctx' in locals() and trt_execution_ctx is not None:
             del trt_execution_ctx
        raise

def unload_trt_model(trt_execution_ctx, trt_inputs, trt_outputs, trt_bindings):
    """注销TensorRT模型，释放所有相关资源"""
    logger.info("Unloading TRT model and freeing resources...")
    try:
        for mem_group_name, mem_group in zip(["inputs", "outputs"], [trt_inputs, trt_outputs]):
            if mem_group is None: continue
            for idx, mem_dict in enumerate(mem_group):
                tensor_name = mem_dict.get('name', f'unknown_{mem_group_name}_{idx}')
                if mem_dict.get('device') is not None:
                    try:
                        mem_dict['device'].free()
                        mem_dict['device'] = None 
                    except Exception as e:
                        logger.warning(f"Error freeing device memory for tensor '{tensor_name}': {e}")
        
        if trt_bindings: 
            trt_bindings.clear()
            logger.info("Cleared TRT bindings list.")
            
        if trt_execution_ctx:
            del trt_execution_ctx 
            logger.info("TRT execution context deleted.")
        
        logger.info("TensorRT model resources unloaded.")
    except Exception as e:
        logger.error(f"Error during unload_trt_model: {e}")


def infer_trt(trt_path, source_img, ref_img, audio_feature, test_time=1):
    """使用TensorRT模型进行推理
    
    Args:
        trt_path: TensorRT模型路径
        source_img: 源图像 [B, 3, H, W]
        ref_img: 参考图像 [B, 3, H, W]
        audio_feature: 音频特征 [B, 256, 20]
        test_time: 测试次数
        
    Returns:
        输出图像 [B, 3, H, W]
    """
    import pycuda.driver as cuda
    import numpy as np
    import tensorrt as trt
    import time

    pycuda_ctx = None
    trt_execution_ctx_local = None
    trt_inputs_loaded, trt_outputs_loaded, trt_bindings_loaded = None, None, None
    output_numpy_array = None

    try:
        cuda.init()
        device = cuda.Device(0)
        pycuda_ctx = device.make_context()
        pycuda_ctx.push()
        logger.info("PyCUDA context created and pushed.")

        trt_execution_ctx_local, trt_inputs_loaded, trt_outputs_loaded, trt_bindings_loaded, _, _ = \
            load_trt_model(trt_path, max_batch_to_allocate_for=source_img.shape[0])

        input_data_map = {
            'source_img': np.ascontiguousarray(source_img.cpu().numpy()),
            'ref_img': np.ascontiguousarray(ref_img.cpu().numpy()),
            'audio_feature': np.ascontiguousarray(audio_feature.cpu().numpy())
        }
        
        for input_binding_info in trt_inputs_loaded:
            if input_binding_info['name'] not in input_data_map:
                raise ValueError(f"Engine input tensor '{input_binding_info['name']}' not found in provided data.")

        infer_cost_time = []
        
        for i in range(test_time):
            start_time = time.time()
            
            for input_binding_info in trt_inputs_loaded:
                tensor_name = input_binding_info['name']
                data_np = input_data_map[tensor_name]
                trt_execution_ctx_local.set_input_shape(tensor_name, data_np.shape)
                cuda.memcpy_htod(input_binding_info['device'], data_np)
        
            trt_execution_ctx_local.execute_v2(bindings=trt_bindings_loaded)
            
            output_binding_info = trt_outputs_loaded[0]
            output_tensor_name = output_binding_info['name']
            output_shape_from_context = trt_execution_ctx_local.get_tensor_shape(output_tensor_name)
            output_numpy_array = np.empty(output_shape_from_context, dtype=output_binding_info['dtype'])
            cuda.memcpy_dtoh(output_numpy_array, output_binding_info['device'])

            end_time = time.time()
            infer_cost_time.append(end_time - start_time)
    
        if infer_cost_time: 
            mean_cost_val = np.mean(infer_cost_time[1:]) if len(infer_cost_time) > 1 else infer_cost_time[0]
            logger.info(f"TRT model inference: {mean_cost_val*1000:.2f} ms/batch avg over {len(infer_cost_time)} run(s) (mean excludes first if >1 runs).")
        else:
            logger.info("TRT model inference was not run (test_time might be 0 or less).")
        
        return output_numpy_array
        
    except Exception as e:
        logger.error(f"Error during TRT inference: {str(e)}")
        raise
    finally:
        if trt_execution_ctx_local is not None:
            logger.info("Attempting to unload TRT model resources from infer_trt finally block...")
            unload_trt_model(trt_execution_ctx_local, trt_inputs_loaded, trt_outputs_loaded, trt_bindings_loaded)
            logger.info("TRT model resources unloaded call completed from infer_trt finally block.")
            trt_execution_ctx_local = None
            trt_inputs_loaded, trt_outputs_loaded, trt_bindings_loaded = None, None, None
        
        if pycuda_ctx is not None:
            logger.info("Attempting to pop PyCUDA context...")
            pycuda_ctx.pop()
            logger.info("PyCUDA context popped.")
            pycuda_ctx = None

def infer_pth( source_img, ref_img, audio_feature,test_time=1):
    
    digital_human_model = DigitalHumanModel(GlobalConfig.instance().blend_dynamic, GlobalConfig.instance().chaofen_before, face_blur_detect=True,half=False)
    logger.info("Starting PyTorch inference (infer_pth)...")
    infer_cos_times = []
    print("------------------------------------------------------------source_img.shape,ref_img.shape,audio_feature.shape",source_img.shape,ref_img.shape,audio_feature.shape)
    for i in range(test_time): 
        start_time =time.time()
        fake_B = digital_human_model.model.netG(source_img, ref_img, audio_feature)
        end_time = time.time()
        infer_cos_times.append(end_time-start_time)
    if len(infer_cos_times) > 0:
        mean_cost = np.array(infer_cos_times[1:]).mean() if len(infer_cos_times) > 1 else infer_cos_times[0]
        logger.info(f"torch model inference mean cost time: {mean_cost*1000} ms/{test_time}img")
    else:
        logger.info(f"torch model inference time: 0 ms/img")
    return fake_B

def diff_result(y, y_trt):
    # convert to TensorRT feeding sample data as input
    print(torch.max(y[0][:,:-1,:,:]),torch.max(y_trt[0][:,:-1,:,:]))
    print("max abs",torch.max(torch.abs(y[1] - y_trt[1])))




def convert_pth_2_trt():
    if not os.path.exists(onnx_model_path):
        logger.info(f"onnx model not found, converting to onnx model")
        convert_pth_to_onnx(onnx_model_path)
        check_onnx_model(onnx_model_path)

    if not os.path.exists(trt_model_path) and os.path.exists(onnx_model_path):
        logger.info(f"trt model not found, converting to trt model")
        convert_to_trt(onnx_model_path, trt_model_path, fp16_mode=False, min_batch=1, max_batch=8)


def diff_models():
    if not os.path.exists(trt_model_path) :
        return
    
    source_img = torch.randn(4, 3, 256, 256).cuda()
    ref_img = torch.randn(4, 3, 256, 256).cuda()
    audio_feature = torch.randn(4, 256, 20).cuda()
    
    # logger.info("Starting TensorRT inference (infer_trt)...")
    # y_trt = infer_trt(trt_model_path, source_img, ref_img, audio_feature,test_time=100)
    # logger.info("Finished TensorRT inference (infer_trt).")

    # 在PyTorch推理之前，尝试同步CUDA操作
    # logger.info("Synchronizing CUDA before PyTorch inference...")
    # torch.cuda.synchronize() 
    # logger.info("CUDA synchronized.")

    y = infer_pth(source_img, ref_img, audio_feature,test_time=100)
    logger.info("Finished PyTorch inference (infer_pth).")

    # if y is not None and y_trt is not None:
    #     print(y.shape,y_trt.shape)  
    #     diff_result(y, y_trt)
    # else:
    #     logger.error("One of the inference results (PyTorch or TensorRT) is None, skipping diff.")

def convert_pth_to_onnx_v2(onnx_path, half=False):
    """使用ONNX版本转换工具将模型转换为ONNX格式
    
    Args:
        onnx_path: ONNX模型保存路径
        half: 是否使用半精度
    """
    import onnx
    from onnx import version_converter
    import tempfile
    
    # 创建示例输入
    digital_human_model = DigitalHumanModel(GlobalConfig.instance().blend_dynamic, GlobalConfig.instance().chaofen_before, face_blur_detect=True,half=False)

    source_img = torch.randn(1, 3, 256, 256).cuda()
    ref_img = torch.randn(1, 3, 256, 256).cuda()
    audio_feature = torch.randn(1, 256, 20).cuda()

    if half:
        source_img = source_img.half()
        ref_img = ref_img.half()
        audio_feature = audio_feature.half()
    
    # 打印模型信息
    logger.info(f"Model type: {type(digital_human_model.model.netG)}")
    logger.info(f"Model forward signature: {inspect.signature(digital_human_model.model.netG.forward)}")
    
    # 打印输入信息
    logger.info(f"Input shapes:")
    logger.info(f"source_img: {source_img.shape}")
    logger.info(f"ref_img: {ref_img.shape}")
    logger.info(f"audio_feature: {audio_feature.shape}")
    
    # 首先使用torch.onnx导出基础模型
    temp_onnx_path = tempfile.mktemp(suffix='.onnx')
    logger.info(f"Exporting base ONNX model to temporary file: {temp_onnx_path}")
    
    torch.onnx.export(
        digital_human_model.model.netG,
        (source_img, ref_img, audio_feature),
        temp_onnx_path,
        input_names=['source_img', 'ref_img', 'audio_feature'],
        output_names=['output'],
        dynamic_axes={
            'source_img': {0: 'batch_size'},
            'ref_img': {0: 'batch_size'},
            'audio_feature': {0: 'batch_size'},
            'output': {0: 'batch_size'}
        },
        opset_version=11,
        do_constant_folding=False,
        verbose=True,
        training=torch.onnx.TrainingMode.EVAL,
        operator_export_type=torch.onnx.OperatorExportTypes.ONNX,
        export_params=True,
        keep_initializers_as_inputs=True
    )
    
    # 加载ONNX模型
    logger.info("Loading ONNX model for version conversion")
    model = onnx.load(temp_onnx_path)
    
    # 检查模型
    logger.info("Checking ONNX model")
    onnx.checker.check_model(model)
    
    # 转换到更高版本
    logger.info("Converting ONNX model to version 12")
    converted_model = version_converter.convert_version(model, 12)
    
    # 保存转换后的模型
    logger.info(f"Saving converted ONNX model to {onnx_path}")
    onnx.save(converted_model, onnx_path)
    
    # 验证转换后的模型
    logger.info("Verifying converted model")
    converted_model = onnx.load(onnx_path)
    onnx.checker.check_model(converted_model)
    
    # 打印模型信息
    logger.info("Model inputs:")
    for input in converted_model.graph.input:
        logger.info(f"  Name: {input.name}, Shape: {[d.dim_value for d in input.type.tensor_type.shape.dim]}")
    
    logger.info("Model outputs:")
    for output in converted_model.graph.output:
        logger.info(f"  Name: {output.name}, Shape: {[d.dim_value for d in output.type.tensor_type.shape.dim]}")
    
    # 清理临时文件
    os.remove(temp_onnx_path)
    
    # 删除digital_human_model，释放显存
    del digital_human_model
    torch.cuda.empty_cache()
    
    logger.info("ONNX model conversion completed successfully")

def convert_pth_to_onnx_v3(onnx_path, half=False):
    """使用ONNX Python API直接构建模型
    
    Args:
        onnx_path: ONNX模型保存路径
        half: 是否使用半精度
    """
    import onnx
    from onnx import helper, numpy_helper
    import numpy as np
    import tempfile
    
    # 创建示例输入
    digital_human_model = DigitalHumanModel(GlobalConfig.instance().blend_dynamic, GlobalConfig.instance().chaofen_before, face_blur_detect=True,half=False)

    source_img = torch.randn(1, 3, 256, 256).cuda()
    ref_img = torch.randn(1, 3, 256, 256).cuda()
    audio_feature = torch.randn(1, 256, 20).cuda()

    if half:
        source_img = source_img.half()
        ref_img = ref_img.half()
        audio_feature = audio_feature.half()
    
    # 打印模型信息
    logger.info(f"Model type: {type(digital_human_model.model.netG)}")
    logger.info(f"Model forward signature: {inspect.signature(digital_human_model.model.netG.forward)}")
    
    # 打印输入信息
    logger.info(f"Input shapes:")
    logger.info(f"source_img: {source_img.shape}")
    logger.info(f"ref_img: {ref_img.shape}")
    logger.info(f"audio_feature: {audio_feature.shape}")
    
    # 首先使用torch.onnx导出基础模型
    temp_onnx_path = tempfile.mktemp(suffix='.onnx')
    logger.info(f"Exporting base ONNX model to temporary file: {temp_onnx_path}")
    
    torch.onnx.export(
        digital_human_model.model.netG,
        (source_img, ref_img, audio_feature),
        temp_onnx_path,
        input_names=['source_img', 'ref_img', 'audio_feature'],
        output_names=['output'],
        dynamic_axes={
            'source_img': {0: 'batch_size'},
            'ref_img': {0: 'batch_size'},
            'audio_feature': {0: 'batch_size'},
            'output': {0: 'batch_size'}
        },
        opset_version=11,
        do_constant_folding=False,
        verbose=True,
        training=torch.onnx.TrainingMode.EVAL,
        operator_export_type=torch.onnx.OperatorExportTypes.ONNX,
        export_params=True,
        keep_initializers_as_inputs=True
    )
    
    # 加载ONNX模型
    logger.info("Loading ONNX model")
    model = onnx.load(temp_onnx_path)
    
    # 创建新的输入
    source_img_tensor = helper.make_tensor_value_info(
        'source_img',
        onnx.TensorProto.FLOAT,
        [1, 3, 256, 256]
    )
    
    ref_img_tensor = helper.make_tensor_value_info(
        'ref_img',
        onnx.TensorProto.FLOAT,
        [1, 3, 256, 256]
    )
    
    audio_feature_tensor = helper.make_tensor_value_info(
        'audio_feature',
        onnx.TensorProto.FLOAT,
        [1, 256, 20]
    )
    
    # 创建新的输出
    output_tensor = helper.make_tensor_value_info(
        'output',
        onnx.TensorProto.FLOAT,
        [1, 3, 256, 256]
    )
    
    # 创建新的图
    graph = helper.make_graph(
        model.graph.node,
        'DINetV1',
        [source_img_tensor, ref_img_tensor, audio_feature_tensor],
        [output_tensor],
        model.graph.initializer
    )
    
    # 创建新的模型
    new_model = helper.make_model(graph, producer_name='DINetV1')
    new_model.opset_import[0].version = 12
    
    # 检查模型
    logger.info("Checking ONNX model")
    onnx.checker.check_model(new_model)
    
    # 保存模型
    logger.info(f"Saving ONNX model to {onnx_path}")
    onnx.save(new_model, onnx_path)
    
    # 验证保存的模型
    logger.info("Verifying saved model")
    saved_model = onnx.load(onnx_path)
    onnx.checker.check_model(saved_model)
    
    # 打印模型信息
    logger.info("Model inputs:")
    for input in saved_model.graph.input:
        logger.info(f"  Name: {input.name}, Shape: {[d.dim_value for d in input.type.tensor_type.shape.dim]}")
    
    logger.info("Model outputs:")
    for output in saved_model.graph.output:
        logger.info(f"  Name: {output.name}, Shape: {[d.dim_value for d in output.type.tensor_type.shape.dim]}")
    
    # 清理临时文件
    os.remove(temp_onnx_path)
    
    # 删除digital_human_model，释放显存
    del digital_human_model
    torch.cuda.empty_cache()
    
    logger.info("ONNX model conversion completed successfully")

def convert_pth_to_onnx_v4(onnx_path, half=False):
    """使用ONNX Runtime优化器修复和优化模型
    
    Args:
        onnx_path: ONNX模型保存路径
        half: 是否使用半精度
    """
    import onnx
    from onnx import helper, numpy_helper
    import onnxruntime as ort
    import numpy as np
    import tempfile
    
    # 创建示例输入
    digital_human_model = DigitalHumanModel(GlobalConfig.instance().blend_dynamic, GlobalConfig.instance().chaofen_before, face_blur_detect=True,half=False)

    source_img = torch.randn(1, 3, 256, 256).cuda()
    ref_img = torch.randn(1, 3, 256, 256).cuda()
    audio_feature = torch.randn(1, 256, 20).cuda()

    if half:
        source_img = source_img.half()
        ref_img = ref_img.half()
        audio_feature = audio_feature.half()
    
    # 打印模型信息
    logger.info(f"Model type: {type(digital_human_model.model.netG)}")
    logger.info(f"Model forward signature: {inspect.signature(digital_human_model.model.netG.forward)}")
    
    # 打印输入信息
    logger.info(f"Input shapes:")
    logger.info(f"source_img: {source_img.shape}")
    logger.info(f"ref_img: {ref_img.shape}")
    logger.info(f"audio_feature: {audio_feature.shape}")
    
    # 首先使用torch.onnx导出基础模型
    temp_onnx_path = tempfile.mktemp(suffix='.onnx')
    logger.info(f"Exporting base ONNX model to temporary file: {temp_onnx_path}")
    
    # 使用更高的opset版本
    torch.onnx.export(
        digital_human_model.model.netG,
        (source_img, ref_img, audio_feature),
        temp_onnx_path,
        input_names=['source_img', 'ref_img', 'audio_feature'],
        output_names=['output'],
        dynamic_axes={
            'source_img': {0: 'batch_size'},
            'ref_img': {0: 'batch_size'},
            'audio_feature': {0: 'batch_size'},
            'output': {0: 'batch_size'}
        },
        opset_version=13,  # 使用更高的opset版本
        do_constant_folding=False,
        verbose=True,
        training=torch.onnx.TrainingMode.EVAL,
        operator_export_type=torch.onnx.OperatorExportTypes.ONNX,
        export_params=True,
        keep_initializers_as_inputs=True
    )
    
    # 加载ONNX模型
    logger.info("Loading ONNX model")
    model = onnx.load(temp_onnx_path)
    
    # 使用ONNX Runtime优化模型
    logger.info("Optimizing ONNX model with ONNX Runtime")
    sess_options = ort.SessionOptions()
    sess_options.graph_optimization_level = ort.GraphOptimizationLevel.ORT_ENABLE_ALL
    sess_options.optimized_model_filepath = onnx_path
    
    # 创建ONNX Runtime会话
    session = ort.InferenceSession(temp_onnx_path, sess_options)
    
    # 确保所有输入都存在
    input_names = set(['source_img', 'ref_img', 'audio_feature'])
    existing_inputs = set([input.name for input in model.graph.input])
    
    if input_names != existing_inputs:
        logger.warning(f"Missing inputs: {input_names - existing_inputs}")
        # 添加缺失的输入
        for input_name in input_names - existing_inputs:
            if input_name == 'audio_feature':
                tensor = helper.make_tensor_value_info(
                    input_name,
                    onnx.TensorProto.FLOAT,
                    [1, 256, 20]
                )
            else:
                tensor = helper.make_tensor_value_info(
                    input_name,
                    onnx.TensorProto.FLOAT,
                    [1, 3, 256, 256]
                )
            model.graph.input.append(tensor)
    
    # 检查并修复模型
    logger.info("Checking and fixing model")
    onnx.checker.check_model(model)
    
    # 保存优化后的模型
    logger.info(f"Saving optimized ONNX model to {onnx_path}")
    onnx.save(model, onnx_path)
    
    # 验证保存的模型
    logger.info("Verifying saved model")
    saved_model = onnx.load(onnx_path)
    onnx.checker.check_model(saved_model)
    
    # 打印模型信息
    logger.info("Model inputs:")
    for input in saved_model.graph.input:
        logger.info(f"  Name: {input.name}, Shape: {[d.dim_value for d in input.type.tensor_type.shape.dim]}")
    
    logger.info("Model outputs:")
    for output in saved_model.graph.output:
        logger.info(f"  Name: {output.name}, Shape: {[d.dim_value for d in output.type.tensor_type.shape.dim]}")
    
    # 清理临时文件
    os.remove(temp_onnx_path)
    
    # 删除digital_human_model，释放显存
    del digital_human_model
    torch.cuda.empty_cache()
    
    logger.info("ONNX model conversion completed successfully")

def convert_pth_to_onnx_v5(onnx_path, half=False):
    """使用ONNX helper直接构建模型，确保所有输入都被正确处理
    
    Args:
        onnx_path: ONNX模型保存路径
        half: 是否使用半精度
    """
    import onnx
    from onnx import helper, numpy_helper
    import numpy as np
    import tempfile
    
    # 创建示例输入
    digital_human_model = DigitalHumanModel(GlobalConfig.instance().blend_dynamic, GlobalConfig.instance().chaofen_before, face_blur_detect=True,half=False)

    source_img = torch.randn(1, 3, 256, 256).cuda()
    ref_img = torch.randn(1, 3, 256, 256).cuda()
    audio_feature = torch.randn(1, 256, 20).cuda()

    if half:
        source_img = source_img.half()
        ref_img = ref_img.half()
        audio_feature = audio_feature.half()
    
    # 打印模型信息
    logger.info(f"Model type: {type(digital_human_model.model.netG)}")
    logger.info(f"Model forward signature: {inspect.signature(digital_human_model.model.netG.forward)}")
    
    # 打印输入信息
    logger.info(f"Input shapes:")
    logger.info(f"source_img: {source_img.shape}")
    logger.info(f"ref_img: {ref_img.shape}")
    logger.info(f"audio_feature: {audio_feature.shape}")
    
    # 首先使用torch.onnx导出基础模型
    temp_onnx_path = tempfile.mktemp(suffix='.onnx')
    logger.info(f"Exporting base ONNX model to temporary file: {temp_onnx_path}")
    
    # 使用更高的opset版本
    torch.onnx.export(
        digital_human_model.model.netG,
        (source_img, ref_img, audio_feature),
        temp_onnx_path,
        input_names=['source_img', 'ref_img', 'audio_feature'],
        output_names=['output'],
        dynamic_axes={
            'source_img': {0: 'batch_size'},
            'ref_img': {0: 'batch_size'},
            'audio_feature': {0: 'batch_size'},
            'output': {0: 'batch_size'}
        },
        opset_version=13,  # 使用更高的opset版本
        do_constant_folding=False,
        verbose=True,
        training=torch.onnx.TrainingMode.EVAL,
        operator_export_type=torch.onnx.OperatorExportTypes.ONNX,
        export_params=True,
        keep_initializers_as_inputs=True
    )
    
    # 加载ONNX模型
    logger.info("Loading ONNX model")
    model = onnx.load(temp_onnx_path)
    
    # 创建新的输入
    source_img_tensor = helper.make_tensor_value_info(
        'source_img',
        onnx.TensorProto.FLOAT,
        [1, 3, 256, 256]
    )
    
    ref_img_tensor = helper.make_tensor_value_info(
        'ref_img',
        onnx.TensorProto.FLOAT,
        [1, 3, 256, 256]
    )
    
    audio_feature_tensor = helper.make_tensor_value_info(
        'audio_feature',
        onnx.TensorProto.FLOAT,
        [1, 256, 20]
    )
    
    # 创建新的输出
    output_tensor = helper.make_tensor_value_info(
        'output',
        onnx.TensorProto.FLOAT,
        [1, 3, 256, 256]
    )
    
    # 创建新的图
    graph = helper.make_graph(
        model.graph.node,
        'DINetV1',
        [source_img_tensor, ref_img_tensor, audio_feature_tensor],
        [output_tensor],
        model.graph.initializer
    )
    
    # 创建新的模型
    new_model = helper.make_model(graph, producer_name='DINetV1')
    new_model.opset_import[0].version = 13
    
    # 检查模型
    logger.info("Checking ONNX model")
    onnx.checker.check_model(new_model)
    
    # 保存模型
    logger.info(f"Saving ONNX model to {onnx_path}")
    onnx.save(new_model, onnx_path)
    
    # 验证保存的模型
    logger.info("Verifying saved model")
    saved_model = onnx.load(onnx_path)
    onnx.checker.check_model(saved_model)
    
    # 打印模型信息
    logger.info("Model inputs:")
    for input in saved_model.graph.input:
        logger.info(f"  Name: {input.name}, Shape: {[d.dim_value for d in input.type.tensor_type.shape.dim]}")
    
    logger.info("Model outputs:")
    for output in saved_model.graph.output:
        logger.info(f"  Name: {output.name}, Shape: {[d.dim_value for d in output.type.tensor_type.shape.dim]}")
    
    # 清理临时文件
    os.remove(temp_onnx_path)
    
    # 删除digital_human_model，释放显存
    del digital_human_model
    torch.cuda.empty_cache()
    
    logger.info("ONNX model conversion completed successfully")

def convert_pth_to_onnx_v6(onnx_path, half=False):
    """将audio_feature从3D扩展到4D，使用2D卷积处理
    
    Args:
        onnx_path: ONNX模型保存路径
        half: 是否使用半精度
    """
    import onnx
    from onnx import helper, numpy_helper
    import numpy as np
    import tempfile
    
    # 创建示例输入
    digital_human_model = DigitalHumanModel(GlobalConfig.instance().blend_dynamic, GlobalConfig.instance().chaofen_before, face_blur_detect=True,half=False)

    source_img = torch.randn(1, 3, 256, 256).cuda()
    ref_img = torch.randn(1, 3, 256, 256).cuda()
    # 将audio_feature从[B, 256, 20]扩展到[B, 256, 20, 1]
    audio_feature = torch.randn(1, 256, 20, 1).cuda()

    if half:
        source_img = source_img.half()
        ref_img = ref_img.half()
        audio_feature = audio_feature.half()
    
    # 打印模型信息
    logger.info(f"Model type: {type(digital_human_model.model.netG)}")
    logger.info(f"Model forward signature: {inspect.signature(digital_human_model.model.netG.forward)}")
    
    # 打印输入信息
    logger.info(f"Input shapes:")
    logger.info(f"source_img: {source_img.shape}")
    logger.info(f"ref_img: {ref_img.shape}")
    logger.info(f"audio_feature: {audio_feature.shape}")
    
    # 首先使用torch.onnx导出基础模型
    temp_onnx_path = tempfile.mktemp(suffix='.onnx')
    logger.info(f"Exporting base ONNX model to temporary file: {temp_onnx_path}")
    
    # 使用更高的opset版本
    torch.onnx.export(
        digital_human_model.model.netG,
        (source_img, ref_img, audio_feature),
        temp_onnx_path,
        input_names=['source_img', 'ref_img', 'audio_feature'],
        output_names=['output'],
        dynamic_axes={
            'source_img': {0: 'batch_size'},
            'ref_img': {0: 'batch_size'},
            'audio_feature': {0: 'batch_size'},
            'output': {0: 'batch_size'}
        },
        opset_version=13,  # 使用更高的opset版本
        do_constant_folding=False,
        verbose=True,
        training=torch.onnx.TrainingMode.EVAL,
        operator_export_type=torch.onnx.OperatorExportTypes.ONNX,
        export_params=True,
        keep_initializers_as_inputs=True
    )
    
    # 加载ONNX模型
    logger.info("Loading ONNX model")
    model = onnx.load(temp_onnx_path)
    
    # 创建新的输入
    source_img_tensor = helper.make_tensor_value_info(
        'source_img',
        onnx.TensorProto.FLOAT,
        [1, 3, 256, 256]
    )
    
    ref_img_tensor = helper.make_tensor_value_info(
        'ref_img',
        onnx.TensorProto.FLOAT,
        [1, 3, 256, 256]
    )
    
    audio_feature_tensor = helper.make_tensor_value_info(
        'audio_feature',
        onnx.TensorProto.FLOAT,
        [1, 256, 20, 1]  # 修改为4D
    )
    
    # 创建新的输出
    output_tensor = helper.make_tensor_value_info(
        'output',
        onnx.TensorProto.FLOAT,
        [1, 3, 256, 256]
    )
    
    # 创建新的图
    graph = helper.make_graph(
        model.graph.node,
        'DINetV1',
        [source_img_tensor, ref_img_tensor, audio_feature_tensor],
        [output_tensor],
        model.graph.initializer
    )
    
    # 创建新的模型
    new_model = helper.make_model(graph, producer_name='DINetV1')
    new_model.opset_import[0].version = 13
    
    # 检查模型
    logger.info("Checking ONNX model")
    onnx.checker.check_model(new_model)
    
    # 保存模型
    logger.info(f"Saving ONNX model to {onnx_path}")
    onnx.save(new_model, onnx_path)
    
    # 验证保存的模型
    logger.info("Verifying saved model")
    saved_model = onnx.load(onnx_path)
    onnx.checker.check_model(saved_model)
    
    # 打印模型信息
    logger.info("Model inputs:")
    for input in saved_model.graph.input:
        logger.info(f"  Name: {input.name}, Shape: {[d.dim_value for d in input.type.tensor_type.shape.dim]}")
    
    logger.info("Model outputs:")
    for output in saved_model.graph.output:
        logger.info(f"  Name: {output.name}, Shape: {[d.dim_value for d in output.type.tensor_type.shape.dim]}")
    
    # 清理临时文件
    os.remove(temp_onnx_path)
    
    # 删除digital_human_model，释放显存
    del digital_human_model
    torch.cuda.empty_cache()
    
    logger.info("ONNX model conversion completed successfully")

# 修改主函数，使用新的转换函数
if __name__ == "__main__":
    # import tensorrt as trt
    # print("tensorrt version:", trt.__version__)
    
    # # 强制重新生成TensorRT模型
    # if os.path.exists(trt_model_path):
    #     logger.info(f"Removing existing TensorRT model: {trt_model_path}")
    #     os.remove(trt_model_path)
    
    # # 确保ONNX模型存在
    # if os.path.exists(onnx_model_path):
    #     os.remove(onnx_model_path)
    # logger.info(f"Converting PyTorch model to ONNX: {onnx_model_path}")
    # convert_pth_to_onnx(onnx_model_path)  # 使用新的转换函数
    # check_onnx_model(onnx_model_path)
    
    # # 生成新的TensorRT模型
    # logger.info(f"Converting ONNX model to TensorRT: {trt_model_path}")
    # convert_to_trt(onnx_model_path, trt_model_path, fp16_mode=False, min_batch=1, max_batch=8)
    
    # 运行模型比较
    diff_models()
    
"""
目前版本能转，但是没有验证过效果
"""