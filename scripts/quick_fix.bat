@echo off
echo ========================================
echo WebRTC 快速修复脚本
echo ========================================
echo.

echo 正在检查并修复常见问题...
echo.

REM 检查端口占用
echo 检查端口8080是否被占用...
netstat -an | findstr :8080 >nul 2>&1
if not errorlevel 1 (
    echo ⚠️  端口8080被占用，正在尝试释放...
    for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8080') do (
        echo 终止进程: %%a
        taskkill /f /pid %%a >nul 2>&1
    )
    echo ✅ 端口已释放
) else (
    echo ✅ 端口8080可用
)

echo.

REM 检查Python包
echo 检查Python包...
python -c "import aiortc" >nul 2>&1
if errorlevel 1 (
    echo ❌ aiortc未安装，正在安装...
    pip install aiortc
)

python -c "import aiohttp" >nul 2>&1
if errorlevel 1 (
    echo ❌ aiohttp未安装，正在安装...
    pip install aiohttp
)

python -c "import cv2" >nul 2>&1
if errorlevel 1 (
    echo ❌ opencv-python未安装，正在安装...
    pip install opencv-python
)

python -c "import pyaudio" >nul 2>&1
if errorlevel 1 (
    echo ❌ pyaudio未安装，正在安装...
    pip install pyaudio
)

echo ✅ 所有包检查完成
echo.

REM 设置环境变量
set PYTHONPATH=%PYTHONPATH%;%CD%
set PYTHONUNBUFFERED=1

echo 修复完成！现在可以运行测试了。
echo.
echo 建议的测试顺序:
echo 1. 先运行简单连接测试: python scripts/simple_webrtc_test.py
echo 2. 如果成功，再运行完整播放器: python scripts/webrtc_player.py
echo.
pause 