#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WebRTC连接测试脚本
用于测试WebRTC信令服务器是否正常工作
"""

import asyncio
import aiohttp
import json
import logging
from aiortc import RTCPeerConnection, RTCSessionDescription

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

async def test_webrtc_connection(signaling_url: str):
    """测试WebRTC连接"""
    logger.info(f"开始测试WebRTC连接: {signaling_url}")
    
    # 创建对等连接
    pc = RTCPeerConnection()
    
    # 设置事件处理器
    @pc.on("connectionstatechange")
    async def on_connectionstatechange():
        logger.info(f"连接状态: {pc.connectionState}")
    
    @pc.on("iceconnectionstatechange")
    async def on_iceconnectionstatechange():
        logger.info(f"ICE连接状态: {pc.iceConnectionState}")
    
    try:
        # 创建offer
        pc.addTransceiver("video", direction="recvonly")
        pc.addTransceiver("audio", direction="recvonly")
        offer = await pc.createOffer()
        await pc.setLocalDescription(offer)
        
        logger.info("发送offer到信令服务器...")
        
        # 发送offer到信令服务器
        async with aiohttp.ClientSession() as session:
            async with session.post(
                signaling_url,
                json={"sdp": pc.localDescription.sdp, "type": pc.localDescription.type},
                timeout=aiohttp.ClientTimeout(total=10)
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    logger.info("成功接收到answer")
                    logger.info(f"Answer类型: {data.get('type')}")
                    
                    # 设置远程描述
                    answer = RTCSessionDescription(sdp=data["sdp"], type=data["type"])
                    await pc.setRemoteDescription(answer)
                    logger.info("成功设置远程描述")
                    
                    # 等待连接建立
                    await asyncio.sleep(5)
                    
                    logger.info("✅ WebRTC连接测试成功!")
                    return True
                else:
                    error_text = await response.text()
                    logger.error(f"❌ 信令服务器响应失败: {response.status}")
                    logger.error(f"错误信息: {error_text}")
                    return False
                    
    except asyncio.TimeoutError:
        logger.error("❌ 连接超时")
        return False
    except Exception as e:
        logger.error(f"❌ 连接测试失败: {e}")
        return False
    finally:
        await pc.close()

async def test_signaling_server(signaling_url: str):
    """测试信令服务器是否可访问"""
    logger.info(f"测试信令服务器可访问性: {signaling_url}")
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.get(signaling_url.replace('/offer', '/health'), timeout=5) as response:
                if response.status == 200:
                    logger.info("✅ 信令服务器可访问")
                    return True
                else:
                    logger.warning(f"⚠️ 信令服务器响应异常: {response.status}")
                    return False
    except Exception as e:
        logger.error(f"❌ 无法访问信令服务器: {e}")
        return False

async def main():
    """主函数"""
    signaling_url = "http://127.0.0.1:8080/offer"
    
    logger.info("=" * 50)
    logger.info("WebRTC连接测试")
    logger.info("=" * 50)
    
    # 测试信令服务器可访问性
    server_ok = await test_signaling_server(signaling_url)
    if not server_ok:
        logger.error("信令服务器不可访问，请检查服务器是否启动")
        return
    
    # 测试WebRTC连接
    connection_ok = await test_webrtc_connection(signaling_url)
    
    if connection_ok:
        logger.info("🎉 所有测试通过!")
    else:
        logger.error("💥 连接测试失败")

if __name__ == "__main__":
    asyncio.run(main()) 