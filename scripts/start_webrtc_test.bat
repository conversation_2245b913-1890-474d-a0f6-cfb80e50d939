@echo off
echo ========================================
echo WebRTC 播放器测试启动脚本
echo ========================================
echo.

REM 检查Python环境
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 未找到Python环境
    pause
    exit /b 1
)

echo ✅ Python环境检查通过
echo.

REM 检查必要的包
echo 检查必要的Python包...
python -c "import aiortc, aiohttp, cv2, pyaudio" >nul 2>&1
if errorlevel 1 (
    echo ❌ 错误: 缺少必要的Python包
    echo 请运行: pip install aiortc aiohttp opencv-python pyaudio
    pause
    exit /b 1
)

echo ✅ 所有必要的包已安装
echo.

REM 设置环境变量
set PYTHONPATH=%PYTHONPATH%;%CD%
set PYTHONUNBUFFERED=1

echo 选择测试模式:
echo 1. 简单连接测试
echo 2. 完整播放器测试
echo 3. 网络诊断
echo.
set /p choice="请输入选择 (1-3): "

if "%choice%"=="1" (
    echo.
    echo 运行简单连接测试...
    python scripts/simple_webrtc_test.py
) else if "%choice%"=="2" (
    echo.
    echo 启动WebRTC播放器...
    echo 提示: 按 'q' 键退出播放器
    echo 提示: 按 's' 键保存当前帧
    echo.
    python scripts/webrtc_player.py
) else if "%choice%"=="3" (
    echo.
    echo 运行网络诊断...
    python scripts/webrtc_diagnostics.py
) else (
    echo ❌ 无效选择
    pause
    exit /b 1
)

echo.
echo 测试完成
pause 