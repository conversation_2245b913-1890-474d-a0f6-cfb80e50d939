#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进的WebRTC连接状态检测
验证客户端断开时服务器能够及时检测到
"""

import asyncio
import time
import numpy as np
import cv2
from loguru import logger
from service.webrtc import WebRTCStreamer

class ConnectionTestClient:
    """模拟WebRTC客户端连接和断开"""
    
    def __init__(self, server_url="http://localhost:8080/offer"):
        self.server_url = server_url
        self.is_connected = False
        
    async def connect(self):
        """模拟客户端连接"""
        try:
            import aiohttp
            from aiortc import RTCPeerConnection, RTCSessionDescription
            
            self.pc = RTCPeerConnection()
            
            # 创建offer
            offer = await self.pc.createOffer()
            await self.pc.setLocalDescription(offer)
            
            # 发送offer到服务器
            async with aiohttp.ClientSession() as session:
                async with session.post(self.server_url, json={
                    "sdp": self.pc.localDescription.sdp,
                    "type": self.pc.localDescription.type
                }) as response:
                    if response.status == 200:
                        answer_data = await response.json()
                        answer = RTCSessionDescription(
                            sdp=answer_data["sdp"],
                            type=answer_data["type"]
                        )
                        await self.pc.setRemoteDescription(answer)
                        self.is_connected = True
                        logger.info("【测试客户端】连接成功")
                        return True
                    else:
                        logger.error(f"【测试客户端】连接失败: {response.status}")
                        return False
                        
        except Exception as e:
            logger.error(f"【测试客户端】连接异常: {e}")
            return False
    
    async def disconnect(self, method="normal"):
        """模拟客户端断开"""
        if not self.is_connected:
            return
            
        try:
            if method == "normal":
                # 正常关闭
                await self.pc.close()
                logger.info("【测试客户端】正常断开连接")
            elif method == "abrupt":
                # 突然断开（不发送关闭信号）
                self.pc = None
                logger.info("【测试客户端】突然断开连接")
            
            self.is_connected = False
            
        except Exception as e:
            logger.error(f"【测试客户端】断开连接异常: {e}")

async def test_connection_detection():
    """测试连接状态检测"""
    logger.info("=== 开始连接状态检测测试 ===")
    
    # 创建推流器
    streamer = WebRTCStreamer(
        signaling_url="0.0.0.0:8080",
        audio_sample_rate=16000,
        video_fps=25
    )
    
    try:
        # 启动推流器
        logger.info("启动推流器...")
        success = streamer.start()
        assert success, "推流器启动失败"
        
        # 等待推流器完全启动
        await asyncio.sleep(2)
        
        # 检查初始状态
        status = streamer.get_status()
        logger.info(f"初始状态: is_connected={status['is_connected']}, connections={status['connection_count']}")
        assert not status['is_connected'], "初始状态应该是未连接"
        
        # 测试1: 模拟客户端连接
        logger.info("\n--- 测试1: 客户端连接 ---")
        client = ConnectionTestClient()
        
        # 连接前状态
        logger.info(f"连接前: is_connected={streamer.is_connected}")
        
        # 尝试连接（这里只是模拟，实际需要真实的WebRTC客户端）
        # 我们直接测试推流器的连接检测逻辑
        
        # 发送一些测试帧
        logger.info("发送测试帧...")
        frame = np.zeros((720, 1280, 3), dtype=np.uint8)
        frame[:, :, 0] = 255  # 红色帧
        audio_data = np.sin(2 * np.pi * 440 * np.linspace(0, 0.02, 320)).astype(np.float32)
        
        # 发送帧并观察连接状态
        for i in range(10):
            streamer.write_frame(frame, audio_data)
            status = streamer.get_status()
            logger.info(f"帧 {i}: is_connected={status['is_connected']}, connections={status['connection_count']}")
            await asyncio.sleep(0.1)
        
        # 测试2: 连接超时检测
        logger.info("\n--- 测试2: 连接超时检测 ---")
        
        # 模拟有连接但长时间无活动
        if hasattr(streamer, '_last_frame_time'):
            # 设置一个假的连接活动时间（很久以前）
            fake_pc_id = 12345
            streamer._last_frame_time[fake_pc_id] = time.time() - 60  # 60秒前
            
            # 手动触发连接健康检查
            streamer._check_connection_health()
            
            logger.info(f"超时检测后: is_connected={streamer.is_connected}")
        
        # 测试3: 定期检查任务
        logger.info("\n--- 测试3: 定期检查任务 ---")
        
        # 等待定期检查任务运行
        logger.info("等待定期检查任务运行...")
        await asyncio.sleep(6)  # 等待超过检查间隔
        
        status = streamer.get_status()
        logger.info(f"定期检查后: is_connected={status['is_connected']}, connections={status['connection_count']}")
        
        logger.info("✅ 连接状态检测测试完成")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
    finally:
        # 停止推流器
        logger.info("停止推流器...")
        streamer.stop()

async def test_connection_state_transitions():
    """测试连接状态转换"""
    logger.info("=== 测试连接状态转换 ===")
    
    streamer = WebRTCStreamer("0.0.0.0:8081")
    
    try:
        streamer.start()
        await asyncio.sleep(1)
        
        # 测试状态转换逻辑
        logger.info("测试连接状态转换...")
        
        # 初始状态
        assert not streamer.is_connected, "初始应该未连接"
        logger.info("✅ 初始状态正确")
        
        # 模拟连接建立
        # 这里我们直接调用内部方法来测试状态转换逻辑
        old_connected = streamer.is_connected
        
        # 测试状态更新方法
        if hasattr(streamer, '_update_connection_status'):
            await streamer._update_connection_status()
            logger.info(f"状态更新后: {streamer.is_connected}")
        
        # 测试缓冲区清理
        if hasattr(streamer, '_clear_buffers_on_disconnect'):
            streamer._clear_buffers_on_disconnect()
            logger.info("✅ 缓冲区清理测试完成")
        
        logger.info("✅ 连接状态转换测试完成")
        
    finally:
        streamer.stop()

def test_connection_health_check():
    """测试连接健康检查"""
    logger.info("=== 测试连接健康检查 ===")
    
    streamer = WebRTCStreamer("0.0.0.0:8082")
    
    try:
        # 测试健康检查参数
        assert hasattr(streamer, '_connection_check_interval'), "应该有检查间隔参数"
        assert hasattr(streamer, '_connection_timeout'), "应该有连接超时参数"
        assert hasattr(streamer, '_last_frame_time'), "应该有活动时间记录"
        
        logger.info(f"检查间隔: {streamer._connection_check_interval}s")
        logger.info(f"连接超时: {streamer._connection_timeout}s")
        
        # 测试健康检查方法
        if hasattr(streamer, '_check_connection_health'):
            streamer._check_connection_health()
            logger.info("✅ 健康检查方法可用")
        
        logger.info("✅ 连接健康检查测试完成")
        
    except Exception as e:
        logger.error(f"健康检查测试失败: {e}")

if __name__ == "__main__":
    logger.info("开始WebRTC连接状态检测测试...")
    
    try:
        # 基础测试
        test_connection_health_check()
        
        # 异步测试
        asyncio.run(test_connection_state_transitions())
        asyncio.run(test_connection_detection())
        
        logger.info("🎉 所有连接状态检测测试完成！")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
