#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试WebRTC连接状态检测改进
验证所有新增的连接检测功能
"""

import asyncio
import time
import numpy as np
from loguru import logger
from service.webrtc import WebRTCStreamer

async def test_connection_detection_improvements():
    """测试连接检测改进"""
    logger.info("=== 测试WebRTC连接状态检测改进 ===")
    
    # 创建推流器
    streamer = WebRTCStreamer(
        signaling_url="0.0.0.0:8080",
        audio_sample_rate=16000,
        video_fps=25
    )
    
    try:
        # 启动推流器
        logger.info("启动推流器...")
        success = streamer.start()
        assert success, "推流器启动失败"
        
        # 等待推流器完全启动
        await asyncio.sleep(2)
        
        # 测试1: 检查新增的属性和方法
        logger.info("\n--- 测试1: 检查新增功能 ---")
        
        # 检查新增的属性
        required_attrs = [
            '_connection_check_interval',
            '_connection_timeout', 
            '_last_frame_time',
            '_connection_stats',
            '_heartbeat_interval'
        ]
        
        for attr in required_attrs:
            assert hasattr(streamer, attr), f"缺少属性: {attr}"
            logger.info(f"✅ 属性 {attr}: {getattr(streamer, attr)}")
        
        # 检查新增的方法
        required_methods = [
            '_check_connection_health',
            '_is_connection_truly_active',
            'get_detailed_connection_status',
            'force_connection_check',
            '_clear_buffers_on_disconnect'
        ]
        
        for method in required_methods:
            assert hasattr(streamer, method), f"缺少方法: {method}"
            logger.info(f"✅ 方法 {method} 可用")
        
        # 测试2: 详细连接状态
        logger.info("\n--- 测试2: 详细连接状态 ---")
        
        detailed_status = streamer.get_detailed_connection_status()
        logger.info(f"详细连接状态: {detailed_status}")
        
        assert 'total_connections' in detailed_status
        assert 'is_connected' in detailed_status
        assert 'connections' in detailed_status
        assert 'check_interval' in detailed_status
        assert 'timeout_threshold' in detailed_status
        
        logger.info("✅ 详细连接状态功能正常")
        
        # 测试3: 强制连接检查
        logger.info("\n--- 测试3: 强制连接检查 ---")
        
        before_check = time.time()
        streamer.force_connection_check()
        after_check = time.time()
        
        logger.info(f"强制检查耗时: {after_check - before_check:.3f}s")
        logger.info("✅ 强制连接检查功能正常")
        
        # 测试4: 模拟帧处理和活动时间记录
        logger.info("\n--- 测试4: 帧处理和活动时间记录 ---")
        
        # 生成测试帧
        frame = np.zeros((720, 1280, 3), dtype=np.uint8)
        frame[:, :, 1] = 255  # 绿色帧
        audio_data = np.sin(2 * np.pi * 440 * np.linspace(0, 0.02, 320)).astype(np.float32)
        
        # 发送帧并检查活动时间记录
        for i in range(5):
            streamer.write_frame(frame, audio_data)
            
            # 检查活动时间是否被记录
            if streamer._last_frame_time:
                logger.info(f"帧 {i}: 活动时间记录数量: {len(streamer._last_frame_time)}")
            
            await asyncio.sleep(0.1)
        
        logger.info("✅ 帧处理和活动时间记录功能正常")
        
        # 测试5: 连接超时模拟
        logger.info("\n--- 测试5: 连接超时模拟 ---")
        
        # 模拟一个过期的连接
        fake_pc_id = 99999
        old_time = time.time() - streamer._connection_timeout - 1  # 超过超时时间
        streamer._last_frame_time[fake_pc_id] = old_time
        
        logger.info(f"添加过期连接 {fake_pc_id}, 活动时间: {old_time}")
        
        # 执行健康检查
        streamer._check_connection_health()
        
        # 检查过期连接是否被清理
        if fake_pc_id not in streamer._last_frame_time:
            logger.info("✅ 过期连接已被清理")
        else:
            logger.warning("⚠️ 过期连接未被清理")
        
        # 测试6: 缓冲区清理
        logger.info("\n--- 测试6: 缓冲区清理 ---")
        
        # 添加一些测试数据到缓冲区
        if hasattr(streamer, '_audio_buffer'):
            streamer._audio_buffer.extend([0.1, 0.2, 0.3])
            logger.info(f"添加测试数据到音频缓冲区: {len(streamer._audio_buffer)}")
        
        # 执行缓冲区清理
        streamer._clear_buffers_on_disconnect()
        
        # 检查缓冲区是否被清理
        if hasattr(streamer, '_audio_buffer') and len(streamer._audio_buffer) == 0:
            logger.info("✅ 音频缓冲区已清理")
        
        # 测试7: 定期检查任务
        logger.info("\n--- 测试7: 定期检查任务 ---")
        
        logger.info("等待定期检查任务运行...")
        await asyncio.sleep(streamer._connection_check_interval + 1)
        
        # 检查最后检查时间是否更新
        current_time = time.time()
        time_since_check = current_time - streamer._last_connection_check
        
        if time_since_check < streamer._connection_check_interval * 2:
            logger.info("✅ 定期检查任务正常运行")
        else:
            logger.warning(f"⚠️ 定期检查任务可能未运行，距离上次检查: {time_since_check:.1f}s")
        
        # 测试8: 性能测试
        logger.info("\n--- 测试8: 性能测试 ---")
        
        start_time = time.time()
        test_frames = 50
        
        for i in range(test_frames):
            streamer.write_frame(frame, audio_data)
            if i % 10 == 0:
                elapsed = time.time() - start_time
                fps = (i + 1) / elapsed if elapsed > 0 else 0
                logger.info(f"性能测试: {i+1}/{test_frames} 帧, FPS: {fps:.1f}")
        
        total_time = time.time() - start_time
        avg_fps = test_frames / total_time
        
        logger.info(f"性能测试完成: {test_frames}帧, 总时间: {total_time:.2f}s, 平均FPS: {avg_fps:.1f}")
        
        if avg_fps > 20:  # 期望至少20fps
            logger.info("✅ 性能测试通过")
        else:
            logger.warning(f"⚠️ 性能可能有问题，FPS过低: {avg_fps:.1f}")
        
        logger.info("\n🎉 所有连接检测改进测试完成！")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        raise
    finally:
        # 停止推流器
        logger.info("停止推流器...")
        streamer.stop()

def test_configuration_parameters():
    """测试配置参数"""
    logger.info("=== 测试配置参数 ===")
    
    # 测试不同的配置
    configs = [
        {
            'signaling_url': '0.0.0.0:8081',
            'audio_sample_rate': 16000,
            'video_fps': 25
        },
        {
            'signaling_url': '0.0.0.0:8082', 
            'audio_sample_rate': 48000,
            'video_fps': 30
        }
    ]
    
    for i, config in enumerate(configs):
        logger.info(f"测试配置 {i+1}: {config}")
        
        try:
            streamer = WebRTCStreamer(**config)
            
            # 检查配置是否正确应用
            assert streamer._connection_check_interval > 0
            assert streamer._connection_timeout > 0
            assert isinstance(streamer._last_frame_time, dict)
            
            logger.info(f"✅ 配置 {i+1} 测试通过")
            
        except Exception as e:
            logger.error(f"配置 {i+1} 测试失败: {e}")

if __name__ == "__main__":
    logger.info("开始WebRTC连接状态检测改进测试...")
    
    try:
        # 测试配置参数
        test_configuration_parameters()
        
        # 测试连接检测改进
        asyncio.run(test_connection_detection_improvements())
        
        logger.info("🎉 所有测试完成！连接状态检测改进验证成功！")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
