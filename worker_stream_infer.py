# -*-coding: utf-8 -*-
# @Time :2025-04-02
# <AUTHOR> lishengjing
# @Email : <EMAIL>
# @File : worker.py
# =================================================================================================
# 必须保存为worker.py.
# 必须定义全局变量：JOB_ALGORITHM，其为平台管理员向任务调度中心注册的算法名称.
# 必须实现函数：Work(job_id, parameters)，其为任务调度接口，job_id为任务的Id, job_params对应API接口参数
#              (Json格式)，函数返回Status(True/False)和Message(Dict格式，用于日志存储).
# -------------------------------------------------------------------------------------------------
import os,sys,requests,uuid,json,subprocess,cv2
import time,datetime
import shutil
from tqdm import tqdm
from loguru import logger
from multiprocessing import freeze_support
from lt_utils.check_params import Check_params
from lt_utils.wo_common import del_files,upload_file
from lt_utils.log_msg import logger_error
from lt_utils.process_download import auto_download_one_file,download_and_unpack_model_from_oss,download_and_unpack_model
import yaml

from h_utils.custom import CustomError
from flask import Flask, request
from y_utils.config import GlobalConfig
# from service.trans_dh_service import TransDhTask, Status
from service.stream_dh_service import  TransDhTask, Status
from run_local import easy_submit,easy_query,start_websocket_server

HOST_NAME= 'default_host_'
try:
    HOST_NAME = "{}_".format(os.uname().nodename)
except Exception as E:
    logger_error(E)


JOB_ALGORITHM = 'digitalhuman2dinferv2'

trans_dh_task = None
oss_server =None

def process_mask_video(original_video_path, original_mask_path, output_mask_path, workspace):
    """
    处理mask视频使其与原始视频帧数匹配
    
    Args:
        original_video_path (str): 原始视频路径
        original_mask_path (str): 原始mask视频路径
        output_mask_path (str): 输出的mask视频路径
        workspace (str): 工作目录路径
        
    Returns:
        str: 处理后的mask视频路径
    """
    # 获取视频帧数
    video_cap = cv2.VideoCapture(original_video_path)
    video_frames = int(video_cap.get(cv2.CAP_PROP_FRAME_COUNT))
    video_fps = video_cap.get(cv2.CAP_PROP_FPS)
    video_width = int(video_cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    video_height = int(video_cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    video_cap.release()
    
    # 获取mask帧数
    mask_cap = cv2.VideoCapture(original_mask_path)
    mask_frames = int(mask_cap.get(cv2.CAP_PROP_FRAME_COUNT))
    mask_cap.release()
    
    logger.info(f"视频帧数: {video_frames}, mask帧数: {mask_frames}")
    
    # 临时文件路径
    temp_mask = os.path.join(workspace, f"temp_mask.mp4")
    
    if video_frames <= mask_frames:
        # 裁剪mask视频，使其与video帧数一致
        cmd = f'ffmpeg -y -i {original_mask_path} -vf "select=between(n\,0\,{video_frames-1}),setpts=PTS-STARTPTS" -c:v libx264 -crf 18 {temp_mask}'
        logger.info(f"裁剪mask视频命令: {cmd}")
        subprocess.call(cmd, shell=True)
    else:
        # 当video帧数多于mask帧数时，需要拼接mask视频
        # 创建临时目录存放帧
        temp_dir = os.path.join(workspace, "temp_mask_frames")
        os.makedirs(temp_dir, exist_ok=True)
        
        # 提取所有mask帧
        cmd = f'ffmpeg -i {original_mask_path} -vf "fps={video_fps}" {temp_dir}/frame_%06d.jpg'
        subprocess.call(cmd, shell=True)
        
        # 计算需要额外添加的帧数
        extra_frames = video_frames - mask_frames
        
        # 创建帧列表文件
        frames_list_path = os.path.join(workspace, "frames_list.txt")
        with open(frames_list_path, 'w') as f:
            # 添加原始帧
            for i in range(1, mask_frames + 1):
                f.write(f"file 'temp_mask_frames/frame_{i:06d}.jpg'\n")
            
            # 循环添加额外帧（先倒序再正序循环）
            remaining = extra_frames
            is_reverse = True  # 开始是倒序
            
            while remaining > 0:
                if is_reverse:  # 倒序
                    for i in range(mask_frames-1, 0, -1):
                        if remaining <= 0:
                            break
                        f.write(f"file 'temp_mask_frames/frame_{i:06d}.jpg'\n")
                        remaining -= 1
                else:  # 正序
                    for i in range(2, mask_frames + 1):
                        if remaining <= 0:
                            break
                        f.write(f"file 'temp_mask_frames/frame_{i:06d}.jpg'\n")
                        remaining -= 1
                
                # 切换方向
                is_reverse = not is_reverse
        
        # 使用帧列表创建新的mask视频
        cmd = f'ffmpeg -y -f concat -safe 0 -i {frames_list_path} -c:v libx264 -vf "fps={video_fps},scale={video_width}:{video_height}" -crf 18 {temp_mask}'
        logger.info(f"拼接mask视频命令: {cmd}")
        subprocess.call(cmd, shell=True)
        
        # 清理临时文件
        try:
            shutil.rmtree(temp_dir)
            os.remove(frames_list_path)
        except Exception as e:
            logger_error(f"清理临时文件失败: {e}")
    
    # 移动临时文件到目标位置
    try:
        os.renames(temp_mask, output_mask_path)
        return output_mask_path
    except Exception as e:
        logger_error(f"重命名mask文件失败: {e}")
        return temp_mask

class create_logfile():
    def __init__(self,logdir='log'):
        self.logdir = logdir
        self.is_run = False
    
    def get_logfile(self,logdir='log'):
        logfile = os.path.join(logdir, "vctrain_log_{time}.log")
        os.makedirs(logdir, exist_ok=True)
        # rotation: 500 MB,12:00,1 week...
        logger.add(logfile, level='DEBUG', format="{time}| {level} |{message}", enqueue=True, rotation='2 week',
                colorize=True)
    def run(self):
        self.get_logfile(self.logdir)
        self.is_run = True

logfile_instance = create_logfile(logdir='log')

class Var():
    def __init__(self):
        pass

def work_process(job_id, job_params):
    return Work(job_id, job_params)

@logger.catch()
def Work(job_id, job_params):
    try:
        ############################################################初始化日志文件############################################################
        if not logfile_instance.is_run:
            logfile_instance.run()

        logger.info(
                "\n\n" + "**************************************************************************************************\n" * 3)
        t0 = time.time()
        callback_id = os.path.split(job_params.get('callback_url', None))[-1] if job_params.get('callback_url', None) is not None else  '12700001'
        logger.info("backend_id : {}\t job_id ： {}\t  job_params:{}".format(callback_id,job_id,job_params))
        MSG = ""
        ############################################################检查参数############################################################
        params = Check_params(job_id,job_params,JOB_ALGORITHM)
        if not params.is_check:
            params.callback_no_success(False,HOST_NAME+params.job_msg)
            return False,params.job_msg
            
        is_stream = job_params.get('is_stream', False)
        ############################################################删除上次任务的文件############################################################
        try:
            del_files(params.workspace)
        except Exception as e:
            logger_error(e)
        os.makedirs(params.workspace,exist_ok=True)
        
        ############################################################下载文件############################################################
        media_file = {}
        #优先下载video_url,其次才是speaker_id
        logger.info("download model and audio") # todo:确认模型存在
        t1 = time.time()
        local_model_path = os.path.join(params.spk_models,params.speaker_id)
        model_path = os.path.join(params.workspace,'models',params.speaker_id)

        is_train = '2' # 默认为推理模式
        # os.makedirs(model_path)
        if os.path.exists(local_model_path):
            logger.info(f"本地已存在模型文件，跳过下载: {local_model_path}")
            shutil.copytree(local_model_path,model_path)
            Isdownload = True
            _msg = "本地文件已存在，无需下载"
        elif params.wom_ai_environment != "Debug":
            if "oss_server" not in locals() or oss_server is None:
                from lt_utils.wo_oss import oss_server,auto_switch_oss
            logger.info(f"download model  {params.speaker_id} from oss ")
            oss_file_path = f'speaker_models_{params.version}/{params.speaker_id}/{params.speaker_id}.zip'  # 与训练的时候存放路径一致
            oss_server = auto_switch_oss(params.wom_ai_environment,oss_server)
            Isdownload, _, _msg = download_and_unpack_model_from_oss(model_path,oss_server,oss_file_path)
        else:
            logger.info("debug mode,but can not find video file {} ".format(params.video_url))

        if not Isdownload:
            if is_stream:
                logger.error(f"use stream,but can not find model {params.speaker_id}")
                params.callback_no_success(False,HOST_NAME+"use stream,but can not find model")
                return False, "use stream,but can not find model"
            else:   
                logger.error(f"download model error : {params.speaker_id},use zero-shot mode")
                is_train = '1' #没有模型则使用训练模式
                if params.video_url is not None:
                    logger.info(f"download video from {params.video_url} to {model_path}")
                    Isdownload, _, _msg = download_and_unpack_model(params.video_url,model_path)  # 如果存在就不下载  
                else:
                    logger.error(f"video_url is None ")
                    params.callback_no_success(False,HOST_NAME+_msg)
                    return False, _msg
        # 添加到media_file缓存
        media_file['video'] = os.path.join(model_path,params.speaker_id+'_video.mp4')
        # 如果存在mask文件，则添加到media_file缓存
        if os.path.exists(os.path.join(model_path,params.speaker_id+'_mask.mp4')):
            media_file['mask'] = os.path.join(model_path,params.speaker_id+'_mask.mp4')

        # 添加预览图片
        if os.path.exists(os.path.join(model_path,"preview.png")):
            media_file['preview_img'] = os.path.join(model_path,"preview.png")
        # 下载驱动音频
        audio_dir = os.path.join(params.workspace,'src_audio')
        Isdownload, audio_path, _msg = auto_download_one_file(params.audio_url,audio_dir)
        if not Isdownload:
            logger.error(f"download audio error : {params.audio_url} ")
            params.callback_no_success(False,HOST_NAME+_msg)
            return False, _msg
        media_file['audio'] = audio_path
        t2 = time.time()
        MSG += "download time: {:.3f}s".format(t2-t1)

        params_json = {
            "version": "v2",
            "job_id": job_id,
            "job_params": job_params
        }
        
        if not is_stream:
            # 将参数信息保存到params.json文件
            params_json_path = os.path.join(model_path, 'params.json')
            with open(params_json_path, 'w', encoding='utf-8') as f:
                json.dump(params_json, f, ensure_ascii=False, indent=4)
                
            logger.info(f"保存任务参数到 {params_json_path}")

            if not os.path.exists(os.path.join(model_path,'wh_value.txt')):
                logger.info(f'采用零样本模式')
                is_train = '0'

        if  'trans_dh_task' not in locals() or trans_dh_task is None:
            trans_dh_task = TransDhTask.instance()
            time.sleep(15)
            logger.info('模型初始化成功！')

        ################################################################ 开始推理 ################################################################
        request_data = {
            'audio_url':media_file['audio'],
            'video_url':media_file['video'],
            'code':job_id,
            'watermark_switch':0,
            'speaker_id':params.speaker_id,
            'is_train':is_train,
            'digital_auth':0,
            'chaofen':1,
            'pn':1,
            'is_stream': is_stream,
            'stream_url': job_params.get('stream_url', None)  # 添加job_params参数
        }
        logger.info("strat inference ! request_data: {}".format(request_data))
        is_success,return_data = easy_submit(request_data)  # 多进程处理，需要返回数据
        if not is_success:
            params.callback_no_success(is_success,HOST_NAME + return_data)
            return False,return_data
        
        # 流式增加启动websocket
        if is_stream:
            config_path = 'service/server_config.yaml'
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = yaml.safe_load(f)
                # Navigate to the tts_server configuration
                server_config = config.get('websocket_server', {}).get('tts_server', {})
                ip = server_config.get('ip', '127.0.0.1')
                port = server_config.get('port', 8766)
                timeout = server_config.get('timeout', 300)
            except (FileNotFoundError, yaml.YAMLError) as e:
                logger.warning(f"无法加载或解析 {config_path}: {e}. 使用默认值。")
                ip = "127.0.0.1"
                port = 8765
                timeout = 300
            start_websocket_server(ip, port, timeout)
        
        # 任务查询
        return_data = easy_query(request_data)  # 堵塞，返回string
        logger.info("query result: {}".format(return_data))
        t3 = time.time()
        MSG += " inference  time: {:.3f}s  ".format(t3-t2)
        # 获取推理结果
        # {"code": 10000,"data": {"code": "fd01c7b4ae7611ef9fcf6e9e21b189dd","msg": "任务完成","progress": 100,"
        # result": "./workspace/fd01c7b4ae7611ef9fcf6e9e21b189dd-r.mp4","status": 2},    "msg": "",    "success": true
        return_data_json = json.loads(return_data)
        code = return_data_json.get('code',0)
        _data = return_data_json.get('data',{})
        result = _data.get('result',"")
        status = _data.get('status',0)
        _msg = "success!"
        is_success= True
        if status not in [2] and code == 10000:
            _msg = _data.get('msg',"")
            is_success = False
            params.callback_no_success(is_success,HOST_NAME + _msg)
            return False,return_data

        ############################################################上传文件############################################################

        # 重命名视频
        new_video = os.path.join(params.workspace,params.speaker_id+os.path.splitext(os.path.basename(result))[-1])  # 最终结果文件名 /workspace/spkid.mp4
        try:
            os.renames(result,new_video)
        except Exception as e:
            logger_error(e)
            new_video = result
        params._C.update({'result_path':new_video})
        upload_files = [("video",new_video)]

        # 新增mask
        if 'mask' in media_file.keys():
            new_mask = os.path.join(params.workspace,params.speaker_id+"_mask"+os.path.splitext(os.path.basename(result))[-1])  # 最终结果文件名 /workspace/spkid_mask.mp4
            # 调用函数处理mask视频
            processed_mask = process_mask_video(new_video, media_file['mask'], new_mask, params.workspace)
            media_file['mask'] = processed_mask
            upload_files.append(("mask", processed_mask))
        if 'preview_img' in media_file.keys():
            upload_files.append(("preview_img",media_file['preview_img']))
            
        if  is_stream:  # 不用上传，提前结束
            params.callbacks()  # 调用call_back 函数 
            return is_success,"success"
        
        # 开始上传
        if is_train == '1':
            # 定义源目录和目标压缩文件的路径
            output_zip = os.path.join(params.workspace,params.speaker_id+".zip")
            # 使用shutil.make_archive进行压缩
            shutil.make_archive(output_zip.replace('.zip', ''), 'zip', model_path)
            logger.info(f"目录 {model_path} 已成功压缩为 {output_zip}")
            result_oss_dir = f'speaker_models_{params.version}/{params.speaker_id}/'
            is_success, _msg, media_oss_path = upload_file(params,output_zip,oss_server,auto_switch_oss,result_oss_dir)
            logger.info(f"上传模型文件成功: {media_oss_path}")
            # 提取首帧 
            try:
                cap = cv2.VideoCapture(media_file["video"])
                if not cap.isOpened():
                    raise Exception("Failed to open video")

                ret, frame = cap.read()
                if not ret:
                    raise Exception("Failed to read frame")
                preview_img_path = os.path.join(model_path, "preview.png")
                cv2.imwrite(preview_img_path, frame)
                cap.release()
            except Exception as e:
                logger_error(e)
                params.callback_no_success(False,HOST_NAME + "提取首帧失败")
                return False, "提取首帧失败"
            
            # 上传预览图片
            is_success, _msg, preview_oss_path = upload_file(params,preview_img_path,oss_server,auto_switch_oss,result_oss_dir)
            if not is_success:
                params.callback_no_success(is_success,HOST_NAME +_msg)  # 调用call_back 函数
                return is_success,_msg
            
            params._C.update({'speaker_id':params.speaker_id,'preview_img':preview_oss_path})
            params.callbacks()  # 调用call_back 函数
      
        logger.info("start upload file")
        result_oss_dir = 'digital_human_result' + "/" + datetime.date.today().strftime("%Y-%m-%d") +  "/" + callback_id + "/" + job_id + "_" + params.version + "/"
        
        upload_files.append(("params_json",params_json_path))
        for file_type,file in upload_files:
            if 'oss_server' not in locals() or oss_server is None:
                from lt_utils.wo_oss import oss_server,auto_switch_oss

            is_success, _msg, media_oss_path = upload_file(params,file,oss_server,auto_switch_oss,result_oss_dir)
            if not is_success:
                params.callback_no_success(is_success,HOST_NAME +_msg) 
                return is_success,_msg
            if file_type == "video":
                params._C.update({'result_path':media_oss_path})
            elif file_type == "mask":
                params._C.update({'mask_path':media_oss_path})
            elif file_type == "preview_img":
                params._C.update({'preview_img':media_oss_path})

        params.callbacks() 
        MSG += "upload time: {:.3f}s".format(time.time()-t3) 
        MSG += "  total time: {:.3f}s ".format(time.time()-t0)  
        logger.info("############################【{}】############################".format(MSG))
        return is_success,"success"
        ############################################################结束############################################################
    except Exception as e:
        logger_error(e)
        logger.error("Error: Call back failed!\n")
        return False, "failed!"




if __name__ == '__main__':
    freeze_support()
    # trans_dh_task = TransDhTask.instance()
    # time.sleep(15)
    # logger.info('模型初始化成功！')
 

    if True:

        job_params={
            'callback_url':'127.0.0.1/1862548554856034305',
            "video_url": None,
            "mask_url": None,
            'use_mask':1,
            'speaker_id':"gdgdbdfndjfthdrg",
            'anchor_model_url':None,
            'audio_url':'yangwen.wav',
            'additional':{'WOM_AI_ENVIRONMENT': 'Development',   # ["Debug", "Development","Production"] 用来切换不同的环境对象存储
            }
        }

        job_params={ 'video_url': 'me.mp4', 'mask_url': None, 'speaker_id': '7bcb1f55c7cb435fa56','callback_url':'127.0.0.1/978f5e45e3504e2b9e3',
                        'audio_url': 'nv.wav',
                        'additional': {'WOM_AI_ENVIRONMENT': 'Debug'}, 'use_mask': 1, 'blend_threshold': 0.0, 'blend_sigma': 35.0, 'blend_kernel_size': 15}
        for i in range(1):
            print(Work('fd01c7b4ae7611ef9fcf6e9e21b189dd',job_params))
