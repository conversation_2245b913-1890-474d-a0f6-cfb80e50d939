# -*-coding: utf-8 -*-
# @Time :2025-04-02
# <AUTHOR> lishengjing
# @Email : <EMAIL>
# @File : worker.py
# =================================================================================================
# 必须保存为worker.py.
# 必须定义全局变量：JOB_ALGORITHM，其为平台管理员向任务调度中心注册的算法名称.
# 必须实现函数：Work(job_id, parameters)，其为任务调度接口，job_id为任务的Id, job_params对应API接口参数
#              (Json格式)，函数返回Status(True/False)和Message(Dict格式，用于日志存储).
# -------------------------------------------------------------------------------------------------
import os,sys,requests,uuid,json,subprocess,datetime
import time
import shutil
from multiprocessing import freeze_support
from loguru import logger
import numpy as np
from service.stream_dh_service import TransDhTask # 方便提前初始化


JOB_ALGORITHM = 'digitalhuman2dtrainv2'



def Work(job_id, job_params):
    # 获取任务类型

    task_type = job_params.get('task_type',None)
    logger.info(f'task_type : {task_type}')
    if task_type is None:
        task_type = 'train' if JOB_ALGORITHM.find('train') >0 else 'inference'

    if task_type == 'train':
        from worker_train import work_process
    elif task_type == 'inference':
        from worker_infer_notrian import work_process
    elif task_type == 'stream':
        from worker_stream_infer import work_process
    return work_process(job_id,job_params)

if __name__ == '__main__':
    freeze_support()

    if True:
        # job_params={
        # 'callback_url':'127.0.0.1/1862548554856034305',
        # "video_url": None,
        # "mask_url": None,
        # 'use_mask':1,
        # 'speaker_id':"123456",
        # 'anchor_model_url':None,
        # 'audio_url':None,
        # 'blend_threshold': 0.0, 
        # 'blend_sigma': 35.0, 
        # 'blend_kernel_size': 15,
        # 'additional':{'WOM_AI_ENVIRONMENT': 'Development',   # ["Debug", "Development","Production"] 用来切换不同的环境对象存储
        # }
        # }
        # job_params = {'algorithm': 'digitalhuman2dtrainv2',
        #              'callback_url': 'http://172.19.0.93:9195/creator-open-api/v3/anchor/digitalhuman2dZerotrain/callback/1917484238840553474',
        #              'video_url': 'http://172.16.249.3:8082/woapp/zqOss/OssTest/avataravatar01131745998815630.mp4',
        #              'speaker_id': '890daf99-7b5e-4ec5-9fa8-7e61cebb6483', 'blend_threshold': 0.0, 'blend_sigma': 35.0,
        #              'blend_kernel_size': 15}

        # job_id = '12700001'
        # print(Work(job_id, job_params))

        # job_params = {'algorithm': 'digitalhuman2dtrainv2',
        #              'callback_url': 'http://172.19.0.93:9195/creator-open-api/v3/anchor/digitalhuman2dZerotrain/callback/1917483987396222978',
        #              'video_url': 'http://172.16.17.245:8880/test/mask_test_video-1.mp4'
        #             #  'mask_url': 'http://172.16.17.245:8880/test/mask_test_mask-1.mp4',
        #              }
   

        # job_id = '12700002'
        # print(Work(job_id,job_params))
        # 测试配置
        # video_url = "walk.mp4"
        video_url = "v3720p.mp4"
        audio_url = "11.wav"
        speaker_id = os.path.basename(video_url).split('.')[0]

        
        # 数字人流式推理配置 - 使用WebRTC推流
        job_params={'algorithm': 'digitalhuman2dtrainv2', 'callback_url': 'http://172.16.100.21:8100/creator-openv-api/v3/anchor/digitalhuman2dZerotrain/callback/1917169992917164033', 
                    'video_url': video_url, 'audio_url': audio_url,"is_stream":True,
                    'speaker_id': speaker_id, 'blend_threshold': 0.0, 'blend_sigma': 35.0, 'blend_kernel_size': 15,'task_type':'stream',
                    'stream_url': '127.0.0.1:8080',  # WebRTC信令服务器地址
                    'streamer_type': 'webrtc'}  # 使用WebRTC推流器

        job_id = 'fa3ba59424e711f091782228ff08ca3c'
        print(f"启动数字人推流任务...")
        print(f"视频文件: {video_url}")
        print(f"音频文件: {audio_url}")  
        print(f"WebRTC信令服务器: {job_params['stream_url']}")
        print(f"WebSocket地址: ws://127.0.0.1:8766/{speaker_id}")
        Work(job_id,job_params)

"""
0:零样本
1：训练
2：推理
"""
  # 1. 实际运行函数： TransDhTask.instance().work
  # 2. 首先计算 wh（init_wh_process）, 提起音频特征（get_aud_feat1）
  # 3. 读取视频 (drivered_video_pn),将视频传给audio_transfer
  # 4. audio_transfer： 1.超分处理 2.获取人脸目标框以及关键点 3.开始推理 4.发送数据到视频处理进程
  # 5. 视频处理进程： 1.合成视频 2.上传视频
  # 需要保存的东西：1.wh（init_wh_process），2.超分处理后的图片 3.关键点、人脸框信息、（图片要不要保留？）
  # 训练推理要分开，训练的时候加载

  # 根据本地文件判断是训练阶段还是推理阶段，同时训练阶段还需要根据是否需要做超分处理、关键点提取等信息
  # 可以灵活切换，可以支持直接输入视频当零样本推理，也可以用来训练

