# uncompyle6 version 3.9.2
# Python bytecode version base 3.8.0 (3413)
# Decompiled from: Python 3.10.9 | packaged by Anaconda, Inc. | (main, Mar  1 2023, 18:18:15) [MSC v.1916 64 bit (AMD64)]
# Embedded file name: /code/wenet/compute_ctc_att_bnf.py
# Compiled at: 2024-04-01 10:28:36
# Size of source mod 2**32: 7255 bytes
"""
Compute CTC-Attention Seq2seq ASR encoder bottle-neck features (BNF).
"""
import os, time, argparse, torch
from pathlib import Path
import yaml, numpy as np
from wenet.tools._extract_feats import wav2mfcc_v2, load_wav, _extract_feature
os.environ["CUDA_DEVICE_ORDER"] = "PCI_BUS_ID"
hparams1 = {
 'sample_rate': 16000, 
 'preemphasis': 0.97, 
 'n_fft': 1024, 
 'hop_length': 160, 
 'win_length': 800, 
 'num_mels': 80, 
 'n_mfcc': 13, 
 'window': 'hann', 
 'fmin': 0.0, 
 'fmax': 8000.0, 
 'ref_db': 20, 
 'min_db': -80.0, 
 'iterations': 100, 
 'silence_db': -28.0, 
 'center': True}
SAMPLE_RATE = 16000
from wenet.transformer.encoder import ConformerEncoder
import matplotlib
matplotlib.use("Agg")
import matplotlib.pyplot as plt


class WenetStreamer:
    """
    A streaming processor for Wenet bottle-neck feature extraction.
    This class manages an audio buffer and processes audio in overlapping
    chunks to simulate streaming for a non-streaming model.
    """

    def __init__(self, wenet_model, device="cuda", chunk_ms=80, context_ms=320):
        """
        Args:
            wenet_model: The loaded Wenet model.
            device: The device to run the model on.
            chunk_ms: The duration of audio to process and output features for in one step (ms).
            context_ms: The duration of left and right context audio to provide to the model (ms).
        """
        self.wenet_model = wenet_model
        self.device = device
        self.hparams = hparams1
        self.sample_rate = self.hparams['sample_rate']
        self.hop_length = self.hparams['hop_length']
        # Wenet中的Conformer模型默认具有4的下采样率。
        self.subsampling = 4

        # 模型输出中每个特征帧对应的音频样本数。
        self.frame_shift_samples = self.hop_length * self.subsampling #160*4=640

        # 将时间从毫秒转换为样本数。
        # 确保chunk_samples是frame_shift_samples的倍数，以实现清晰的特征帧对齐。
        self.chunk_samples = int(self.sample_rate * chunk_ms / 1000)  #16000*80/1000=1280
        self.chunk_samples = (self.chunk_samples // self.frame_shift_samples) * self.frame_shift_samples #1280//160=8
        
        self.context_samples = int(self.sample_rate * context_ms / 1000) #16000*320/1000=5120
        self.window_samples = self.context_samples + self.chunk_samples + self.context_samples #5120+1280+5120=11520,也就是18帧

        # 计算上下文和当前窗口的特征帧数。
        # 上下文帧数：5120//640=8
        # 当前窗口帧数：1280//640=2
        self.context_frames = self.context_samples // self.frame_shift_samples #5120//640=8
        self.chunk_frames = self.chunk_samples // self.frame_shift_samples #1280//640=2

        # 音频缓冲区，用于流式处理。
        # 初始化为零，大小为上下文样本数。
        self.buffer = np.zeros(self.context_samples, dtype=np.float32)

    def reset(self):
        """Resets the internal buffer to an initial state."""
        self.buffer = np.zeros(self.context_samples, dtype=np.float32)

    def _compute_bnf(self, audio_data):
        """Computes BNF for a given audio segment."""
        mel, _ = wav2mfcc_v2(audio_data,
                             sr=self.hparams["sample_rate"],
                             n_mfcc=self.hparams["n_mfcc"],
                             n_fft=self.hparams["n_fft"],
                             hop_len=self.hparams["hop_length"],
                             win_len=self.hparams["win_length"],
                             window=self.hparams["window"],
                             num_mels=self.hparams["num_mels"],
                             center=self.hparams["center"])

        wav_tensor = torch.from_numpy(mel).float().to(self.device).unsqueeze(0)
        wav_length = torch.LongTensor([mel.shape[0]]).to(self.device)

        with torch.no_grad():
            bnf = self.wenet_model(wav_tensor, wav_length)
        
        return bnf.squeeze(0).cpu().numpy()

    def process(self, audio_chunk):
        """
        Processes a new chunk of audio, returning feature frames.
        Args:
            audio_chunk (np.ndarray): 新音频块。
        Returns:
            np.ndarray or None: 新特征帧的numpy数组，或者如果没有足够的音频缓冲则返回None。
        """
        if audio_chunk is None:

            return None
        # print("【WenetStreamer】处理音频数据",len(audio_chunk))
        self.buffer = np.concatenate((self.buffer, audio_chunk))
        # 11520 - 5120 = 6400  ，一帧640，6400/640=10，所以需要10帧，也就是400ms
        if len(self.buffer) < self.window_samples:
            return None

        features_list = []
        while len(self.buffer) >= self.window_samples:
            processing_window = self.buffer[:self.window_samples]
            
            bnf = self._compute_bnf(processing_window)
            
            # 提取与中心`chunk_frames`对应的特征。
            valid_bnf = bnf[self.context_frames : self.context_frames + self.chunk_frames]
            features_list.append(valid_bnf)
            
            # 滑动缓冲区向前
            self.buffer = self.buffer[self.chunk_samples:]
            # print("【WenetStreamer】11111111111111111111处理音频数据",len(audio_chunk))
        
        return np.concatenate(features_list, axis=0) if features_list else None

    def flush(self):
        """
        Processes any remaining audio in the buffer at the end of the stream.
        This will pad the buffer to meet the window size for a final computation.
        返回:
            np.ndarray or None: 最终特征帧。
        """
        if len(self.buffer) <= self.context_samples:
            # self.reset()
            return None

        padding_needed = self.window_samples - len(self.buffer)
        padding = np.zeros(padding_needed, dtype=np.float32)
        self.buffer = np.concatenate((self.buffer, padding))
        
        bnf = self._compute_bnf(self.buffer)
        
        # 确定要从刷新缓冲区提取多少有效帧
        valid_samples = len(self.buffer) - self.context_samples - self.context_samples
        if valid_samples <= 0:
            # self.reset()
            return None
            
        valid_frames = valid_samples // self.frame_shift_samples
        flushed_bnf = bnf[self.context_frames : self.context_frames + valid_frames]
        
        # self.reset()
        return flushed_bnf


def plot_spectrogram(input, path=None, info=None):
    spectrogram = input
    fig = plt.figure(figsize=(16, 10))
    plt.imshow((spectrogram.T), aspect="auto", origin="lower")
    plt.colorbar()
    if info is not None:
        plt.xlabel(info)
    plt.tight_layout()
    plt.savefig(path)
    plt.close()
    return fig


class PPGModel(torch.nn.Module):

    def __init__(self, encoder):
        super().__init__()
        self.encoder = encoder

    def forward(self, feats, feats_lengths):
        """
        Args:
            speech (tensor): (B, L)
            speech_lengths (tensor): (B, )

        Returns:
            bottle_neck_feats (tensor): (B, L//hop_size, 144)

        """
        encoder_out, encoder_out_lens = self.encoder(feats, feats_lengths)
        return encoder_out

    def _extract_feats(self, speech: torch.Tensor, speech_lengths: torch.Tensor):
        assert speech_lengths.dim() == 1
        speech = speech[:, :speech_lengths.max()]
        if self.frontend is not None:
            feats, feats_lengths = self.frontend(speech, speech_lengths)
        else:
            feats, feats_lengths = speech, speech_lengths
        return (feats, feats_lengths)


def build_model(args):
    encoder = ConformerEncoder(input_size=80, **args.encoder_conf)
    model = PPGModel(encoder)
    return model

@torch.no_grad()
def load_ppg_model(train_config, model_file, device):
    config_file = Path(train_config)
    if not config_file.exists():
        raise FileNotFoundError(f"配置文件不存在: {train_config}")
    with config_file.open("r", encoding="utf-8") as f:
        args = yaml.safe_load(f)
    args = argparse.Namespace(**args)
    model = build_model(args)
    model_state_dict = model.state_dict()
    ckpt_state_dict = torch.load(model_file, map_location="cpu")
    ckpt_state_dict = {k: v for k, v in ckpt_state_dict.items() if "encoder.global_cmvn" not in k}
    model_state_dict.update(ckpt_state_dict)
    model.load_state_dict(model_state_dict, strict=False)
    print(f">>>>>>>>>>>>>>>>>>>>>  音频模型加载成功！！！！！！！！！！ <<<<<<<<<<<<<<<<<<<<<<<")
    return model.eval().to(device)


def compute_bnf(wav_dir, wenet_model, section=560000):
    device = "cuda"
    ppg_model_local = wenet_model
    wav_arr = load_wav(wav_dir, sr=hparams1["sample_rate"])  #16000*sec
    zero = np.zeros(6400)      # 6400是有讲究的，6400/640=10，也就是10帧，也就是0.4秒，从win_size=20来看，就是0.4*20=8帧，也就是以当前帧为中心，前后各10帧，刚好是0.4秒
    wav_arr = np.concatenate((zero, wav_arr, zero))
    result = []
    add_silence_flag = False
    # print("len(wav_arr) // 560000 + 1",len(wav_arr) // section + 1)
    for i in range(len(wav_arr) // section + 1): # 这里每多一次。就减少1帧
        # print("i",i)
        wav_arr_ = wav_arr[section * i:section * (i + 1)]
        if len(wav_arr_) < hparams1["sample_rate"]:
            wav_arr_ = np.append(wav_arr_, np.zeros(hparams1["sample_rate"]))
            add_silence_flag = True
        mel, x_stft = wav2mfcc_v2(wav_arr_,
                                  sr=hparams1["sample_rate"],
                                  n_mfcc=hparams1["n_mfcc"],
                                  n_fft=hparams1["n_fft"],
                                  hop_len=hparams1["hop_length"],
                                  win_len=hparams1["win_length"],
                                  window=hparams1["window"],
                                  num_mels=hparams1["num_mels"],
                                  center=hparams1["center"])
        # print("mel.shape",mel.shape)  # 46s： 3501+1181 *80


        # mel_list = mel.tolist()
        # with open("D:/gitlab/code/digitalhuman2dtrainv2/11_16k_10s_mel.txt", "w") as f:
        #     for i,item in enumerate(mel_list):
        #         f.write(str(i) + "\n" + str(item) + "\n\n")

        # mel = np.full(shape=(1081,80),fill_value=-70.0,dtype=mel.dtype)
        # mel_list = mel.tolist()
        # with open("D:/gitlab/code/digitalhuman2dtrainv2/11_16k_10s_mel_1081.txt", "w") as f:
        #     for i,item in enumerate(mel_list):
        #         f.write(str(i) + "\n" + str(item) + "\n\n")
        
        wav_tensor = torch.from_numpy(mel).float().to(device).unsqueeze(0)
        wav_length = torch.LongTensor([mel.shape[0]]).to(device)
        start_time = time.time()
        with torch.no_grad():
            bnf = ppg_model_local(wav_tensor, wav_length)
        # print("use_time:", time.time() - start_time, bnf.shape)
        bnf_npy = bnf[0].squeeze(0).cpu().numpy()[:-25] if add_silence_flag else bnf.squeeze(0).cpu().numpy()
        result.append(bnf_npy)
        add_silence_flag = False

    bnf_npy = np.concatenate(result, 0)  # 46s：1168*256
    return bnf_npy


def get_parser():
    parser = argparse.ArgumentParser(description="compute ppg or ctc-bnf or ctc-att-bnf")
    parser.add_argument("--output_dir",type=str,default="/data1/wangpeiyu/out/dg_interface_2_2/wrong/wenet")
    parser.add_argument("--wav_dir",type=str,default="/data1/wangpeiyu/out/dg_interface_2_2/wrong/add_sil")
    parser.add_argument("--train_config",type=str,default="examples/aishell/aidata/conf/train_conformer_multi_cn.yaml")
    parser.add_argument("--model_file",type=str,default="examples/aishell/aidata/exp_3500/conformer/81.pt")
    return parser


def get_weget(wavpath, wenet_model, section=560000): 
# def get_weget(wavpath, wenet_model, section=160000):
    return compute_bnf(wavpath, wenet_model, section)


def get_weget0(wavpath):
    return compute_bnf(wavpath, "/data1/wangpeiyu/APB2Face/wenet/examples/aishell/aidata/conf/train_conformer_multi_cn.yaml", "/data1/wangpeiyu/APB2Face/wenet/examples/aishell/aidata/exp/conformer/wenetmodel.pt")


if __name__ == "__main__":
    result = get_weget("D:\gitlab\code\digitalhuman2dtrainv2\11_16k_10s.wav")
    print(result.shape)
