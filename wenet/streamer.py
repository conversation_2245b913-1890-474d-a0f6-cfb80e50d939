import torch
import numpy as np
from wenet.tools._extract_feats import wav2mfcc_v2

# This hparams dictionary is copied from wenet/compute_ctc_att_bnf.py
hparams1 = {
    'sample_rate': 16000, 
    'preemphasis': 0.97, 
    'n_fft': 1024, 
    'hop_length': 160, 
    'win_length': 800, 
    'num_mels': 80, 
    'n_mfcc': 13, 
    'window': 'hann', 
    'fmin': 0.0, 
    'fmax': 8000.0, 
    'ref_db': 20, 
    'min_db': -80.0, 
    'iterations': 100, 
    'silence_db': -28.0, 
    'center': True
}

class WenetStreamer:
    """
    Wenet瓶颈特征提取的流式处理器。
    该类管理音频缓冲区并以重叠块的方式处理音频，
    以模拟非流式模型的流式处理。
    """

    def __init__(self, wenet_model, device="cuda", chunk_ms=80, context_ms=320):
        """
        参数:
            wenet_model: 已加载的Wenet模型
            device: 运行模型的设备
            chunk_ms: 单步处理和输出特征的音频时长(毫秒)
            context_ms: 提供给模型的左右上下文音频时长(毫秒)
        """
        self.wenet_model = wenet_model
        self.device = device
        self.hparams = hparams1
        self.sample_rate = self.hparams['sample_rate']
        self.hop_length = self.hparams['hop_length']
        # Wenet中的Conformer模型默认下采样率为4
        self.subsampling = 4

        # 模型输出的每个特征帧对应的音频样本数
        self.frame_shift_samples = self.hop_length * self.subsampling

        # 将时间从毫秒转换为样本数
        # 确保chunk_samples是frame_shift_samples的倍数，以实现清晰的帧对齐
        self.chunk_samples = int(self.sample_rate * chunk_ms / 1000)
        self.chunk_samples = (self.chunk_samples // self.frame_shift_samples) * self.frame_shift_samples
        
        self.context_samples = int(self.sample_rate * context_ms / 1000)
        self.window_samples = self.context_samples + self.chunk_samples + self.context_samples

        # 特征帧计数
        self.context_frames = self.context_samples // self.frame_shift_samples
        self.chunk_frames = self.chunk_samples // self.frame_shift_samples

        # 用于流式处理的音频缓冲区
        self.buffer = np.zeros(self.context_samples, dtype=np.float32)

    def _compute_bnf(self, audio_data):
        """计算给定音频段的BNF特征"""
        mel, _ = wav2mfcc_v2(audio_data,
                             sr=self.hparams["sample_rate"],
                             n_mfcc=self.hparams["n_mfcc"],
                             n_fft=self.hparams["n_fft"],
                             hop_len=self.hparams["hop_length"],
                             win_len=self.hparams["win_length"],
                             window=self.hparams["window"],
                             num_mels=self.hparams["num_mels"],
                             center=self.hparams["center"])

        wav_tensor = torch.from_numpy(mel).float().to(self.device).unsqueeze(0)
        wav_length = torch.LongTensor([mel.shape[0]]).to(self.device)

        with torch.no_grad():
            bnf = self.wenet_model(wav_tensor, wav_length)
        
        return bnf.squeeze(0).cpu().numpy()

    def process(self, audio_chunk):
        """
        处理新的音频块，返回特征帧
        参数:
            audio_chunk (np.ndarray): 新的音频块
        返回:
            np.ndarray or None: 新的特征帧数组，如果缓冲区音频不足则返回None
        """
        if audio_chunk is None:
            return None
            
        self.buffer = np.concatenate((self.buffer, audio_chunk))
        
        if len(self.buffer) < self.window_samples:
            return None

        features_list = []
        while len(self.buffer) >= self.window_samples:
            processing_window = self.buffer[:self.window_samples]
            
            bnf = self._compute_bnf(processing_window)
            
            # 提取对应于中心`chunk_frames`的特征
            valid_bnf = bnf[self.context_frames : self.context_frames + self.chunk_frames]
            features_list.append(valid_bnf)
            
            # 向前滑动缓冲区
            self.buffer = self.buffer[self.chunk_samples:]
        
        return np.concatenate(features_list, axis=0) if features_list else None

    def flush(self):

        """
        处理流结束时缓冲区中的剩余音频
        这将填充缓冲区以满足最终计算的窗口大小
        返回:
            np.ndarray or None: 最终的特征帧
        """
        if len(self.buffer) <= self.context_samples:
            return None

        padding_needed = self.window_samples - len(self.buffer)
        padding = np.zeros(padding_needed, dtype=np.float32)
        self.buffer = np.concatenate((self.buffer, padding))
        
        bnf = self._compute_bnf(self.buffer)
        
        # 确定从刷新缓冲区中提取多少有效帧
        valid_samples = len(self.buffer) - self.context_samples - self.context_samples
        if valid_samples <= 0:
            return None
            
        valid_frames = valid_samples // self.frame_shift_samples
        flushed_bnf = bnf[self.context_frames : self.context_frames + valid_frames]
        
        self.buffer = np.zeros(self.context_samples, dtype=np.float32) # 重置缓冲区
        return flushed_bnf
