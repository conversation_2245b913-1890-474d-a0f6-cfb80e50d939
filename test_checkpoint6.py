#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试checkpoint6版本的WebRTC推流器
验证基本功能是否正常工作
"""

import asyncio
import time
import numpy as np
from loguru import logger
from service.webrtc import WebRTCStreamer

async def test_checkpoint6():
    """测试checkpoint6版本"""
    logger.info("=== 测试checkpoint6版本的WebRTC推流器 ===")
    
    # 创建推流器
    streamer = WebRTCStreamer(
        signaling_url="0.0.0.0:8080",
        audio_sample_rate=16000,
        video_fps=25
    )
    
    try:
        # 测试启动
        logger.info("启动推流器...")
        success = streamer.start()
        assert success, "推流器启动失败"
        
        # 等待推流器完全启动
        await asyncio.sleep(2)
        
        # 检查状态
        status = streamer.get_status()
        logger.info(f"启动后状态: {status}")
        assert status['is_running'], "推流器应该在运行"
        
        # 测试写入帧
        logger.info("测试写入帧...")
        frame = np.zeros((720, 1280, 3), dtype=np.uint8)
        frame[:, :, 0] = 255  # 红色帧
        
        audio_data = np.sin(2 * np.pi * 440 * np.linspace(0, 0.02, 320)).astype(np.float32)
        
        # 写入一些帧
        for i in range(10):
            streamer.write_frame(frame, audio_data)
            if i % 5 == 0:
                logger.info(f"已写入 {i+1} 帧")
            await asyncio.sleep(0.04)  # 25fps
        
        logger.info("✅ checkpoint6版本测试通过")
        
        # 测试连接状态
        conn_status = streamer.get_connection_status()
        logger.info(f"连接状态: {conn_status}")
        
        # 测试队列状态
        queue_status = streamer.get_queue_status()
        logger.info(f"队列状态: {queue_status}")
        
        logger.info("✅ 所有功能测试通过")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
        raise
    finally:
        # 测试停止
        logger.info("停止推流器...")
        streamer.stop()
        
        # 检查最终状态
        status = streamer.get_status()
        logger.info(f"停止后状态: {status}")

def test_inheritance():
    """测试继承关系"""
    logger.info("=== 测试继承关系 ===")
    
    from service.base_webrtc import BaseWebRTCStreamer
    
    # 创建WebRTCStreamer实例
    streamer = WebRTCStreamer("0.0.0.0:8081")
    
    # 验证继承关系
    assert isinstance(streamer, BaseWebRTCStreamer), "WebRTCStreamer应该继承自BaseWebRTCStreamer"
    
    # 检查基本方法
    required_methods = ['start', 'stop', 'write_frame', 'get_status']
    for method in required_methods:
        assert hasattr(streamer, method), f"应该有{method}方法"
    
    logger.info("✅ 继承关系测试通过")

if __name__ == "__main__":
    logger.info("开始测试checkpoint6版本的WebRTC推流器...")
    
    try:
        # 测试继承关系
        test_inheritance()
        
        # 测试基本功能
        asyncio.run(test_checkpoint6())
        
        logger.info("🎉 checkpoint6版本测试完全通过！代码已恢复到可运行状态！")
        
    except Exception as e:
        logger.error(f"测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    logger.info("测试完成")
