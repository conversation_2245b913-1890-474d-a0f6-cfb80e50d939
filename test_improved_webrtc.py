#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试改进的WebRTC推流器
基于PlayerStreamTrack的时间戳处理改进
"""

import asyncio
import time
import numpy as np
import cv2
from loguru import logger
from service.vid_encoder import WebRTCStreamer

async def test_improved_webrtc():
    """测试改进的WebRTC推流器"""
    
    # 创建推流器
    streamer = WebRTCStreamer(
        signaling_url="0.0.0.0:8080",
        audio_sample_rate=16000,
        video_fps=25
    )
    
    # 启动推流器
    if not streamer.start():
        logger.error("推流器启动失败")
        return
    
    logger.info("推流器启动成功，开始发送测试数据...")
    
    try:
        # 生成测试视频帧
        frame_count = 0
        start_time = time.time()
        
        for i in range(500):  # 发送500帧，约20秒
            # 生成测试图像（彩色渐变）
            frame = np.zeros((720, 1280, 3), dtype=np.uint8)
            frame[:, :, 0] = (i * 5) % 256  # 红色通道
            frame[:, :, 1] = (i * 3) % 256  # 绿色通道
            frame[:, :, 2] = (i * 7) % 256  # 蓝色通道
            
            # 添加帧计数文本
            cv2.putText(frame, f"Frame: {i}", (50, 50), 
                       cv2.FONT_HERSHEY_SIMPLEX, 2, (255, 255, 255), 3)
            
            # 生成测试音频（正弦波）
            duration = 0.04  # 40ms音频块
            sample_rate = 16000
            samples = int(duration * sample_rate)
            t = np.linspace(0, duration, samples, False)
            frequency = 440 + (i % 100) * 10  # 变化的频率
            audio_chunk = np.sin(2 * np.pi * frequency * t).astype(np.float32) * 0.3
            batch_audio_data = [audio_chunk]
            
            # 发送帧
            streamer.write_frame(frame, batch_audio_data)
            frame_count += 1
            
            # 控制发送速率（25fps）
            await asyncio.sleep(0.04)
            
            # 每100帧输出统计信息
            if i % 100 == 0:
                elapsed = time.time() - start_time
                actual_fps = frame_count / elapsed if elapsed > 0 else 0
                logger.info(f"已发送 {frame_count} 帧，实际FPS: {actual_fps:.2f}")
        
        logger.info("测试数据发送完成，等待5秒后关闭...")
        await asyncio.sleep(5)
        
    except Exception as e:
        logger.error(f"测试过程中出错: {e}")
    finally:
        # 关闭推流器
        streamer.stop()
        logger.info("推流器已关闭")

def test_timestamp_accuracy():
    """测试时间戳精度"""
    logger.info("=== 时间戳精度测试 ===")
    
    # 模拟时间戳计算
    fps = 25
    VIDEO_CLOCK_RATE = 90000
    VIDEO_PTIME = 1 / fps
    
    start_time = time.time()
    timestamp = 0
    
    for i in range(100):
        timestamp += int(VIDEO_PTIME * VIDEO_CLOCK_RATE)
        expected_time = start_time + (timestamp / VIDEO_CLOCK_RATE)
        actual_time = time.time()
        
        if i % 25 == 0:  # 每秒输出一次
            drift = actual_time - expected_time
            logger.info(f"帧 {i}: 时间戳={timestamp}, 预期时间={expected_time:.3f}, "
                       f"实际时间={actual_time:.3f}, 漂移={drift:.3f}s")
        
        time.sleep(VIDEO_PTIME)

if __name__ == "__main__":
    logger.info("开始测试改进的WebRTC推流器...")
    
    # 测试时间戳精度
    test_timestamp_accuracy()
    
    # 测试WebRTC推流
    logger.info("\n开始WebRTC推流测试...")
    logger.info("请在浏览器中访问 http://localhost:8080 来查看推流效果")
    
    try:
        asyncio.run(test_improved_webrtc())
    except KeyboardInterrupt:
        logger.info("测试被用户中断")
    except Exception as e:
        logger.error(f"测试失败: {e}")
