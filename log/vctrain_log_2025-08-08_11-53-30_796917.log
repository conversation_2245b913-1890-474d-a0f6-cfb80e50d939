2025-08-08T11:53:30.796917+0800| INFO |

**************************************************************************************************
**************************************************************************************************
**************************************************************************************************

2025-08-08T11:53:30.803382+0800| INFO |backend_id : 1917169992917164033	 job_id ： fa3ba59424e711f091782228ff08ca3c	  job_params:{'algorithm': 'digitalhuman2dtrainv2', 'callback_url': 'http://172.16.100.21:8100/creator-openv-api/v3/anchor/digitalhuman2dZerotrain/callback/1917169992917164033', 'video_url': 'v3720p.mp4', 'audio_url': '11.wav', 'is_stream': True, 'speaker_id': 'v3720p', 'blend_threshold': 0.0, 'blend_sigma': 35.0, 'blend_kernel_size': 15, 'task_type': 'stream', 'stream_url': '127.0.0.1:8080', 'streamer_type': 'webrtc'}
2025-08-08T11:53:30.803939+0800| INFO |本地接收到使用Production环境的传参，将系统参数Production注入！
2025-08-08T11:53:30.804944+0800| INFO |WOM_AI_ENVIRONMENT 配置为：Production，使用正式环境的对象存储
2025-08-08T11:53:30.805943+0800| INFO |重新连接 ：J70ORSJ6RP77IL8ICOFS 环境的oss
2025-08-08T11:53:31.173165+0800| INFO |oss 链接成功
2025-08-08T11:53:31.177955+0800| INFO |WOM_AI_ENVIRONMENT 配置为：Production，使用正式环境的对象存储
2025-08-08T11:53:31.179392+0800| INFO |重新连接模型保存路径 ：Production 环境的oss
2025-08-08T11:53:32.645709+0800| INFO |download model and audio
2025-08-08T11:53:32.646717+0800| INFO |本地已存在模型文件，跳过下载: spk_models\v3720p
2025-08-08T11:53:39.997363+0800| INFO |TransDhTask instance creating...
2025-08-08T11:53:50.067712+0800| INFO |模型初始化成功！
2025-08-08T11:53:50.067712+0800| INFO |strat inference ! request_data: {'audio_url': 'workspace\\src_audio\\source.wav', 'video_url': 'workspace\\models\\v3720p\\v3720p_video.mp4', 'code': 'fa3ba59424e711f091782228ff08ca3c', 'watermark_switch': 0, 'speaker_id': 'v3720p', 'is_train': '2', 'digital_auth': 0, 'chaofen': 1, 'pn': 1, 'is_stream': True, 'stream_url': '127.0.0.1:8080'}
2025-08-08T11:53:50.067712+0800| INFO |任务:fa3ba59424e711f091782228ff08ca3c -> audio_url:workspace\src_audio\source.wav  video_url:workspace\models\v3720p\v3720p_video.mp4
2025-08-08T11:53:50.128544+0800| INFO |[fa3ba59424e711f091782228ff08ca3c] 任务开始
2025-08-08T11:53:50.156324+0800| INFO |视频信息保存到 workspace\models\v3720p\video_info.json
2025-08-08T11:53:50.157316+0800| INFO |[fa3ba59424e711f091782228ff08ca3c] -> 获取视频信息耗时:fps:25, width:720, height:1280, fourcc:828601953, total_frames:1126
2025-08-08T11:53:50.158335+0800| INFO |成功加载wh值: 1.0427000803151962
2025-08-08T11:53:50.159335+0800| INFO |【fa3ba59424e711f091782228ff08ca3c】 任务信息: {'work_id': 'fa3ba59424e711f091782228ff08ca3c', 'audio_path': 'workspace\\src_audio\\source.wav', 'drivered_path': 'workspace\\models\\v3720p\\v3720p_video.mp4', 'is_stream': True, 'gfpgan': None, 'fd': None, 'face_blur_detect': None, 'is_train': '2', 'speaker_id': 'v3720p', 'code': 'fa3ba59424e711f091782228ff08ca3c', 'fps': 25, 'wh': 1.0427000803151962, 'total_video_frame': 1126, 'original_frame_shape_init': (1280, 720), 'pn': 1, 'use_npy': True, 'batch_size': 4, 'stream_url': '127.0.0.1:8080'}
2025-08-08T11:54:06.772855+0800| INFO |[fa3ba59424e711f091782228ff08ca3c] result_queue.get() successfully
2025-08-08T11:54:06.776439+0800| ERROR |<class 'h_utils.custom.CustomError'>,stream_dh_service.py,240
2025-08-08T11:54:06.778514+0800| ERROR |error 【fa3ba59424e711f091782228ff08ca3c】 任务异常: 【VidEnc】 VideoEncoder 处理失败: got 2 bytes; need 3840 bytes
2025-08-08T11:54:06.779606+0800| INFO |>>> 任务:fa3ba59424e711f091782228ff08ca3c 耗时:16.651062488555908 
