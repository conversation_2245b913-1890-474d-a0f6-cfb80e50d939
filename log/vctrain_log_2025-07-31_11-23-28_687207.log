2025-07-31T11:23:28.689207+0800| INFO |

**************************************************************************************************
**************************************************************************************************
**************************************************************************************************

2025-07-31T11:23:28.690209+0800| INFO |backend_id : 1917169992917164033	 job_id ： fa3ba59424e711f091782228ff08ca3c	  job_params:{'algorithm': 'digitalhuman2dtrainv2', 'callback_url': 'http://172.16.100.21:8100/creator-openv-api/v3/anchor/digitalhuman2dZerotrain/callback/1917169992917164033', 'video_url': 'v3720p.mp4', 'audio_url': '11.wav', 'is_stream': True, 'speaker_id': 'v3720p', 'blend_threshold': 0.0, 'blend_sigma': 35.0, 'blend_kernel_size': 15, 'task_type': 'stream', 'stream_url': 'rtp://127.0.0.1:1234'}
2025-07-31T11:23:28.691209+0800| INFO |本地接收到使用Production环境的传参，将系统参数Production注入！
2025-07-31T11:23:28.692207+0800| INFO |WOM_AI_ENVIRONMENT 配置为：Production，使用正式环境的对象存储
2025-07-31T11:23:28.692207+0800| INFO |重新连接 ：J70ORSJ6RP77IL8ICOFS 环境的oss
2025-07-31T11:23:29.040767+0800| INFO |oss 链接成功
2025-07-31T11:23:29.041769+0800| INFO |WOM_AI_ENVIRONMENT 配置为：Production，使用正式环境的对象存储
2025-07-31T11:23:29.041769+0800| INFO |重新连接模型保存路径 ：Production 环境的oss
2025-07-31T11:23:30.289750+0800| INFO |download model and audio
2025-07-31T11:23:30.291751+0800| INFO |本地已存在模型文件，跳过下载: spk_models\v3720p
2025-07-31T11:23:37.177991+0800| INFO |TransDhTask instance creating...
