2025-07-11T15:14:19.227945+0800| INFO |

**************************************************************************************************
**************************************************************************************************
**************************************************************************************************

2025-07-11T15:14:19.228949+0800| INFO |backend_id : 1917169992917164033	 job_id ： fa3ba59424e711f091782228ff08ca3c	  job_params:{'algorithm': 'digitalhuman2dtrainv2', 'callback_url': 'http://172.16.100.21:8100/creator-openv-api/v3/anchor/digitalhuman2dZerotrain/callback/1917169992917164033', 'video_url': 'v3720p.mp4', 'audio_url': '11.wav', 'is_stream': True, 'speaker_id': 'v3720p', 'blend_threshold': 0.0, 'blend_sigma': 35.0, 'blend_kernel_size': 15, 'task_type': 'stream', 'stream_url': 'rtp://127.0.0.1:1234'}
2025-07-11T15:14:19.229457+0800| INFO |本地接收到使用Production环境的传参，将系统参数Production注入！
2025-07-11T15:14:19.230462+0800| INFO |WOM_AI_ENVIRONMENT 配置为：Production，使用正式环境的对象存储
2025-07-11T15:14:19.231460+0800| INFO |重新连接 ：J70ORSJ6RP77IL8ICOFS 环境的oss
2025-07-11T15:14:19.588262+0800| INFO |oss 链接成功
2025-07-11T15:14:19.589758+0800| INFO |WOM_AI_ENVIRONMENT 配置为：Production，使用正式环境的对象存储
2025-07-11T15:14:19.589758+0800| INFO |重新连接模型保存路径 ：Production 环境的oss
2025-07-11T15:14:20.915483+0800| INFO |download model and audio
2025-07-11T15:14:20.916484+0800| INFO |本地已存在模型文件，跳过下载: spk_models\v3720p
2025-07-11T15:14:27.792149+0800| INFO |TransDhTask instance creating...
2025-07-11T15:14:42.850003+0800| INFO |模型初始化成功！
2025-07-11T15:14:42.851787+0800| INFO |strat inference ! request_data: {'audio_url': 'workspace\\src_audio\\source.wav', 'video_url': 'workspace\\models\\v3720p\\v3720p_video.mp4', 'code': 'fa3ba59424e711f091782228ff08ca3c', 'watermark_switch': 0, 'speaker_id': 'v3720p', 'is_train': '2', 'digital_auth': 0, 'chaofen': 1, 'pn': 1, 'is_stream': True, 'stream_url': 'rtp://127.0.0.1:1234'}
2025-07-11T15:14:42.853294+0800| INFO |任务:fa3ba59424e711f091782228ff08ca3c -> audio_url:workspace\src_audio\source.wav  video_url:workspace\models\v3720p\v3720p_video.mp4
2025-07-11T15:14:42.919560+0800| INFO |[fa3ba59424e711f091782228ff08ca3c] 任务开始
2025-07-11T15:14:42.945138+0800| INFO |视频信息保存到 workspace\models\v3720p\video_info.json
2025-07-11T15:14:42.946137+0800| INFO |[fa3ba59424e711f091782228ff08ca3c] -> 获取视频信息耗时:fps:25, width:720, height:1280, fourcc:828601953, total_frames:1126
2025-07-11T15:14:42.947137+0800| INFO |成功加载wh值: 1.0427000803151962
