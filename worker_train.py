# -*-coding: utf-8 -*-
# @Time :2025-04-02
# <AUTHOR> lishengjing
# @Email : <EMAIL>
# @File : worker.py
# =================================================================================================
# 必须保存为worker.py.
# 必须定义全局变量：JOB_ALGORITHM，其为平台管理员向任务调度中心注册的算法名称.
# 必须实现函数：Work(job_id, parameters)，其为任务调度接口，job_id为任务的Id, job_params对应API接口参数
#              (Json格式)，函数返回Status(True/False)和Message(Dict格式，用于日志存储).
# -------------------------------------------------------------------------------------------------
import os,sys,requests,uuid,json,subprocess,cv2
import time,datetime
import shutil
from tqdm import tqdm
from loguru import logger
from lt_utils.check_params import Check_params
from lt_utils.wo_common import del_files,upload_file,check_mp4,get_first_frame_from_video
from lt_utils.log_msg import logger_error
from lt_utils.process_download import auto_download_one_file,download_train_files
from lt_utils.media_type_detector import identify_file
from lt_utils.cmd_process import start_cmd
from service.stream_dh_service import TransDhTask
from run_local import easy_submit,easy_query

HOST_NAME= 'default_host_'
try:
    HOST_NAME = "{}_".format(os.uname().nodename)
except Exception as E:
    logger_error(E)


JOB_ALGORITHM = 'digitalhuman2dtrainv2'


class create_logfile():
    def __init__(self,logdir='log'):
        self.logdir = logdir
        self.is_run = False
    
    def get_logfile(self,logdir='log'):
        logfile = os.path.join(logdir, "vctrain_log_{time}.log")
        os.makedirs(logdir, exist_ok=True)
        # rotation: 500 MB,12:00,1 week...
        logger.add(logfile, level='DEBUG', format="{time}| {level} |{message}", enqueue=True, rotation='2 week',
                colorize=True)
    def run(self):
        self.get_logfile(self.logdir)
        self.is_run = True

logfile_instance = create_logfile(logdir='log')
oss_server =None


class Var():
    def __init__(self):
        pass

def work_process(job_id, job_params):
    return Work(job_id, job_params)


def Work(job_id, job_params):
    try:
        # 初始化日志文件，避免多进程开启多个日志文件
        if not logfile_instance.is_run:
            logfile_instance.run()

        logger.info(
                "\n\n" + "**************************************************************************************************\n" * 3)
        t0 = time.time()
        callback_id = os.path.split(job_params.get('callback_url', None))[-1] if job_params.get('callback_url', None) is not None else  '12700001'
        logger.info("backend_id : {}\t job_id ： {}\t  job_params:{}".format(callback_id,job_id,job_params))
        MSG = ""
        # 检查参数
        params = Check_params(job_id,job_params,JOB_ALGORITHM)
        if not params.is_check:
            params.callback_no_success(False,HOST_NAME+params.job_msg)
            return False,params.job_msg
            
        # todo  删除上次任务的文件
        try:
            del_files(params.workspace)
        except Exception as e:
            logger_error(e)
        
        ################################################################下载视频和音频########################################################
        download_dir = os.path.join(params.workspace,'models',params.speaker_id)
        os.makedirs(download_dir,exist_ok=True)
        t_download_start = time.time()
        is_success,media_file,msg = download_train_files(params.video_url,params.mask_url,params.audio_url,params.speaker_id,download_dir,start_cmd)
        if not is_success:
            params.callback_no_success(False,HOST_NAME+msg)
            return False,msg
        MSG += "download time: {:.3f}s  ".format(time.time()-t_download_start)

        # 转换成mp4
        video_file = media_file.get('video',None)
        is_success,video_file,msg = check_mp4(video_file,start_cmd,logger_error,identify_file)
        if not is_success:
            params.callback_no_success(False,HOST_NAME+"训练视频格式错误:"+msg) 
            return False,HOST_NAME+"训练视频格式错误:"+msg
        media_file['video'] = video_file
        
        params_json = {
            "version": "v2",
            "job_id": job_id,
            "job_params": job_params
        }
        
        ################################################################ 保存任务参数 ################################################################
        params_json_path = os.path.join(download_dir, 'params.json')
        with open(params_json_path, 'w', encoding='utf-8') as f:
            json.dump(params_json, f, ensure_ascii=False, indent=4)
            
        logger.info(f"保存任务参数到 {params_json_path}")


        ################################################################ 开始推理 ################################################################
        t2 = time.time()
        is_train = '1' # 训练模式
        if 'trans_dh_task' not in locals() or trans_dh_task is None:
            trans_dh_task = TransDhTask.instance()
            time.sleep(15)
            logger.info('模型初始化成功！')


        request_data = {
            'audio_url':media_file['audio'],
            'video_url':media_file['video'],
            'code':job_id,
            'watermark_switch':0,
            'speaker_id':params.speaker_id,
            'is_train':is_train,
            'digital_auth':0,
            'chaofen':1,
            'pn':1
        }
        logger.info("strat inference ! request_data: {}".format(request_data))
        is_success,return_data = easy_submit(request_data) #多进程处理，需要返回数据
        if not is_success:
            params.callback_no_success(is_success,HOST_NAME + return_data)
            return False,return_data
        # 任务查询
        return_data = easy_query(request_data)  # 堵塞，返回string
        logger.info("query result: {}".format(return_data))
        t3 = time.time()
        MSG += " inference  time: {:.3f}s  ".format(t3-t2)
        # 获取推理结果
        # {"code": 10000,"data": {"code": "fd01c7b4ae7611ef9fcf6e9e21b189dd","msg": "任务完成","progress": 100,"
        # result": "./workspace\\fd01c7b4ae7611ef9fcf6e9e21b189dd-r.mp4","status": 2},    "msg": "",    "success": true
        return_data_json = json.loads(return_data)
        code = return_data_json.get('code',0)
        _data = return_data_json.get('data',{})
        result = _data.get('result',"")
        status = _data.get('status',0)
        _msg = "success!"
        is_success= True
        if status not in [2] and code == 10000:
            _msg = _data.get('msg',"")
            is_success = False
            params.callback_no_success(is_success,HOST_NAME + _msg)
            return False,return_data
        
        ################################################################提取首帧图片########################################################
        is_success,preview_img_path,msg = get_first_frame_from_video(media_file["video"],download_dir,logger_error)
        if not is_success:
            params.callback_no_success(False,HOST_NAME + msg)
            return False,msg
        
        ################################################################ 压缩视频 ################################################################
        # 定义源目录和目标压缩文件的路径
        output_zip = os.path.join(params.workspace,params.speaker_id+".zip")
        # 使用shutil.make_archive进行压缩
        shutil.make_archive(output_zip.replace('.zip', ''), 'zip', download_dir)
        logger.info(f"目录 {download_dir} 已成功压缩为 {output_zip}")
        MSG += "zip time: {:.3f}s  ".format(time.time()-t_download_start)
        # 拷贝模型到本地
        try:
            local_model_dir = os.path.join(params.spk_models,params.speaker_id)
            logger.info(f"拷贝模型到本地{download_dir} -- > {local_model_dir}")
            if os.path.exists(local_model_dir): # 如果本地模型存在，则删除
                shutil.rmtree(local_model_dir)
            # os.makedirs(local_model_dir,exist_ok=True)
            shutil.copytree(download_dir,local_model_dir)
        except Exception as e:
            logger_error(e)

        ################################################################ 上传文件 ################################################################
        if 'oss_server' not in locals() or oss_server is None:
            from lt_utils.wo_oss import oss_server,auto_switch_oss
        # result_oss_dir = (f'speaker_models_{params.version}/{params.speaker_id}/{params.speaker_id}.zip')
        result_oss_dir = f'speaker_models_{params.version}/{params.speaker_id}/'
        is_success, _msg, media_oss_path = upload_file(params,output_zip,oss_server,auto_switch_oss,result_oss_dir)
        if not is_success:
            params.callback_no_success(is_success,HOST_NAME +_msg)  # 调用call_back 函数
            return is_success,_msg
          # 上传预览图片
        is_success, _msg, preview_oss_path = upload_file(params,preview_img_path,oss_server,auto_switch_oss,result_oss_dir)
        if not is_success:
            params.callback_no_success(is_success,HOST_NAME +_msg)  # 调用call_back 函数
            return is_success,_msg
        MSG += "  upload time: {:.3f}s".format(time.time()-t_download_start)  

        ################################################################ 回调/结束 ################################################################
        params._C.update({'speaker_id':params.speaker_id,'preview_img':preview_oss_path})
        params.callbacks()  # 调用call_back 函数
        logger.info("###############【{}】###############".format(MSG))

        return is_success,"success"

    except Exception as e:
        logger_error(e)
        logger.error("Error: Call back failed!\n")
        return False, "failed!"




if __name__ == '__main__':

    if True:
        job_params = {
            "callback_url" :'http://*************:8100/creator-open-api/v3/anchor/digitalhuman2dtrain/callback/1875016211187322882',
            "video_url" : 'walk.mp4',
            'mask_url':'',
            'speaker_id':'7bcb1f55c7cb435fa56',
            'additional': {
                'WOM_AI_ENVIRONMENT': 'Development',   # ["Debug", "Development","Production"] 用来切换不同的环境对象存储
                'is_debugging':False,
                'is_keep_train_dir':False,
                "version":'v2'
                } ,
            'use_mask': None, 'blend_threshold': 0.0, 'blend_sigma': 35.0, 'blend_kernel_size': 15
        }


        for i in range(1):
            print(Work(job_id='7bcb1f55c7cb435fa567', job_params=job_params))
