#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修正后的AudioExtractor（重叠窗口版本）
验证与get_aud_feat1的一致性
"""

import os
import sys
import time
import queue
import threading
import numpy as np
from loguru import logger
import librosa

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 直接导入需要的函数
from wenet.tools._extract_feats import wav2mfcc_v2


def logger_error(e):
    """错误日志记录"""
    import traceback
    logger.error(f"错误: {e}")
    logger.error(f"堆栈跟踪: {traceback.format_exc()}")


class MockAudioExtractorOverlap:
    """
    模拟修正后的AudioExtractor（重叠窗口版本）
    """
    
    def __init__(self, feature_queue, audio_stream_queue, fps=25):
        self.work_id_for_logging = "MockAudioExtractorOverlap"
        self.feature_queue = feature_queue
        self.audio_stream_queue = audio_stream_queue
        self.fps = fps
        
        # 计算音频处理参数
        self.sample_rate = 16000
        self.chunk_samples = 1280  # 80ms = 1280样本
        self.samples_per_video_frame = self.sample_rate // self.fps  # 每视频帧音频样本数
        
        # Mel特征提取参数
        self.mel_params = {
            'sample_rate': 16000,
            'preemphasis': 0.97,
            'n_fft': 1024,
            'hop_length': 160,
            'win_length': 800,
            'num_mels': 80,
            'n_mfcc': 13,
            'window': 'hann',
            'fmin': 0.0,
            'fmax': 8000.0,
            'ref_db': 20,
            'min_db': -80.0,
            'center': True
        }
        
        # 重叠窗口处理参数
        self.window_size = 3200  # 200ms窗口，足够生成20+帧mel特征
        self.hop_size = 640  # 40ms跳跃，与视频帧率同步
        self.context_frames = 10  # 上下文帧数，用于去除边界效应
        
        self._is_running = False
        
        logger.info(f"MockAudioExtractorOverlap初始化: fps={fps}")
        logger.info(f"重叠窗口配置: window_size={self.window_size}, hop_size={self.hop_size}, context_frames={self.context_frames}")
    
    def extract_mel_features(self, audio_data):
        """
        提取mel特征
        """
        try:
            mel_features, x_stft = wav2mfcc_v2(
                audio_data,
                sr=self.mel_params["sample_rate"],
                n_mfcc=self.mel_params["n_mfcc"],
                n_fft=self.mel_params["n_fft"],
                hop_len=self.mel_params["hop_length"],
                win_len=self.mel_params["win_length"],
                window=self.mel_params["window"],
                num_mels=self.mel_params["num_mels"],
                center=self.mel_params["center"]
            )
            
            return {
                'log_mel_spec': mel_features,
                'stft': x_stft,
                'audio_length': len(audio_data),
                'mel_shape': mel_features.shape,
                'processing_method': 'wav2mfcc_v2'
            }
            
        except Exception as e:
            logger.error(f"mel特征提取失败: {e}")
            logger_error(e)
            return None
    
    def run_flow_audio_with_wenet(self):
        """
        修正后的流式音频mel特征处理逻辑（重叠窗口版本）
        """
        logger.info(f"【{self.work_id_for_logging}】 开始流式音频mel特征处理.")
        self._is_running = True
        
        # 音频处理参数
        audio_buffer = np.array([])
        
        # 重叠窗口处理参数
        self.overlap_buffer = np.array([])
        self.valid_features_buffer = []
        self.is_first_data = True

        while self._is_running:
            try:
                status, audio_chunk = self.audio_stream_queue.get(block=True)
                
                if status == "data" and audio_chunk:
                    # 将音频字节转换为numpy数组
                    audio_segment = np.frombuffer(audio_chunk, dtype=np.int16).astype(np.float32) / 32768.0
                    
                    # 如果是第一次接收data状态，添加初始静音填充
                    if self.is_first_data:
                        logger.info(f"【{self.work_id_for_logging}】 第一次接收data，添加初始静音填充")
                        zero_padding = np.zeros(6400)  # 0.4秒静音填充
                        audio_segment = np.concatenate([zero_padding, audio_segment])
                        self.is_first_data = False
                    
                    # 添加到重叠缓冲区
                    self.overlap_buffer = np.concatenate([self.overlap_buffer, audio_segment.flatten()])
                    
                    # 使用重叠窗口处理音频
                    self._process_overlap_windows()
                    
                elif status == "start":
                    # 重置所有缓冲区和状态
                    audio_buffer = np.array([])
                    self.overlap_buffer = np.array([])
                    self.valid_features_buffer = []
                    self.is_first_data = True
                    self.feature_queue.put(("start", None, None))
                    logger.info(f"【{self.work_id_for_logging}】 流式mel特征处理开始，所有缓冲区已重置.")
                    
                elif status == "silence":
                    logger.info(f"【{self.work_id_for_logging}】 音频段结束信号接收. 处理剩余数据...")
                    
                    # 处理剩余的重叠窗口
                    self._process_remaining_overlap_windows(is_final=False)
                    
                    # 发送累积的有效特征
                    self._send_accumulated_features()
                    
                    self.feature_queue.put(("silence", None, None))
                    # 重置缓冲区但保持状态
                    self.overlap_buffer = np.array([])
                    self.valid_features_buffer = []
                    logger.info(f"【{self.work_id_for_logging}】 静音处理完成，缓冲区已重置.")
                    
                elif status == "end":
                    logger.info(f"【{self.work_id_for_logging}】 音频流结束信号接收. 最终处理...")
                    
                    # 添加末尾静音填充
                    if len(self.overlap_buffer) > 0:
                        zero_padding = np.zeros(6400)  # 0.4秒静音填充
                        self.overlap_buffer = np.concatenate([self.overlap_buffer, zero_padding])
                    
                    # 处理所有剩余的重叠窗口
                    self._process_remaining_overlap_windows(is_final=True)
                    
                    # 发送累积的有效特征
                    self._send_accumulated_features()
                    
                    self.feature_queue.put(("end", None, None))
                    self._is_running = False
                    
            except queue.Empty:
                logger.warning(f"【{self.work_id_for_logging}】 等待音频块超时. 处理剩余数据...")
                self._process_remaining_overlap_windows(is_final=True)
                self._send_accumulated_features()
                self._is_running = False
                break
                
            except Exception as e:
                logger_error(f"【{self.work_id_for_logging}】 流式mel特征处理循环错误: {e}")
                self._is_running = False
                break

    def _process_overlap_windows(self):
        """
        使用重叠窗口处理音频，去除边界效应
        """
        while len(self.overlap_buffer) >= self.window_size:
            # 提取一个窗口的音频数据
            window_audio = self.overlap_buffer[:self.window_size]
            
            # 提取mel特征
            mel_result = self.extract_mel_features(window_audio)
            
            if mel_result is not None:
                mel_features = mel_result['log_mel_spec']
                
                # 去除边界效应：只保留中间的有效帧
                if mel_features.shape[0] > 2 * self.context_frames:
                    valid_mel = mel_features[self.context_frames:-self.context_frames]
                    
                    # 将有效特征添加到缓冲区
                    self.valid_features_buffer.append(valid_mel)
                    
                    logger.info(f"【{self.work_id_for_logging}】 处理窗口: 原始={mel_features.shape[0]}帧, 有效={valid_mel.shape[0]}帧")
                else:
                    logger.warning(f"【{self.work_id_for_logging}】 窗口mel特征帧数不足: {mel_features.shape[0]} < {2 * self.context_frames}")
            
            # 滑动窗口
            self.overlap_buffer = self.overlap_buffer[self.hop_size:]
            
            # 检查是否有足够的特征可以发送
            self._check_and_send_features()

    def _check_and_send_features(self):
        """
        检查是否有足够的特征可以发送（20帧窗口）
        """
        # 计算总的有效特征帧数
        total_frames = sum(mel.shape[0] for mel in self.valid_features_buffer)
        
        # 当有足够的特征时，按20帧窗口发送
        win_size = 20
        while total_frames >= win_size:
            # 连接所有有效特征
            if len(self.valid_features_buffer) > 0:
                concatenated_features = np.concatenate(self.valid_features_buffer, axis=0)
                
                # 提取20帧窗口
                feature_window = concatenated_features[:win_size]
                
                # 发送特征窗口
                self.feature_queue.put(("data", feature_window.tolist(), None))
                logger.info(f"【{self.work_id_for_logging}】 发送特征窗口: 形状={feature_window.shape}")
                
                # 移除已发送的帧，保留剩余特征
                remaining_features = concatenated_features[win_size:]
                
                # 重新组织缓冲区
                self.valid_features_buffer = [remaining_features] if remaining_features.shape[0] > 0 else []
                
                # 重新计算总帧数
                total_frames = sum(mel.shape[0] for mel in self.valid_features_buffer)
            else:
                break

    def _process_remaining_overlap_windows(self, is_final=False):
        """
        处理剩余的重叠窗口
        """
        # 如果是最终处理且缓冲区不足一个完整窗口，用静音填充
        if is_final and len(self.overlap_buffer) > 0:
            if len(self.overlap_buffer) < self.window_size:
                padding_needed = self.window_size - len(self.overlap_buffer)
                padding = np.zeros(padding_needed, dtype=np.float32)
                self.overlap_buffer = np.concatenate([self.overlap_buffer, padding])
                logger.info(f"【{self.work_id_for_logging}】 最终处理：添加静音填充 {padding_needed} 样本")
        
        # 处理剩余的窗口
        self._process_overlap_windows()

    def _send_accumulated_features(self):
        """
        发送累积的有效特征
        """
        if len(self.valid_features_buffer) > 0:
            # 连接所有剩余特征
            concatenated_features = np.concatenate(self.valid_features_buffer, axis=0)
            
            # 按20帧窗口发送剩余特征
            win_size = 20
            for i in range(0, concatenated_features.shape[0], win_size):
                if i + win_size <= concatenated_features.shape[0]:
                    feature_window = concatenated_features[i:i + win_size]
                    self.feature_queue.put(("data", feature_window.tolist(), None))
                    logger.info(f"【{self.work_id_for_logging}】 发送剩余特征窗口: 形状={feature_window.shape}")
                else:
                    # 最后不足20帧的部分，如果超过10帧也发送
                    remaining_frames = concatenated_features.shape[0] - i
                    if remaining_frames >= 10:
                        feature_window = concatenated_features[i:]
                        self.feature_queue.put(("data", feature_window.tolist(), None))
                        logger.info(f"【{self.work_id_for_logging}】 发送最后的特征窗口: 形状={feature_window.shape}")
            
            # 清空缓冲区
            self.valid_features_buffer = []


def test_overlap_audioextractor():
    """
    测试重叠窗口版本的AudioExtractor
    """
    audio_file = "11_16k.wav"
    fps = 25
    
    logger.info("🚀 开始测试重叠窗口版本的AudioExtractor")
    logger.info(f"音频文件: {audio_file}")
    logger.info(f"视频帧率: {fps} fps")
    
    try:
        # 创建队列
        feature_queue = queue.Queue()
        audio_stream_queue = queue.Queue()
        
        # 创建MockAudioExtractorOverlap实例
        extractor = MockAudioExtractorOverlap(
            feature_queue=feature_queue,
            audio_stream_queue=audio_stream_queue,
            fps=fps
        )
        
        # 启动AudioExtractor线程
        extractor_thread = threading.Thread(target=extractor.run_flow_audio_with_wenet)
        extractor_thread.daemon = True
        extractor_thread.start()
        
        # 模拟音频流输入
        logger.info("开始模拟音频流输入...")
        
        # 读取音频文件
        audio_data, sr = librosa.load(audio_file, sr=16000)
        logger.info(f"音频文件: 长度={len(audio_data)}, 采样率={sr}, 时长={len(audio_data)/sr:.2f}秒")
        
        # 发送开始信号
        audio_stream_queue.put(("start", None))
        time.sleep(0.1)
        
        # 模拟不同长度的音频块输入
        chunk_sizes = [
            int(sr * 0.05),   # 50ms
            int(sr * 0.08),   # 80ms
            int(sr * 0.12),   # 120ms
            int(sr * 0.1),    # 100ms
            int(sr * 0.06),   # 60ms
        ]
        
        current_pos = 0
        chunk_idx = 0
        
        while current_pos < len(audio_data):
            # 选择当前块大小
            chunk_size = chunk_sizes[chunk_idx % len(chunk_sizes)]
            chunk_idx += 1
            
            # 提取音频块
            chunk = audio_data[current_pos:current_pos + chunk_size]
            current_pos += chunk_size
            
            # 转换为int16格式
            chunk_int16 = (chunk * 32768.0).astype(np.int16)
            chunk_bytes = chunk_int16.tobytes()
            
            # 发送音频数据
            audio_stream_queue.put(("data", chunk_bytes))
            logger.info(f"发送音频块: {len(chunk)}样本 ({len(chunk)/sr*1000:.1f}ms)")
            
            # 模拟实时流式处理的延迟
            time.sleep(0.02)  # 20ms延迟
        
        # 发送结束信号
        time.sleep(0.1)
        audio_stream_queue.put(("end", None))
        
        # 等待处理完成
        extractor_thread.join(timeout=30)
        
        # 收集结果
        features = []
        while not feature_queue.empty():
            try:
                item = feature_queue.get_nowait()
                features.append(item)
            except queue.Empty:
                break
        
        # 分析结果
        logger.info("=" * 60)
        logger.info("重叠窗口版本AudioExtractor测试结果")
        logger.info("=" * 60)
        logger.info(f"总特征数量: {len(features)}")
        
        # 分析不同状态的特征
        start_count = len([f for f in features if f[0] == "start"])
        data_count = len([f for f in features if f[0] == "data"])
        silence_count = len([f for f in features if f[0] == "silence"])
        end_count = len([f for f in features if f[0] == "end"])
        
        logger.info(f"状态统计: start={start_count}, data={data_count}, silence={silence_count}, end={end_count}")
        
        if data_count > 0:
            data_features = [f for f in features if f[0] == "data"]
            first_feature = np.array(data_features[0][1])
            
            # 分析特征窗口大小
            feature_shapes = [np.array(f[1]).shape for f in data_features]
            unique_shapes = list(set(feature_shapes))
            logger.info(f"特征窗口形状: {unique_shapes}")
            logger.info(f"第一个特征形状: {first_feature.shape}")
            logger.info(f"第一个特征范围: [{first_feature.min():.3f}, {first_feature.max():.3f}]")
            
            # 统计20帧窗口的数量
            win20_count = len([f for f in data_features if np.array(f[1]).shape[0] == 20])
            logger.info(f"20帧窗口数量: {win20_count}/{data_count}")
            
            logger.info("✅ 重叠窗口版本AudioExtractor测试完成!")
            
        else:
            logger.error("❌ 没有提取到特征")
            
    except Exception as e:
        logger.error(f"测试过程中出现错误: {e}")
        logger_error(e)


if __name__ == "__main__":
    test_overlap_audioextractor()
