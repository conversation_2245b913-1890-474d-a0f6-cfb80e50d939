# WebRTC 问题解决指南

## 问题现象
1. 播放出来的画面比较卡，也有马赛克
2. 播放一小会就会自己断开

## 问题原因分析

### 1. 码率设置过高
- **问题**: 视频码率过高导致网络拥塞
- **表现**: 日志中显示大量丢包 `packets_lost=2196`, `packets_lost=8350`
- **解决**: 降低视频码率到1Mbps以下

### 2. MTU设置不当
- **问题**: 网络MTU设置过大导致分片
- **表现**: 视频帧大小1300字节，音频帧3字节
- **解决**: 设置MTU为1200字节

### 3. WebRTC配置缺失
- **问题**: 缺少ICE服务器、编码参数等配置
- **表现**: ICE连接状态变化频繁，DTLS传输断开
- **解决**: 添加完整的WebRTC配置

### 4. 协议不匹配
- **问题**: 服务器使用RTP推流，客户端使用WebRTC播放
- **表现**: 连接失败或无法接收媒体流
- **解决**: 统一使用WebRTC协议

## 解决方案

### 1. 使用优化的播放器
```bash
# 运行优化的WebRTC播放器
python scripts/webrtc_player.py

# 或者使用批处理脚本
scripts/start_webrtc_test.bat
```

### 2. 运行连接测试
```bash
# 测试WebRTC连接
python scripts/test_webrtc_connection.py
```

### 3. 运行网络诊断
```bash
# 诊断网络问题
python scripts/webrtc_diagnostics.py
```

### 4. 优化系统网络设置
```bash
# 在Windows上优化网络参数
netsh interface tcp set global autotuninglevel=normal
netsh interface tcp set global chimney=enabled
netsh interface tcp set global ecncapability=enabled
```

## 配置参数说明

### 视频编码参数
- **分辨率**: 1280x720 (720p)
- **帧率**: 25fps
- **码率**: 1Mbps
- **编码格式**: H.264

### 音频编码参数
- **采样率**: 16kHz
- **声道数**: 1 (单声道)
- **码率**: 64kbps
- **编码格式**: Opus

### 网络参数
- **MTU**: 1200字节
- **ICE服务器**: Google STUN服务器
- **队列大小**: 4帧缓冲

## 性能优化建议

### 1. 降低码率
```python
# 在 vid_encoder.py 中设置
self._max_video_bitrate = 1000000  # 1Mbps
self._max_audio_bitrate = 64000    # 64kbps
```

### 2. 优化队列大小
```python
# 减少队列大小以降低延迟
self._video_track = MediaRelayTrack(kind="video", maxsize=4)
self._audio_track = MediaRelayTrack(kind="audio", maxsize=4)
```

### 3. 添加帧率控制
```python
# 避免发送过快
try:
    await self._video_track.queue_frame(video_frame)
except asyncio.QueueFull:
    logger.warning("视频队列已满，跳过当前帧")
```

## 故障排除步骤

### 1. 检查服务器配置
确保 `worker.py` 中使用正确的WebRTC配置：
```python
'stream_url': '127.0.0.1:8080',  # WebRTC信令服务器
'streamer_type': 'webrtc'         # 使用WebRTC推流器
```

### 2. 检查网络连接
```bash
# 测试STUN服务器
ping stun.l.google.com
ping stun1.l.google.com
```

### 3. 检查端口可用性
```bash
# 检查WebRTC使用的端口
netstat -an | findstr :8080
```

### 4. 检查系统资源
```bash
# 检查CPU和内存使用率
tasklist /fi "imagename eq python.exe"
```

### 5. 查看详细日志
```python
# 启用详细日志
logging.getLogger("aiortc").setLevel(logging.DEBUG)
logging.getLogger("aioice").setLevel(logging.DEBUG)
```

## 常见问题解决

### Q1: 画面卡顿怎么办？
**A**: 
1. 降低视频码率到1Mbps以下
2. 使用720p分辨率
3. 检查网络带宽是否足够

### Q2: 连接频繁断开怎么办？
**A**: 
1. 检查STUN服务器连接
2. 确保防火墙没有阻止WebRTC流量
3. 使用有线网络而不是WiFi

### Q3: 音频延迟怎么办？
**A**: 
1. 降低音频队列大小
2. 使用20ms音频块
3. 优化音频编码参数

### Q4: 马赛克现象怎么办？
**A**: 
1. 降低视频码率
2. 增加网络缓冲区
3. 检查网络丢包率

### Q5: 无法连接到信令服务器怎么办？
**A**: 
1. 检查服务器是否启动
2. 确认端口8080是否被占用
3. 运行连接测试脚本

### Q6: 播放器启动失败怎么办？
**A**: 
1. 检查Python环境
2. 安装必要的包：`pip install aiortc aiohttp opencv-python pyaudio`
3. 使用批处理脚本自动检查环境

## 监控指标

### 关键指标
- **视频FPS**: 目标25fps
- **音频延迟**: 目标<100ms
- **网络丢包率**: 目标<1%
- **CPU使用率**: 目标<80%
- **内存使用率**: 目标<80%

### 日志关键词
- `packets_lost`: 丢包数量
- `jitter`: 网络抖动
- `connectionstatechange`: 连接状态变化
- `iceconnectionstatechange`: ICE连接状态

## 测试步骤

### 1. 环境检查
```bash
# 运行环境检查
scripts/start_webrtc_test.bat
```

### 2. 连接测试
```bash
# 测试WebRTC连接
python scripts/test_webrtc_connection.py
```

### 3. 网络诊断
```bash
# 诊断网络问题
python scripts/webrtc_diagnostics.py
```

### 4. 完整测试
1. 启动数字人系统：`python worker.py`
2. 启动WebRTC播放器：`python scripts/webrtc_player.py`
3. 观察播放效果和日志输出

## 联系支持

如果问题仍然存在，请提供以下信息：
1. 网络诊断报告 (`webrtc_diagnostics_*.txt`)
2. 详细的错误日志
3. 系统配置信息
4. 网络环境描述
5. 测试脚本的输出结果 