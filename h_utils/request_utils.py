# uncompyle6 version 3.9.2
# Python bytecode version base 3.8.0 (3413)
# Decompiled from: Python 3.10.9 | packaged by Anaconda, Inc. | (main, Mar  1 2023, 18:18:15) [MSC v.1916 64 bit (AMD64)]
# Embedded file name: /code/h_utils/request_utils.py
# Compiled at: 2024-04-09 17:54:37
# Size of source mod 2**32: 4394 bytes
"""
@project : dhp-tools
<AUTHOR> huyi
@file   : request_utils.py
@ide    : PyCharm
@time   : 2021-09-03 16:00:32
"""
import json, os, sys, time, requests
import y_utils.logger as logger

def request_post(url, param, timeout = (10,)):
    result = 0
    fails = 0
# WARNING: Decompyle incomplete

def download(url, file_path):
    count = 0
    r1 = requests.get(url, stream=True, verify=False, timeout=(20, 20))
    total_size = int(r1.headers["Content-Length"])
    if os.path.exists(file_path):
        temp_size = os.path.getsize(file_path)
    else:
        temp_size = 0
    print(temp_size)
    print(total_size)
    r1.close
    while count < 10:
        if count != 0:
            temp_size = os.path.getsizefile_path
        if temp_size >= total_size:
            break
        count += 1
        logger.info("第[{}]次下载文件,已经下载数据大小:[{}],未下载数据大小:[{}]".format(count, temp_size, total_size))
        headers = {"Range": f"bytes={temp_size}-{total_size}"}
        r = requests.get(url, stream=True, verify=False, headers=headers)
        with open(file_path, "ab") as f:
            for chunk in r.iter_content(chunk_size=65536):
                if count != 1:
                    f.seektemp_size

            if chunk:
                temp_size += len(chunk)
                f.writechunk
                f.flush
                done = int(50 * temp_size / total_size)
                sys.stdout.write("\r[%s%s] %d%%" % (
                 "█" * done, " " * (50 - done), 100 * temp_size / total_size))
                sys.stdout.flush
        print("\n")
        r.close

    return file_path


def download_file(file_url, local_path):
    chunk_size = 4096
    lst_size = 0
    total_size = 0
    with requests.get(file_url, stream=True, verify=False, timeout=1200) as r:
        with open(local_path, "wb") as ff:
            for chunk in r.iter_content(chunk_size=chunk_size):
                if total_size - lst_size > 5242880:
                    logger.info("downloading file %f M" % (total_size / 1048576))
                    lst_size = total_size
                total_size += len(chunk)
                ff.writechunk

    return local_path