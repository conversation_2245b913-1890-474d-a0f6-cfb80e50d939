# Visit https://www.lddgo.net/string/pyc-compile-decompile for more information
# Version : Python 3.7

import torch
from base_model import BaseModel
from models import networks_pix2pixHD as networks

class face3d2faceModel(BaseModel):
    
    def name(self):
        return 'face3d2face_Model'

    @staticmethod
    def modify_commandline_options(parser, is_train = (True,)):
        if is_train:
            parser.set_defaults(pool_size=0, gan_mode='vanilla')
        return parser

    
    def __init__(self, opt):
        BaseModel.__init__(self, opt)
        self.resize_size = opt.img_size
        (self.m_x1, self.m_x2, self.m_y1, self.m_y2) = (100 * self.resize_size // 512, 400 * self.resize_size // 512, 120 * self.resize_size // 512, -120 * self.resize_size // 512)
        self.visual_names = [
            'real_A',
            'fake_B',
            'real_B',
            'mask_B']
        if self.isTrain:
            self.loss_names = [
                'G_GAN',
                'G_L1',
                'G_VGG',
                'D_real',
                'D_fake',
                'G_mouthL1',
                'G_mouthGAN',
                'D_real_m',
                'D_fake_m']
            self.model_names = [
                'G',
                'D']
        else:
            self.model_names = [
                'G']
        self.netG = networks.define_G(6, opt.output_nc, opt.ngf, opt.netG, opt.n_downsample_global, opt.n_blocks_global, opt.n_local_enhancers, opt.n_blocks_local, opt.norm,  gpu_ids=self.gpu_ids, apex=opt.fp16)
        if self.isTrain:
            use_sigmoid = False
            self.netD = networks.define_D(opt.input_nc + opt.output_nc, opt.ndf, opt.n_layers_D, opt.norm, use_sigmoid, opt.num_D, not (opt.no_ganFeat_loss), gpu_ids=self.gpu_ids, apex=opt.fp16)
        if self.isTrain:
            self.criterionVGG = networks.VGGLoss(self.gpu_ids)
            self.criterionGAN = networks.GANLoss(True, torch.cuda.FloatTensor)
            self.criterionL1 = torch.nn.L1Loss()
            self.optimizer_G = torch.optim.Adam(self.netG.parameters(), opt.lr, (opt.beta1, 0.999))
            self.optimizer_D = torch.optim.Adam(self.netD.parameters(), opt.lr, (opt.beta1, 0.999))
            self.optimizers.append(self.optimizer_G)
            self.optimizers.append(self.optimizer_D)
            if self.opt.fp16:
                import apex
                (self.netG, self.netD) = (self.netG.half(), self.netD.half())
                (self.optimizer_G, self.optimizer_D) = apex.amp.initialize([
                    self.netG.to(self.device),
                    self.netD.to(self.device)], [
                    self.optimizer_G,
                    self.optimizer_D],  opt_level='O1')
                if not opt.distributed:
                    self.netG = torch.nn.DataParallel(self.netG, device_ids=opt.gpu_ids)
                    self.netD = torch.nn.DataParallel(self.netD, opt.gpu_ids)
                else:
                    self.netG = apex.parallel.DistributedDataParallel(self.netG, delay_allreduce=True)
                    self.netD = apex.parallel.DistributedDataParallel(self.netD,  delay_allreduce=True)

    
    def set_input(self, input):
        self.real_A = input['A'].to(self.device)
        self.A_label = input['A_label'].to(self.device)
        self.real_B = input['B'].to(self.device)
        self.B_label = input['B_label'].to(self.device)
        self.mask_B = input['mask_B'].to(self.device)

    
    def forward(self):
        self.fake_B = self.netG(self.B_label, torch.cat((self.mask_B, self.real_A), 1))

    
    def backward_D(self):
        fake_AB = torch.cat((self.real_A, self.fake_B), 1)
        pred_fake = self.netD(fake_AB.detach())
        self.loss_D_fake = self.criterionGAN(pred_fake, False)
        real_AB = torch.cat((self.real_A, self.real_B), 1)
        self.pred_real = self.netD(real_AB)
        self.loss_D_real = self.criterionGAN(self.pred_real, True)
        self.loss_D = (self.loss_D_fake + self.loss_D_real) * 0.5
    # WARNING: Decompyle incomplete

    
    def backward_G(self):
        lambda_GAN = 1
        lambda_L1 = 100
        fake_AB = torch.cat((self.real_A, self.fake_B), 1)
        pred_fake = self.netD(fake_AB)
        self.loss_G_GAN = self.criterionGAN(pred_fake, True) * lambda_GAN
        self.loss_G_L1 = self.criterionL1(self.fake_B, self.real_B) * lambda_L1
        m_fake_AB = torch.cat((self.real_A[:, :, self.m_x1:self.m_x2, self.m_y1:self.m_y2], self.fake_B[:, :, self.m_x1:self.m_x2, self.m_y1:self.m_y2]), 1)
        m_pred_fake = self.netD(m_fake_AB)
        self.loss_G_mouthGAN = self.criterionGAN(m_pred_fake, True) * lambda_GAN * 2
        self.loss_G_mouthL1 = self.criterionL1(self.fake_B[:, :, self.m_x1:self.m_x2, self.m_y1:self.m_y2], self.real_B[:, :, self.m_x1:self.m_x2, self.m_y1:self.m_y2]) * lambda_L1 * 2
        self.loss_G_VGG = self.criterionVGG(self.fake_B, self.real_B) * lambda_L1 / 6
        self.loss_G = self.loss_G_GAN + self.loss_G_L1 + self.loss_G_VGG + self.loss_G_mouthL1 + self.loss_G_mouthGAN
    # WARNING: Decompyle incomplete

    
    def optimize_parameters(self):
        self.forward()
        self.set_requires_grad(self.netD, True)
        self.optimizer_D.zero_grad()
        self.backward_D()
        self.optimizer_D.step()
        self.set_requires_grad(self.netD, False)
        self.optimizer_G.zero_grad()
        self.backward_G()
        self.optimizer_G.step()

    
    def eval_(self):
        lambda_GAN = 1
        lambda_L1 = 100
        self.forward()
        fake_AB = torch.cat((self.real_A, self.fake_B), 1)
        pred_fake = self.netD(fake_AB)
        self.loss_G_GAN = self.criterionGAN(pred_fake, True) * lambda_GAN
        self.loss_G_L1 = self.criterionL1(self.fake_B, self.real_B) * lambda_L1
        self.loss_G_VGG = self.criterionVGG(self.fake_B, self.real_B) * lambda_L1 / 6
        self.loss_G_mouthL1 = self.criterionL1(self.fake_B[:, :, self.m_x1:self.m_x2, self.m_y1:self.m_y2], self.real_B[:, :, self.m_x1:self.m_x2, self.m_y1:self.m_y2]) * lambda_L1 * 2