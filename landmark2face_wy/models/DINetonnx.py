# uncompyle6 version 3.9.2
# Python bytecode version base 3.8.0 (3413)
# Decompiled from: Python 3.10.9 | packaged by Anaconda, Inc. | (main, Mar  1 2023, 18:18:15) [MSC v.1916 64 bit (AMD64)]
# Embedded file name: /code/landmark2face_wy/models/DINet.py
# Compiled at: 2024-03-06 18:04:18
# Size of source mod 2**32: 35736 bytes
import torch
from torch import nn
import torch.nn.functional as F
import math, cv2, numpy as np
from landmark2face_wy.sync_batchnorm import SynchronizedBatchNorm2d as BatchNorm2d
from landmark2face_wy.sync_batchnorm import SynchronizedBatchNorm1d as BatchNorm1d
from einops import rearrange, repeat

    
"""
目前版本能转，但是没有验证过效果，需要验证
"""


def make_coordinate_grid_2d_mouth(spatial_size, tensor_for_attributes, align_corners=True):
    """
    Generates a 2D coordinate grid.
    Args:
        spatial_size: tuple (H, W)
        tensor_for_attributes: A tensor from which dtype and device will be derived.
        align_corners: bool, if True, normalize to corners, else to centers.
    Returns:
        A tensor of shape (H, W, 2) with (x, y) coordinates.
    """
    h, w = spatial_size
    dtype = tensor_for_attributes.dtype
    device = tensor_for_attributes.device

    x_range = torch.arange(w, dtype=dtype, device=device)
    y_range = torch.arange(h, dtype=dtype, device=device)

    if align_corners:
        # Normalize to -1 to 1, where -1 and 1 are at the centers of the corner pixels
        x_norm = (2 * x_range / (w - 1) - 1) if w > 1 else torch.zeros_like(x_range)
        y_norm = (2 * y_range / (h - 1) - 1) if h > 1 else torch.zeros_like(y_range)
    else:
        # Normalize to -1 to 1, where -1 and 1 are at the edges of the image
        x_norm = (2 * (x_range + 0.5) / w) - 1
        y_norm = (2 * (y_range + 0.5) / h) - 1
    
    # Create grid using broadcasting
    # torch.meshgrid with indexing='ij' produces yy (H,W) and xx (H,W)
    # yy corresponds to y_norm broadcasted, xx to x_norm broadcasted
    yy, xx = torch.meshgrid(y_norm, x_norm, indexing='ij') 

    # Stack to get (H, W, 2) where the last dim is (x, y) for F.grid_sample
    # F.grid_sample expects grid input of shape (N, H_out, W_out, 2)
    # where grid[..., 0] are x-coordinates and grid[..., 1] are y-coordinates.
    meshed = torch.stack([xx, yy], dim=2)
    return meshed



def make_coordinate_grid_2d(spatial_size, tensor_for_attributes, align_corners=True):
    """
    Generates a 2D coordinate grid.
    Args:
        spatial_size: tuple (H, W)
        tensor_for_attributes: A tensor from which dtype and device will be derived.
        align_corners: bool, if True, normalize to corners, else to centers.
    Returns:
        A tensor of shape (H, W, 2) with (x, y) coordinates.
    """
    h, w = spatial_size
    dtype = tensor_for_attributes.dtype
    device = tensor_for_attributes.device

    x_range = torch.arange(w, dtype=dtype, device=device)
    y_range = torch.arange(h, dtype=dtype, device=device)

    # Assuming H, W will always be > 1 for normalization with H-1, W-1
                      # If H or W can be 1, this normalization needs adjustment as (dim - 1) would be 0.
        # The TracerWarning occurs because h and w, derived from a tensor's shape,
        # are used in Python-level control flow (if w > 1).
        # For fixed H, W (e.g., 32x32), this condition is constant.
        # To make it more robust for ONNX tracing if H,W could vary and be 1:
        
        # For x_norm:
        # Create tensor for w-1 to use in torch.where
    w_minus_1 = torch.tensor(w - 1, dtype=dtype, device=device)
    x_norm = torch.where(
        torch.tensor(w > 1, device=device), # condition
        (2 * x_range / w_minus_1) - 1,      # if true
        torch.zeros_like(x_range)           # if false (w=1 case)
    )

    # For y_norm:
    h_minus_1 = torch.tensor(h - 1, dtype=dtype, device=device)
    y_norm = torch.where(
        torch.tensor(h > 1, device=device), # condition
        (2 * y_range / h_minus_1) - 1,      # if true
        torch.zeros_like(y_range)           # if false (h=1 case)
        )

    yy, xx = torch.meshgrid(y_norm, x_norm, indexing='ij') 
    meshed = torch.stack([xx, yy], dim=2)
    return meshed


def make_coordinate_grid_3d(spatial_size, type):
    """
        generate 3D coordinate grid
    """
    d, h, w = spatial_size
    x = torch.arange(w).type(type)
    y = torch.arange(h).type(type)
    z = torch.arange(d).type(type)
    x = 2 * (x / (w - 1)) - 1
    y = 2 * (y / (h - 1)) - 1
    z = 2 * (z / (d - 1)) - 1
    yy = y.view(1, -1, 1).repeat(d, 1, w)
    xx = x.view(1, 1, -1).repeat(d, h, 1)
    zz = z.view(-1, 1, 1).repeat(1, h, w)
    meshed = torch.cat([xx.unsqueeze_(3), yy.unsqueeze_(3)], 3)
    return (meshed, zz)


class ResBlock1d(nn.Module):
    '''
        basic block
    '''

    def __init__(self, in_features, out_features, kernel_size, padding):
        super(ResBlock1d, self).__init__()
        self.in_features = in_features
        self.out_features = out_features
        self.conv1 = nn.Conv1d(in_channels=in_features, out_channels=in_features, kernel_size=kernel_size, padding=padding)
        self.conv2 = nn.Conv1d(in_channels=in_features, out_channels=out_features, kernel_size=kernel_size, padding=padding)
        if out_features != in_features:
            self.channel_conv = nn.Conv1d(in_features, out_features, 1)
        self.norm1 = BatchNorm1d(in_features)
        self.norm2 = BatchNorm1d(in_features)
        self.relu = nn.ReLU()

    def forward(self, x):
        out = self.norm1(x)
        out = self.relu(out)
        out = self.conv1(out)
        out = self.norm2(out)
        out = self.relu(out)
        out = self.conv2(out)
        if self.in_features != self.out_features:
            out += self.channel_conv(x)
        else:
            out += x
        return out


class ResBlock2d(nn.Module):
    '''
            basic block
    '''
    def __init__(self, in_features, out_features, kernel_size, padding):
        super(ResBlock2d, self).__init__()
        self.in_features = in_features
        self.out_features = out_features
        self.conv1 = nn.Conv2d(in_channels=in_features, out_channels=in_features, kernel_size=kernel_size, padding=padding)
        self.conv2 = nn.Conv2d(in_channels=in_features, out_channels=out_features, kernel_size=kernel_size, padding=padding)
        if out_features != in_features:
            self.channel_conv = nn.Conv2d(in_features, out_features, 1)
        self.norm1 = BatchNorm2d(in_features)
        self.norm2 = BatchNorm2d(in_features)
        self.relu = nn.ReLU()

    def forward(self, x):
        out = self.norm1(x)
        out = self.relu(out)
        out = self.conv1(out)
        out = self.norm2(out)
        out = self.relu(out)
        out = self.conv2(out)
        if self.in_features != self.out_features:
            out += self.channel_conv(x)
        else:
            out += x
        return out


class UpBlock2d(nn.Module):
    '''
            basic block
    '''
    def __init__(self, in_features, out_features, kernel_size=3, padding=1):
        super(UpBlock2d, self).__init__()
        self.conv = nn.Conv2d(in_channels=in_features, out_channels=out_features, kernel_size=kernel_size, padding=padding)
        self.norm = BatchNorm2d(out_features)
        self.relu = nn.ReLU()

    def forward(self, x):
        out = F.interpolate(x, scale_factor=2)
        out = self.conv(out)
        out = self.norm(out)
        out = F.relu(out)
        return out


class DownBlock1d(nn.Module):
    '''
            basic block
    '''
    def __init__(self, in_features, out_features, kernel_size, padding):
        super(DownBlock1d, self).__init__()
        self.conv = nn.Conv1d(in_channels=in_features, out_channels=out_features, kernel_size=kernel_size, padding=padding,
          stride=2)
        self.norm = BatchNorm1d(out_features)
        self.relu = nn.ReLU()

    def forward(self, x):
        out = self.conv(x)
        out = self.norm(out)
        out = self.relu(out)
        return out


class DownBlock2d(nn.Module):
    '''
            basic block
    '''

    def __init__(self, in_features, out_features, kernel_size=3, padding=1, stride=2):
        super(DownBlock2d, self).__init__()
        self.conv = nn.Conv2d(in_channels=in_features, out_channels=out_features, kernel_size=kernel_size, padding=padding,
          stride=stride)
        self.norm = BatchNorm2d(out_features)
        self.relu = nn.ReLU()

    def forward(self, x):
        out = self.conv(x)
        out = self.norm(out)
        out = self.relu(out)
        return out


class SameBlock1d(nn.Module):
    '''
            basic block
    '''
    def __init__(self, in_features, out_features, kernel_size, padding):
        super(SameBlock1d, self).__init__()
        self.conv = nn.Conv1d(in_channels=in_features, out_channels=out_features, kernel_size=kernel_size,
          padding=padding)
        self.norm = BatchNorm1d(out_features)
        self.relu = nn.ReLU()

    def forward(self, x):
        out = self.conv(x)
        out = self.norm(out)
        out = self.relu(out)
        return out


class SameBlock2d(nn.Module):
    '''
            basic block
    '''

    def __init__(self, in_features, out_features, kernel_size=3, padding=1):
        super(SameBlock2d, self).__init__()
        self.conv = nn.Conv2d(in_channels=in_features, out_channels=out_features, kernel_size=kernel_size,padding=padding)
        self.norm = BatchNorm2d(out_features)
        self.relu = nn.ReLU()

    def forward(self, x):
        out = self.conv(x)
        out = self.norm(out)
        out = self.relu(out)
        return out


class AdaAT(nn.Module):
    '''
       AdaAT operator
    '''
    def __init__(self, para_ch, feature_ch):
        super(AdaAT, self).__init__()
        self.para_ch = para_ch
        self.feature_ch = feature_ch # This is D_feat
        self.commn_linear = nn.Sequential(nn.Linear(para_ch, para_ch), nn.ReLU())
        self.scale = nn.Sequential(nn.Linear(para_ch, feature_ch), nn.Sigmoid())
        self.rotation = nn.Sequential(nn.Linear(para_ch, feature_ch), nn.Tanh())
        self.translation = nn.Sequential(nn.Linear(para_ch, 2 * feature_ch), nn.Tanh())

    def forward(self, feature_map, para_code):
        # feature_map: (B, D_feat, H, W)
        # para_code: (B, P) where P is self.para_ch
        batch, D_feat, H, W = feature_map.shape
        
        # 1. Calculate transformation parameters from para_code
        para_code_common = self.commn_linear(para_code) # (B, P)
        
        # scale_params: (B, D_feat), angle_params: (B, D_feat)
        scale_params = self.scale(para_code_common) 
        angle_params = self.rotation(para_code_common)
        
        # translation_params_raw: (B, 2 * D_feat)
        translation_params_raw = self.translation(para_code_common)
        # translation_params: (B, D_feat, 2) -> for (tx, ty) per D_feat channel
        translation_params = translation_params_raw.view(batch, D_feat, 2)

        # 2. Prepare parameters for affine transformation matrix components
        # scale_val: (B, D_feat, 1) for scaling
        scale_val = scale_params.unsqueeze(-1) * 2.0
        # angle_val: (B, D_feat, 1) for rotation
        angle_val = angle_params.unsqueeze(-1) * math.pi 
        
        cos_a = torch.cos(angle_val) # (B, D_feat, 1)
        sin_a = torch.sin(angle_val) # (B, D_feat, 1)

        # 3. Create base 2D grid
        # base_grid_hw: (H, W, 2) with (x,y) coordinates for F.grid_sample
        base_grid_hw = make_coordinate_grid_2d((H, W), feature_map, align_corners=True) 
        
        # Expand base grid for batch and D_feat operations
        # current_x_coords: (1, 1, H, W)
        # current_y_coords: (1, 1, H, W)
        # grid_sample expects grid[..., 0] as x and grid[..., 1] as y.
        # make_coordinate_grid_2d returns stack([xx, yy], dim=2) -> x is dim 0, y is dim 1 in last dimension.
        current_x_coords = base_grid_hw[..., 0].unsqueeze(0).unsqueeze(1) # (1, 1, H, W)
        current_y_coords = base_grid_hw[..., 1].unsqueeze(0).unsqueeze(1) # (1, 1, H, W)
        # These will broadcast with (B, D_feat, 1, 1) parameters.

        # 4. Prepare transformation parameters for broadcasting (B, D_feat, 1, 1)
        cos_a_bc = cos_a.view(batch, D_feat, 1, 1)
        sin_a_bc = sin_a.view(batch, D_feat, 1, 1)
        scale_val_bc = scale_val.view(batch, D_feat, 1, 1)
        
        # tx_bc: (B, D_feat, 1, 1), ty_bc: (B, D_feat, 1, 1)
        tx_bc = translation_params[:, :, 0].view(batch, D_feat, 1, 1)
        ty_bc = translation_params[:, :, 1].view(batch, D_feat, 1, 1)

        # 5. Compute all sampling coordinates vectorially
        # Rotated coordinates
        rotated_x_all_d = current_x_coords * cos_a_bc + current_y_coords * sin_a_bc
        rotated_y_all_d = -current_x_coords * sin_a_bc + current_y_coords * cos_a_bc
        
        # Scaled coordinates
        scaled_rotated_x_all_d = rotated_x_all_d * scale_val_bc
        scaled_rotated_y_all_d = rotated_y_all_d * scale_val_bc
            
        # Translated coordinates (final grid components)
        final_x_all_d = scaled_rotated_x_all_d + tx_bc # (B, D_feat, H, W)
        final_y_all_d = scaled_rotated_y_all_d + ty_bc # (B, D_feat, H, W)
            
        # Stack final (x,y) coordinates to form the grid for F.grid_sample
        # final_sampling_grid: (B, D_feat, H, W, 2)
        final_sampling_grid = torch.stack([final_x_all_d, final_y_all_d], dim=-1) 
        
        # 6. Reshape inputs for a single F.grid_sample call
        # feature_map_reshaped: (B * D_feat, 1, H, W)
        feature_map_reshaped = feature_map.reshape(batch * D_feat, 1, H, W)
        
        # final_sampling_grid_reshaped: (B * D_feat, H, W, 2)
        final_sampling_grid_reshaped = final_sampling_grid.reshape(batch * D_feat, H, W, 2)
            
        # 7. Perform grid sampling
        sampled_reshaped = F.grid_sample(
            feature_map_reshaped, 
            final_sampling_grid_reshaped, 
            mode='bilinear', 
            padding_mode='zeros', # Consistent with original F.grid_sample implicit default
            align_corners=True    # Grid was generated assuming align_corners=True
        ) # Output shape: (B * D_feat, 1, H, W)
        
        # 8. Reshape output back to (B, D_feat, H, W)
        trans_feature = sampled_reshaped.reshape(batch, D_feat, H, W)
        
        return trans_feature



class DINetV1(nn.Module):

    def __init__(self, source_channel, ref_channel, audio_channel):
        super(DINetV1, self).__init__()
        self.source_in_conv = nn.ModuleList([
         nn.Sequential(SameBlock2d(source_channel, 64, kernel_size=7, padding=3), DownBlock2d(64, 128, kernel_size=3, padding=1)),
         nn.Sequential(SameBlock2d(128, 128, kernel_size=3, padding=1), DownBlock2d(128, 256, kernel_size=3, padding=1)),
         nn.Sequential(SameBlock2d(256, 256, kernel_size=3, padding=1), DownBlock2d(256, 512, kernel_size=3, padding=1))])
        self.ref_in_conv = nn.ModuleList([
         nn.Sequential(SameBlock2d(ref_channel, 64, kernel_size=7, padding=3), DownBlock2d(64, 128, kernel_size=3, padding=1)),
         nn.Sequential(SameBlock2d(128, 128, kernel_size=3, padding=1), DownBlock2d(128, 256, kernel_size=3, padding=1)),
         nn.Sequential(SameBlock2d(256, 256, kernel_size=3, padding=1), DownBlock2d(256, 512, kernel_size=3, padding=1))])
        self.trans_conv = nn.Sequential(SameBlock2d(1024, 256, kernel_size=3, padding=1), SameBlock2d(256, 256, kernel_size=11, padding=5), SameBlock2d(256, 256, kernel_size=11, padding=5), DownBlock2d(256, 256, kernel_size=3, padding=1), SameBlock2d(256, 256, kernel_size=7, padding=3), SameBlock2d(256, 256, kernel_size=7, padding=3), DownBlock2d(256, 256, kernel_size=3, padding=1), SameBlock2d(256, 256, kernel_size=5, padding=2), DownBlock2d(256, 256, kernel_size=5, padding=2), SameBlock2d(256, 256, kernel_size=3, padding=1), DownBlock2d(256, 256, kernel_size=3, padding=1))
        self.audio_encoder = nn.Sequential(
            SameBlock2d(audio_channel, 256, kernel_size=(5,1), padding=(2,0)), 
            ResBlock2d(256, 256, kernel_size=(3,1), padding=(1,0)), 
            DownBlock2d(256, 256, kernel_size=(3,1), padding=(1,0), stride=(2,1)), 
            ResBlock2d(256, 256, kernel_size=(3,1), padding=(1,0)), 
            DownBlock2d(256, 256, kernel_size=(3,1), padding=(1,0), stride=(2,1)), 
            SameBlock2d(256, 256, kernel_size=(3,1), padding=(1,0))
        )
        appearance_conv_list = list()
        appearance_conv_list.append(nn.Sequential(SameBlock2d(512, 512, kernel_size=5, padding=2), ResBlock2d(512, 512, 3, 1), ResBlock2d(512, 512, 3, 1), ResBlock2d(512, 512, 3, 1), ResBlock2d(512, 512, 3, 1)))
        appearance_conv_list.append(nn.Sequential(ResBlock2d(512, 512, 3, 1), ResBlock2d(512, 512, 3, 1), ResBlock2d(512, 512, 3, 1), ResBlock2d(512, 512, 3, 1)))
        self.appearance_conv_list = nn.ModuleList(appearance_conv_list)
        self.adaAT = AdaAT(512, 512)
        self.out_conv = nn.ModuleList([
         nn.Sequential(SameBlock2d(1024, 512, kernel_size=3, padding=1), UpBlock2d(512, 512, kernel_size=3, padding=1), ResBlock2d(512, 512, 3, 1)),
         nn.Sequential(UpBlock2d(768, 256, kernel_size=3, padding=1), ResBlock2d(256, 256, 3, 1)),
         nn.Sequential(UpBlock2d(384, 128, kernel_size=3, padding=1), ResBlock2d(128, 128, 3, 1), nn.Conv2d(128, 3, kernel_size=(7,7), padding=(3,3)), nn.Sigmoid())])
        # ori
        # self.global_avg2d = nn.AdaptiveAvgPool2d(1)
        # self.global_avg1d = nn.AdaptiveAvgPool1d(1)
        # for trt
        self.global_avg2d = nn.AvgPool2d(2)
        self.global_avg_audio = nn.AdaptiveAvgPool2d(1)

    def forward(self, source_img, ref_img, audio_feature):
        feats_source = []
        x = source_img
        for f in self.source_in_conv:
            x = f(x)
            feats_source.append(x)
        feats_ref = []
        x = ref_img
        count = 0
        for f in self.ref_in_conv:
            x = f(x)
            if count == 2:
                feats_ref.append(x)
            count += 1
        source_in_feature = feats_source.pop()
        ref_in_feature = feats_ref.pop()
        img_para = self.trans_conv(torch.cat([source_in_feature, ref_in_feature], 1))
        img_para = self.global_avg2d(img_para).squeeze(3).squeeze(2)
        print("img_para", img_para.size())
        # Process audio_feature with 2D convolutions
        audio_feature = audio_feature.unsqueeze(-1)
        # Assuming audio_feature is already [B, 256, 20, 1]
        audio_para = self.audio_encoder(audio_feature) * 1.5
        print("audio_para", audio_para.size())
        audio_para = self.global_avg_audio(audio_para).squeeze(3).squeeze(2) # Adjusted for 2D pooling output
        print("audio_para2", audio_para.size())
        trans_para = torch.cat([img_para, audio_para], 1)
        print("trans_para", trans_para.size())
        ref_trans_feature = self.appearance_conv_list[0](ref_in_feature)
        print("ref_trans_feature0", ref_trans_feature.size())
        ref_trans_feature = self.adaAT(ref_trans_feature, trans_para)
        ref_trans_feature = self.appearance_conv_list[1](ref_trans_feature)
        print("ref_trans_feature", ref_trans_feature.size())
        print("source_in_feature", source_in_feature.size())
        merge_feature = torch.cat([source_in_feature, ref_trans_feature], 1)
        out = self.out_conv[0](merge_feature)
        out = self.out_conv[1](torch.cat([out, feats_source.pop()], 1))
        out = self.out_conv[2](torch.cat([out, feats_source.pop()], 1))
        print("out",out.shape)
        return out

class DINetMouth():
    pass

class DINetMouth512():
    pass




if __name__ == "__main__":
    dinet_model = DINetMouth512(3, 3, 256)
    dinet_model.cuda()
    ref_face = torch.randn(4, 3, 256, 256)
    audio_feature = torch.randn(4, 256, 20)
    print(dinet_model(ref_face.cuda(), ref_face.cuda(), audio_feature.cuda()).size())
