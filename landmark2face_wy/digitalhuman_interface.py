# uncompyle6 version 3.9.2
# Python bytecode version base 3.8.0 (3413)
# Decompiled from: Python 3.10.9 | packaged by Anaconda, Inc. | (main, Mar  1 2023, 18:18:15) [MSC v.1916 64 bit (AMD64)]
# Embedded file name: /code/landmark2face_wy/digitalhuman_interface.py
# Compiled at: 2024-04-01 10:05:40
# Size of source mod 2**32: 15233 bytes
from landmark2face_wy.options.test_options import TestOptions
import torchvision.transforms as transforms
from landmark2face_wy.models.l2faceaudio_model import L2FaceAudioModel
from landmark2face_wy.util.util import *
import torch, time, math

from torch import nn
import torch.nn.functional as F
from face_lib.face_restore import GFPGAN
from y_utils.config import GlobalConfig
import cv2
import numpy as np
from loguru import logger
import sys

# sys.path.append('D:/project/others/torch2trt-master')
# from torch2trt import torch2trt,TRTModule

# def convert_models_G(model, save_path, audio_shape, mask_shape, fake_shape, fp16_mode=False):
#     # 创建示例输入
#     audio_feature = torch.randn((1,audio_shape[0], audio_shape[1]),dtype=torch.float32).cuda()
#     mask_B = torch.randn((1, 3, mask_shape[0], mask_shape[1]),dtype=torch.float32).cuda()
#     fake_B = torch.randn((1, 3, fake_shape[0], fake_shape[1]),dtype=torch.float32).cuda()
    
#     # 打印输入维度
#     print("Input shapes:")
#     print("mask_B:", mask_B.shape)
#     print("fake_B:", fake_B.shape)
#     print("audio_feature:", audio_feature.shape)

#     # 添加调试信息
#     def debug_hook(module, input, output):
#         print(f"Module: {module.__class__.__name__}")
#         print(f"Input shapes: {[x.shape for x in input]}")
#         print(f"Output shape: {output.shape}")
#         if module.__class__.__name__ == 'AdaAT':
#             print(f"AdaAT input shapes:")
#             print(f"feature_map: {input[0].shape}")
#             print(f"para_code: {input[1].shape}")
#             print(f"AdaAT output shape: {output.shape}")
#             # 检查para_code的维度
#             if len(input[1].shape) != 2:
#                 print(f"Warning: para_code has unexpected shape: {input[1].shape}")

#         if isinstance(module, nn.Linear):
#             print(f"Linear input shape: {input[0].shape}")
#             print(f"Linear weight shape: {module.weight.shape}")
    
    
#     # 注册钩子
#     hooks = []
#     for name, module in model.named_modules():
#         if isinstance(module, (nn.Linear, nn.Conv2d, nn.Conv1d)):
#             hooks.append(module.register_forward_hook(debug_hook))
    
#     # 转换模型
#     model_trt = torch2trt(
#         model,
#         [mask_B, fake_B, audio_feature],
#         fp16_mode=fp16_mode,
#         max_workspace_size=1<<30,
#         strict_type_constraints=True
#     )
    
#     # 移除钩子
#     for hook in hooks:
#         hook.remove()
#     print("111111111111111111111111111111111111111111111111111111111111111111", model_trt.shape)
#     for i in range(2):
#         t1_mean = 0
#         for i in range(10):
#             t1 = time.time()
#             y = model(mask_B,fake_B,audio_feature)
#             t2 = time.time()
#             t1_mean += (t2 - t1)
#     #print(y)

#     print("torch model inference time : {} ms input_size : ({},{})".format(t1_mean*100,audio_feature.shape[-1],audio_feature.shape[-2]))

#     for i in range(2):
#         t1_mean = 0
#         for i in range(10):
#             t1 = time.time()
#             y_trt = model_trt(mask_B,fake_B,audio_feature)
#             t2 = time.time()
#             t1_mean += (t2 - t1)
#     #print(y_trt)

#     print("trt model inference time : {} ms input_size : ({},{})".format(t1_mean*100,audio_feature.shape[-1],audio_feature.shape[-2]))
#     # convert to TensorRT feeding sample data as input
#     print("max abs",torch.max(torch.abs(y[0][:,:-1,:,:] - y_trt[0][:,:-1,:,:])))
#     print(torch.max(y[0][:,:-1,:,:]),torch.max(y_trt[0][:,:-1,:,:]))
#     print("max abs",torch.max(torch.abs(y[1] - y_trt[1])))
#     torch.save(model_trt.state_dict(), save_path)


class DigitalHumanModel:

    def __init__(self, blend_dynamic, chaofen_before, face_blur_detect=False,half=False):
        self.blend = True
        self.opt = TestOptions().parse()
        self.opt.isTrain = False
        self.half = half
        temp_model = torch.load(self.opt.model_path)
        print("temp_model", temp_model.keys()) 
        self.opt.netG = temp_model["model_name"]
        print("self.opt.netG", self.opt.netG)
        self.opt.dataloader_size = temp_model["model_input_size"][0]
        self.opt.ngf = temp_model["model_ngf"]
        self.img_size = temp_model["model_input_size"][0]
        self.fuse_mask = temp_model["fuse_mask"]
        self.fuse_mask = cv2.resize(self.fuse_mask, (self.img_size, self.img_size))
        self.mask_re_cuda = torch.tensor(temp_model["input_mask_re"]).unsqueeze(0).unsqueeze(0).cuda().half()
        self.mask_cuda = torch.tensor(temp_model["input_mask"]).unsqueeze(0).unsqueeze(0).cuda().half()
        self.fuse_mask_cuda = torch.tensor(self.fuse_mask).unsqueeze(0).unsqueeze(0).cuda().repeat(1, 3, 1, 1).half()
        self.nblend = temp_model["nblend"]
        self.model = L2FaceAudioModel(self.opt)

        self.drivered_wh = temp_model["wh"]
        self.model.netG.load_state_dict(temp_model["face_G"])

        if self.half:
            self.model.netG.half()
        self.model.netG.cuda()
        self.model.eval()



        # 将模型转换为TRT模型
        # convert_models_G(self.model.netG,"model.trt",(256,20), (256,256),(256,256),False)

        if blend_dynamic == "xseg":
            from xseg.dfl_xseg_api import XsegNet
            self.xseg = XsegNet(model_name="xseg_net_private", provider="gpu")
        if chaofen_before == 1:
            self.gfpgan = GFPGAN(model_type="GFPGANv1.4", provider="gpu")
        self.face_blur_detect = face_blur_detect
        if self.face_blur_detect:
            from face_attr_detect.face_attr import FaceAttr
            self.face_attr = FaceAttr(model_name="face_attr_mbnv3", provider="gpu")

    def tensor_norm(self, img_tensor, mask=None):
        img_tensor = img_tensor / 127.5 - 1
        if mask is not None:
            return (img_tensor + 1) * mask - 1
        return img_tensor

    def tensor_norm_no_training(self, img_tensor, mask=None):
        img_tensor = img_tensor / 255.0
        if mask is not None:
            return img_tensor * mask
        return img_tensor

    def inference(self, audio_info, face_data_dict, this_batch, start_idx, params):
        audio_idx, wenet_feature = audio_info
        B_img_list = []
        B_img__list = []
        mask_B_list = []
        mask_B_pre_list = []
        lab_list = []
        for i in range(this_batch):
            img_idx = start_idx + i
            mask_B_pre = self.gfpgan.forward(face_data_dict[img_idx]["crop_img"])

            mask_B = mask_B_pre[int(0 * (self.img_size / 256)):int(-10 * (self.img_size / 256)),
             int(5 * (self.img_size / 256)):int(-5 * (self.img_size / 256))]
            B_img = mask_B.copy()
            mask_B = torch.from_numpy(mask_B[:, :, [2,1,0]].transpose(2, 0, 1))
            B_img_ = B_img.copy()
            B_img_ = torch.from_numpy(B_img_[:, :, [2,1,0]].transpose(2, 0, 1))
            B_img_list.append(torch.from_numpy(B_img.transpose(2, 0, 1)))
            B_img__list.append(B_img_)
            mask_B_pre_list.append(mask_B_pre)
            mask_B_list.append(mask_B)
            lab = wenet_feature.transpose(1, 0)[audio_idx[start_idx + i][0]:audio_idx[start_idx + i][1]][np.newaxis, ...]
            lab_list.append(lab)
        model_st = time.time()
        torch.cuda.synchronize()
        if this_batch > 0:
            lab = torch.tensor(lab_list).cuda().half()
            mask_B = torch.stack(mask_B_list).cuda().half()
            mask_B = self.tensor_norm(mask_B, mask=self.mask_cuda.repeat(this_batch, 3, 1, 1))
            B_img_ = torch.stack(B_img__list).cuda().half()
            B_img_ = self.tensor_norm(B_img_, mask=self.mask_re_cuda.repeat(this_batch, 3, 1, 1))
            B_img = torch.stack(B_img_list).cuda().half()
            B_img = self.tensor_norm(B_img)

            fake_B = self.model.netG(lab, torch.cat((mask_B, B_img_), 1))[:, [2,1,0], :, :]
            if self.nblend:
                fake_B = torch.where(self.mask_re_cuda == 0, B_img, fake_B)
            self.fuse_mask_cuda_copy = self.fuse_mask_cuda.repeat(this_batch, 1, 1, 1)
            fuse_res = fake_B * self.fuse_mask_cuda_copy + (1 - self.fuse_mask_cuda_copy) * B_img
        torch.cuda.synchronize()
        model_et = time.time()
        output_img_list = []
        for i in range(this_batch):
            mask_B_pre_list[i][int(0 * (self.img_size / 256)):int(-10 * (self.img_size / 256)),
             int(5 * (self.img_size / 256)):int(-5 * (self.img_size / 256))] = ((fuse_res[i] + 1) * 127.5).permute(1, 2, 0).cpu().detach().numpy()
            output_img_list.append(mask_B_pre_list[i])
        return output_img_list

    def inference1(self, audio_info, face_data_dict, this_batch, start_idx, params):
        B_img_list = []
        B_img__list = []
        mask_B_list = []
        mask_B_pre_list = []
        lab_list = []
        for i in range(this_batch):
            img_idx = start_idx + i
            mask_B_pre = face_data_dict[img_idx]["crop_img"]
            mask_B = mask_B_pre[int(0 * (self.img_size / 256)):int(-10 * (self.img_size / 256)),int(5 * (self.img_size / 256)):int(-5 * (self.img_size / 256))]
            B_img = mask_B.copy()
            mask_B = torch.from_numpy(mask_B[:, :, [2,1,0]].transpose(2, 0, 1))
            B_img_ = B_img.copy()
            B_img_ = torch.from_numpy(B_img_[:, :, [2,1,0]].transpose(2, 0, 1))
            B_img_list.append(torch.from_numpy(B_img.transpose(2, 0, 1)))
            B_img__list.append(B_img_)
            mask_B_pre_list.append(mask_B_pre)
            mask_B_list.append(mask_B)
            lab = audio_info[start_idx + i][(np.newaxis, ...)]
            lab_list.append(lab)

        model_st = time.time()
        torch.cuda.synchronize()
        if this_batch > 0:
            lab = torch.tensor(lab_list).cuda().half()
            mask_B = torch.stack(mask_B_list).cuda().half()
            mask_B = self.tensor_norm(mask_B, mask=(self.mask_cuda.repeat(this_batch, 3, 1, 1)))
            B_img_ = torch.stack(B_img__list).cuda().half()
            B_img_ = self.tensor_norm(B_img_, mask=(self.mask_re_cuda.repeat(this_batch, 3, 1, 1)))
            B_img = torch.stack(B_img_list).cuda().half()
            B_img = self.tensor_norm(B_img)
            fake_B = self.model.netG(lab, torch.cat((mask_B, B_img_), 1))[:, [2,1,0], :, :]
            if self.nblend:
                fake_B = torch.where(self.mask_re_cuda == 0, B_img, fake_B)
            self.fuse_mask_cuda_copy = self.fuse_mask_cuda.repeat(this_batch, 1, 1, 1)
            fuse_res = fake_B * self.fuse_mask_cuda_copy + (1 - self.fuse_mask_cuda_copy) * B_img
        torch.cuda.synchronize()
        model_et = time.time()
        output_img_list = []
        for i in range(this_batch):
            mask_B_pre_list[i][int(0 * (self.img_size / 256)):int(-10 * (self.img_size / 256)),int(5 * (self.img_size / 256)):int(-5 * (self.img_size / 256))] = \
                ((fuse_res[i] + 1) * 127.5).permute(1, 2, 0).cpu().detach().numpy()
            output_img_list.append(mask_B_pre_list[i])
        return output_img_list

    def get_face_mask(self, img, landmarks):
        imgshape = img.shape[0]
        landmarks = landmarks.astype(int)
        wanted_numpy = np.concatenate([landmarks[2:15], landmarks[29:30]])
        mask = np.zeros((imgshape, imgshape), dtype=np.uint8)
        wanted_numpy = cv2.convexHull(wanted_numpy)
        cv2.fillConvexPoly(mask, wanted_numpy, 255)
        mid = (landmarks[5, :] + landmarks[11, :]) // 2
        cv2.ellipse(mask, (mid[0], mid[1]), ((landmarks[11, 0] - landmarks[5, 0] + 3 * (imgshape // 266)) // 2, 60 * (imgshape // 266)), 0, 0, 180, (255,255,255), -1)
        amask = (mask > 0).astype(np.uint8) * 255
        kernel_size = (5 * (imgshape // 266) + 1, 5 * (imgshape // 266) + 1)
        iterations = 1
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, kernel_size)
        eroded_mask = cv2.dilate(amask, kernel, iterations=iterations)
        return eroded_mask

    def get_face_mask2(self, img, landmarks, delta):
        wanted_numpy = landmarks.astype(int)
        mask = np.zeros((img.shape[0], img.shape[1]), dtype=np.uint8)
        wanted_numpy = cv2.convexHull(wanted_numpy)
        cv2.fillConvexPoly(mask, wanted_numpy, 255)
        amask = (mask > 0).astype(np.uint8) * 255
        kernel_size = (5, 5)
        iterations = 1
        kernel = cv2.getStructuringElement(cv2.MORPH_RECT, kernel_size)
        amask = cv2.dilate(amask, kernel, iterations=iterations)
        eroded_mask = cv2.GaussianBlur(amask, (5, 5), 0)
        return amask

    def inference_notraining(self, audio_info, face_data_dict, this_batch, start_idx, blend_dynamic, params, frameId):
        # print(">>>start_idx :{}, this_batch :{}, face_data_dict :{},params :{},frameId :{}".format(start_idx, this_batch, face_data_dict, params, frameId))
        model_st = time.time()
        B_img_list = []
        B_img__list = []
        mask_B_list = []
        mask_B_pre_list = []
        blend_mask_list = []
        lab_list = []
        t1 = time.time()
        for i in range(this_batch):

            img_idx = start_idx + i
            mask_B_pre = face_data_dict[img_idx]["crop_img"]
            
            # print("mask_B_pre.shape:", mask_B_pre.shape)
            # if img_idx == 0:
            #     cv2.imwrite(f"mask_B_pre_{frameId}.png", mask_B_pre)

            mask_B = mask_B_pre[int(5 * (self.img_size / 256)):int(-5 * (self.img_size / 256)),int(5 * (self.img_size / 256)):int(-5 * (self.img_size / 256))] # [5, -5, 5, -5]
            B_img = mask_B.copy()
            mask_B = torch.from_numpy(mask_B[:, :, [2, 1, 0]].transpose(2, 0, 1))
            B_img_ = B_img.copy()
            B_img_ = torch.from_numpy(B_img_[:, :, [2, 1, 0]].transpose(2, 0, 1))
            if blend_dynamic == "lmk":
                lm = face_data_dict[img_idx]["crop_lm"]
                # if img_idx == 0: 
                #     print("img_idx:lm.shape:", lm.shape)
                #     print("img_idx : lm:", lm)
                blend_mask = self.get_face_mask(mask_B_pre, lm)
                blend_mask = blend_mask[int(5 * (self.img_size / 256)):int(-5 * (self.img_size / 256)),
                 int(5 * (self.img_size / 256)):int(-5 * (self.img_size / 256))]
                blend_mask = cv2.resize(blend_mask, (self.img_size, self.img_size))
                blend_mask_list.append(blend_mask)

            elif blend_dynamic == "xseg":
                    xseg_mask_out = self.xseg.forward(B_img)
                    xseg_mask_out = np.where(xseg_mask_out >= 0.1, 255, 0).astype(np.uint8)
                    blend_mask = xseg_mask_out
                    blend_mask = cv2.resize(blend_mask, (self.img_size, self.img_size))
                    blend_mask_list.append(blend_mask)

            B_img_list.append(torch.from_numpy(B_img[:, :, [2, 1, 0]].transpose(2, 0, 1)))
            B_img__list.append(B_img_)
            mask_B_pre_list.append(mask_B_pre)
            mask_B_list.append(mask_B)
            lab = audio_info[start_idx + i].transpose(1, 0)
            lab_list.append(lab)

        t2 = time.time()
        # print(f"【{frameId} 获取数据】: {(t2 - t1)*1000:.3f}ms")
        torch.cuda.synchronize()
        # t3 = time.time()
        # print(f"【{frameId} 同步】: {(t3 - t2)*1000:.3f}ms")
        lab_array = np.array(lab_list)
        if self.half:
            lab_array = lab_array.astype(np.float16)
            mask_B_list = [mask_B.half() for mask_B in mask_B_list]
            B_img__list = [B_img_.half() for B_img_ in B_img__list]
            B_img_list = [B_img.half() for B_img in B_img_list]
            
        if this_batch > 0:
            t4 = time.time()
            lab = torch.from_numpy(lab_array).cuda()
            mask_B = torch.stack(mask_B_list).cuda()
            mask_B = self.tensor_norm_no_training(mask_B, mask=self.mask_cuda.repeat(this_batch, 3, 1, 1))
            B_img_ = torch.stack(B_img__list).cuda()
            B_img_ = self.tensor_norm_no_training(B_img_, mask=self.mask_re_cuda.repeat(this_batch, 3, 1, 1))
            B_img = torch.stack(B_img_list).cuda()
            B_img = self.tensor_norm_no_training(B_img)
            torch.cuda.synchronize()
            t5 = time.time()
            # print(f"【{frameId} 类型转换np-cuda】: {(t5 - t4)*1000:.3f}ms")
            # print(f"lab :{lab.shape}, mask_B :{mask_B.shape}, B_img_ :{B_img_.shape}")
            fake_B = self.model.netG(mask_B, B_img_, lab)
            torch.cuda.synchronize()
            t6 = time.time()
            # print(f"【{frameId} netG推理】: {(t6 - t5)*1000:.3f}ms")
            if self.nblend:
                fake_B = torch.where(self.mask_re_cuda == 0, B_img, fake_B)
            torch.cuda.synchronize()
            t7 = time.time()
            # print(f"【{frameId} 融合指引】: {(t7 - t6)*1000:.3f}ms")
            if blend_dynamic in ('xseg', 'lmk'):
                # Transfer all masks to GPU at once, then perform all blur operations on the GPU.
                # This replaces the CPU loop and is the most significant optimization.
                masks_tensor_gpu = torch.from_numpy(np.array(blend_mask_list)).cuda(non_blocking=True).to(torch.float32, non_blocking=True).unsqueeze(1)
                
                # The kernel size for blurring.
                k = 16 * int(self.img_size / 256) + 1
                padding = k // 2

                # Replicate cv2.stackBlur on the GPU. It's a 3-pass box filter.
                blurred_tensor = F.avg_pool2d(masks_tensor_gpu, kernel_size=k, stride=1, padding=padding)
                blurred_tensor = F.avg_pool2d(blurred_tensor, kernel_size=k, stride=1, padding=padding)
                blurred_tensor = F.avg_pool2d(blurred_tensor, kernel_size=k, stride=1, padding=padding)

                # Normalize the final blurred tensor.
                weights_tensor = blurred_tensor / 255.0
                
                if self.half:
                    weights_tensor = weights_tensor.half()

                self.fuse_mask_cuda_copy = weights_tensor.repeat(1, 3, 1, 1)
                torch.cuda.synchronize()
                t8 = time.time()
                # print(f"【{frameId} 融合】: {(t8 - t7)*1000:.3f}ms")
            else:
                self.fuse_mask_cuda_copy = self.fuse_mask_cuda.repeat(this_batch, 1, 1, 1)
            if blend_dynamic == "xseg":
                fuse_res = B_img * self.fuse_mask_cuda_copy + (1 - self.fuse_mask_cuda_copy) * fake_B
            else:
                fuse_res = fake_B * self.fuse_mask_cuda_copy + (1 - self.fuse_mask_cuda_copy) * B_img
            torch.cuda.synchronize()
            model_et = time.time()
            output_img_list = []
            for i in range(this_batch):
                mask_B_pre_list[i][int(5 * (self.img_size / 256)):int(-5 * (self.img_size / 256)),
                 int(5 * (self.img_size / 256)):int(-5 * (self.img_size / 256))] = (fuse_res[i] * 255).permute(1, 2, 0).cpu().detach().numpy()[..., ::-1]
                output_img_list.append(mask_B_pre_list[i])
                # if i==0:
                #     cv2.imwrite(f"output_img_list_{frameId}.png", output_img_list[i])

            return output_img_list

    def blendImages(self, src, dst, featherAmount=0.1):
        torch.cuda.synchronize()
        src_gpu = src
        dst_gpu = dst
        composed_gpu = self.w_gpu * src_gpu + (1 - self.w_gpu) * dst_gpu
        composedImg = composed_gpu
        return composedImg

#  face_data_dict = {
#     0: {'imgs_data': array(), 'idx': 0, 
#     'bounding_box_p': array([ 199,  614, 1263, 1628]), # x1,x2,y1,y2 （415，365)
#     'landmarks': array([[1298.00185713,  340.68917418]]), 
#     'bounding_box': array([ 235,  682, 1219, 1667]), # (447,448)
#     'crop_lm': array([[ 46.90735267,  62.89333407],]), # 裁出人脸后的坐标点
#     'crop_img': array()}, 
#     1: {'imgs_data': array(), 'idx': 1, 
#     'bounding_box_p': array([ 199,  614, 1263, 1628]), 
#     'landmarks': array([[1298.00688814,  340.39061153]]),
#      'bounding_box': array([ 235,  682, 1219, 1667]), 
#      'crop_lm': array([[ 46.91033983,  62.71566592]]), 
#      'crop_img': array()}, 
#      2: {'imgs_data': array(), 'idx': 2, 
#      'bounding_box_p': array([ 204,  618, 1256, 1612]),  #（414，356）
#      'landmarks': array([[1288.8067086 ,  342.85517442]]),
#       'bounding_box': array([ 237,  686, 1209, 1658]),  # 
#       'crop_lm': array([[ 47.27969819,  62.71152872]]), #（449，449）
#       'crop_img': array()},
#        3: {'imgs_data': array(), 'idx': 3, 
#        'bounding_box_p': array([ 201,  621, 1242, 1600]),  #（420，358）
#        'landmarks': array([[1277.98446088,  339.82835269]]), 
#        'bounding_box': array([ 239,  686, 1198, 1646]),  # （447，448）
#        'crop_lm': array(), 
#        'crop_img': array()}
#        }
# params = [(1440, 2560, 3), 1, [array(), array(), array(), array()], 
# [1219, 1219, 1209, 1198], [1667, 1667, 1658, 1646], [235, 235, 237, 239], [682, 682, 686, 686]],
# frameId :136