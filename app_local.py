from cy_app_local import *

if __name__ == '__main__':
    
    TransDhTask.instance()
    time.sleep(15)
    logger.info("******************* TransDhServer服务启动 *******************")
    if not os.path.exists(GlobalConfig.instance().temp_dir):
        logger.info("创建临时目录")
        os.makedirs(GlobalConfig.instance().temp_dir)
    if not os.path.exists(GlobalConfig.instance().result_dir):
        logger.info("创建结果目录")
        os.makedirs(GlobalConfig.instance().result_dir)
    app.run(
        host=str(GlobalConfig.instance().server_ip),
        port=int(GlobalConfig.instance().server_port),
        debug=False,
        threaded=False)