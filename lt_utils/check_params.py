# -*-coding: utf-8 -*-
# @Time : 2024/10/31 11:18
# <AUTHOR> lishengjing
# @Email : <EMAIL>
# @File : check_params.py

import os
import json
from loguru import logger
import time
import requests
# import cv2
import platform
from lt_utils.log_msg import logging_params,locate_exception
from lt_utils.wo_oss import oss_server,check_env
# from lt_utils.media_type_detector import file_type

class Var():
    def __init__(self):
        pass


class  Check_params():
    # ----------------------------------------------
    # 检验参数:
    # img_url:目标图片url
    # audio_url:目标音频url
    # callback_url:结果回调接口，由调用方指定，用于将处理结果通知给调用方
    def __init__(self,job_id,job_params,JOB_ALGORITHM):

        self._C = {'code': 0, 'msg': 'success!', 'algorithm': JO<PERSON>_ALGORITHM, 'job_id': job_id,"result_path":"",'speaker_id':"",'mask_path':""}


        self.is_check=True
        self.job_params = job_params
        self.job_id =job_id
        self.spk_models = "spk_models"
        os.makedirs(self.spk_models, exist_ok=True)
        self.version = job_params.get('version', 'v2')
        self.place_id = job_params.get('place_id', '100001')
        self.callback_url =job_params.get('callback_url', None)
        self.callback_id = os.path.split(self.callback_url)[-1] if self.callback_url is not None else  '12700001'
        self.job_msg=""
        
        self.parse_additional(job_params.get('additional', {}))
        if JOB_ALGORITHM.find('train') >= 0:
            self.parse_train_params(job_params)
        elif JOB_ALGORITHM.find('infer') >= 0:
            self.parse_infer_params(job_params)
        
        self.workspace ='workspace' # 缓存文件的文件夹tts_train
        os.makedirs(self.workspace, exist_ok=True)
        platform_sys = platform.system()
        self.python_exec = 'python' if platform_sys == 'Linux' else r"D:/Program_Files/anaconda3/envs/rvc/python.exe"

    def is_checked(self):
        return self.is_check

    def parse_additional(self,additional):

        self.presigned_days = additional.get("presigned_days", 7) #去除混响

        self.parse_environment(additional)  # 解析当前系统环境

    def parse_train_params(self,job_params):
        self.speaker_id = job_params.get('speaker_id', None)
        self.video_url = job_params.get('video_url', None)
        self.audio_url = job_params.get('audio_url', None)
        self.mask_url = job_params.get('mask_url', None)
        self.use_mask = job_params.get('use_mask', 0)
        self.blend_threshold = job_params.get('blend_threshold', 0.0)
        self.blend_sigma = job_params.get('blend_sigma', 35.0)
        self.blend_kernel_size = job_params.get('blend_kernel_size', 15)
        for (k, v) in zip([self.callback_url, self.speaker_id,self.video_url],["callback_url", "speaker_id","video_url"]):
            if k is None:
                self.is_check, self.job_msg = False, "Parameter {} is None!".format(v)
        self._C.update({"speaker_id":self.speaker_id})


    def parse_infer_params(self,job_params):
        self.speaker_id = job_params.get('speaker_id', None)
        self.video_url = job_params.get('video_url', None)
        self.audio_url = job_params.get('audio_url', None)
        self.mask_url = job_params.get('mask_url', None)
        self.use_mask = job_params.get('use_mask', 0)
        self.blend_threshold = job_params.get('blend_threshold', 0.0)
        self.blend_sigma = job_params.get('blend_sigma', 35.0)
        self.blend_kernel_size = job_params.get('blend_kernel_size', 15)
        for (k, v) in zip([self.callback_url, self.speaker_id,self.audio_url],["callback_url", "speaker_id","audio_url"]):
            if k is None:
                self.is_check, self.job_msg = False, "Parameter {} is None!".format(v)
        self._C.update({"speaker_id":self.speaker_id})


    def parse_environment(self, additional):
        """
        WOM_AI_ENVIRONMENT 优先使用系统存在的环境变量，其次才使用传参的环境变量，共有4种参数：
        DEBUG:
        Development:
        Production：
        None:环境变量中没有，将使用默认值
        """
        model_oss_server=Var()
        WOM_AI_ENVIRONMENT = os.getenv('WOM_AI_ENVIRONMENT', None)
        self.wom_ai_environment = 'Production'

        if additional is not None:
            try:
                self.wom_ai_environment = additional.get("WOM_AI_ENVIRONMENT", 'Production')
            except:
                logger.error("additional error : {}".format(additional))

        if WOM_AI_ENVIRONMENT is None:  # 用传参环境变量
            if self.wom_ai_environment in ["Debug", "Development"]:  # 不连接oss，只是本地开发
                logger.info("使用参数{}环境!本地开发，将系统参数注入！".format(self.wom_ai_environment))
                os.environ["WOM_AI_ENVIRONMENT"] = self.wom_ai_environment  # 通过赋值环境用于测试
                logger.info("测试获取 {}".format(os.getenv('WOM_AI_ENVIRONMENT', 111)))

            else:
                logger.info("本地接收到使用Production环境的传参，将系统参数Production注入！")
                os.environ["WOM_AI_ENVIRONMENT"] = "Production"

        elif WOM_AI_ENVIRONMENT in ["Debug", 'Development', 'Production']:  # 线上的测试/正式环境，由线上容器建立的时候触发系统注入
            logger.info("接收到系统变量 {}".format(WOM_AI_ENVIRONMENT))
            self.wom_ai_environment = WOM_AI_ENVIRONMENT
        else:
            logger.error("WOM_AI_ENVIRONMENT 配置错误！:{},使用正式环境的对象存储".format(WOM_AI_ENVIRONMENT))
            os.environ["WOM_AI_ENVIRONMENT"] = "Production"

        # reconect oss
        if WOM_AI_ENVIRONMENT != oss_server.WOM_AI_ENVIRONMENT:
            cenv_is_check, _WOM_AI_ENVIRONMENT, _AWS_SERVER_PUBLIC_KEY, _AWS_SERVER_SECRET_KEY, _BUCKET_NAME, _REGION_NAME, _ENDPOINT_URL = check_env()
            logger.info("重新连接 ：{} 环境的oss".format(_AWS_SERVER_PUBLIC_KEY))
            oss_server.reconnect_oss(_AWS_SERVER_PUBLIC_KEY, _AWS_SERVER_SECRET_KEY, _BUCKET_NAME, _ENDPOINT_URL,
                                     _WOM_AI_ENVIRONMENT)
        if WOM_AI_ENVIRONMENT != oss_server.WOM_AI_ENVIRONMENT:
            cenv_is_check, _WOM_AI_ENVIRONMENT, _AWS_SERVER_PUBLIC_KEY, _AWS_SERVER_SECRET_KEY, _BUCKET_NAME, _REGION_NAME, _ENDPOINT_URL = check_env()
            logger.info("重新连接模型保存路径 ：{} 环境的oss".format(_WOM_AI_ENVIRONMENT))
            oss_server.reconnect_oss(_AWS_SERVER_PUBLIC_KEY, _AWS_SERVER_SECRET_KEY, _BUCKET_NAME, _ENDPOINT_URL,
                                           _WOM_AI_ENVIRONMENT)

        # if self.wom_ai_environment in ['Development','Production'] and not self.oss_server.connect_seccuss:
        #     logger.error("连接oss失败！ 请检查oss参数！")
        #     self.is_check, self.job_msg = False, "连接oss失败！ 请检查oss参数！!"

  
    def parse_oss_storage(self, oss_storage):
        """
        解析oss参数
            "access_key": "GQ832XMUO6YZEO5FTXVE",
            "secret_key": "iiEUUDAHhGTGYdwtEUDZIQSH9EPMJXELuvaPXScd",
            "endpoint": "http://172.16.17.245:8880",
            "bucket_name": "aigo-2dtalkingface-meta",
        """

        self.access_key=oss_storage.get("access_key",None)
        self.secret_key=oss_storage.get("secret_key",None)
        self.endpoint=oss_storage.get("endpoint",None)
        self.bucket_name=oss_storage.get("bucket_name",None)


    def callbacks(self,files=None,try_time=3):
        if self.wom_ai_environment == 'Debug': # 测试的时候不callback ，只是打印
            logger.debug(self._C)
            return True
        else: # callback ，上线后用
            try:
                post_time = 0
                has_send = False
                t0 = time.time()
                while post_time < try_time and not has_send:
                    try:
                        resp = requests.post(self.callback_url,
                                      data=json.dumps(self._C),
                                      timeout=10,
                                      headers={"Content-Type": "application/json; charset=UTF-8"})
                        # resp = requests.post(self.callback_url,
                        #                      data=self._C,
                        #                      files=files,
                        #                      timeout=10,
                        #                      )
                        logger.info("Call back Time: " + str((time.time() - t0) * 1000) + 'ms\n')
                        text = resp.text
                        logger.info("call_back msg : {}".format(json.dumps(self._C)))
                        logger.info("resp msg : {}".format(text))
                        has_send = True
                    except:
                        logger.error("Error: Call back failed! : url:{},msg:{}\n".format(self.callback_url, self._C))
                    post_time += 1
                return True
            except Exception as E:
                logger.error(locate_exception())
                logger.error(E)
                logger.error("Error: Call back failed!\n")
                return False

    def callback_no_success(self,is_success,msg):
        if not is_success:
            self._C.update({'code':-1,'msg':'{}'.format(msg)})
            self.callbacks()
            logger.error("任务失败 : {}".format(msg))

