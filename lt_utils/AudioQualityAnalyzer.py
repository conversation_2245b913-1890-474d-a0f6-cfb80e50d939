# -*-coding: utf-8 -*-
# @Time : 2024/11/20 11:54
# <AUTHOR> lishengjing
# @Email : <EMAIL>
# @File : AudioQualityAnalyzer.py

import numpy as np
import librosa
import soundfile as sf
from scipy import signal
from scipy.stats import kurtosis
from loguru import logger
from lt_utils.log_msg import logger_error

class Audio_Quality_Analyzer(object):
    def __init__(self):
        """初始化音频质量分析器，设置各项指标的阈值"""
        self.thresholds = {
            'noise_level': -30,      # dB, 噪声水平阈值
            'snr': 15,              # dB, 信噪比阈值
            'reverb': 0.02,          # 混响阈值
            'clarity': 0.5,         # 清晰度阈值
            'distortion': 0.1,      # 失真度阈值
            'dynamic_range': 40,    # dB, 动态范围阈值
            'peak_level': -3,       # dB, 峰值电平阈值
            'spectral_centroid': 3000  # Hz, 频谱中心阈值
        }

    def analyze_audio(self, audio_path):
        """
        分析音频质量并返回详细的评估结果
        
        Args:
            audio_path: 音频文件路径
            
        Returns:
            dict: 包含质量评估结果和处理建议的字典
        """
        try:
            # 加载音频
            y, sr = librosa.load(audio_path, sr=None)
            
            # 计算各项指标
            metrics = {
                'noise_level': self._estimate_noise_level(y),
                'snr': self._estimate_snr(y),
                'reverb': self._estimate_reverb(y, sr),
                'clarity': self._estimate_clarity(y, sr),
                'distortion': self._estimate_distortion(y),
                'dynamic_range': self._estimate_dynamic_range(y),
                'peak_level': self._estimate_peak_level(y),
                'spectral_centroid': self._estimate_spectral_centroid(y, sr),
                'background_separation': self._estimate_background_separation_need(y, sr)
            }
            
            # 评估处理需求
            processing_needs = self._evaluate_processing_needs(metrics)
            
            # 生成质量评分
            quality_score = self._calculate_quality_score(metrics)
            
            return {
                'metrics': metrics,
                'processing_needs': processing_needs,
                'quality_score': quality_score,
                'recommendations': self._generate_recommendations(metrics)
            }
            
        except Exception as e:
            logger.error(f"音频质量分析失败: {str(e)}")
            return None

    def _estimate_noise_level(self, y):
        """估计噪声水平"""
        frame_length = 2048
        hop_length = 512
        
        # 计算短时能量
        energy = librosa.feature.rms(y=y, frame_length=frame_length, hop_length=hop_length)
        
        # 取最安静的10%片段的平均能量作为噪声水平估计
        noise_level = np.percentile(energy, 10)
        return 20 * np.log10(noise_level + 1e-10)

    def _estimate_snr(self, y):
        """估计信噪比"""
        signal_power = np.mean(y ** 2)
        frame_length = 2048
        hop_length = 512
        
        # 使用最安静部分估计噪声功率
        energy = librosa.feature.rms(y=y, frame_length=frame_length, hop_length=hop_length) ** 2
        noise_power = np.percentile(energy, 10)
        
        if noise_power == 0:
            return float('inf')
            
        return 10 * np.log10(signal_power / noise_power)

    def _estimate_reverb(self, y, sr):
        """估计混响程度"""
        # 计算包络
        envelope = np.abs(signal.hilbert(y))
        
        # 计算包络的衰减率
        decay_rate = np.mean(np.diff(envelope)) / np.mean(envelope)
        
        # 计算RT60（混响时间）
        rt60 = self._estimate_rt60(y, sr)
        
        return {
            'decay_rate': np.abs(decay_rate),
            'rt60': rt60
        }

    def _estimate_rt60(self, y, sr):
        """估计RT60混响时间"""
        envelope = np.abs(signal.hilbert(y))
        
        # 将包络转换为dB
        envelope_db = 20 * np.log10(envelope + 1e-10)
        
        # 找到最大值后的衰减部分
        max_idx = np.argmax(envelope_db)
        decay = envelope_db[max_idx:]
        
        if len(decay) < sr:  # 确保有足够的数据点
            return None
            
        # 计算-60dB衰减所需时间
        threshold = np.max(decay) - 60
        for i, value in enumerate(decay):
            if value <= threshold:
                return i / sr
                
        return None

    def _estimate_clarity(self, y, sr):
        """
        估计语音清晰度
        使用频谱熵和调制频谱指标
        """
        # 计算频谱熵
        spec = np.abs(librosa.stft(y))
        spec_norm = spec / np.sum(spec)
        spectral_entropy = -np.sum(spec_norm * np.log2(spec_norm + 1e-10))
        
        # 计算调制频谱
        frame_length = 2048
        hop_length = 512
        mspec = np.abs(librosa.feature.melspectrogram(y=y, sr=sr))
        mod_spec = np.abs(librosa.feature.tempogram(y=y, sr=sr))
        
        # 综合评分
        clarity_score = 1.0 - (spectral_entropy / np.log2(spec.shape[0]))
        
        return clarity_score

    def _estimate_distortion(self, y):
        """
        估计信号失真度
        使用峰值因子和峰度
        """
        # 计算峰值因子 (Crest Factor)
        peak = np.max(np.abs(y))
        rms = np.sqrt(np.mean(y**2))
        crest_factor = peak / (rms + 1e-10)
        
        # 计算峰度
        kurt = kurtosis(y)
        
        # 综合评分
        distortion_score = (crest_factor - 1.414) / 10.0  # 1.414是理想正弦波的峰值因子
        distortion_score = np.clip(distortion_score, 0, 1)
        
        return {
            'crest_factor': crest_factor,
            'kurtosis': kurt,
            'distortion_score': distortion_score
        }

    def _estimate_dynamic_range(self, y):
        """估计动态范围"""
        # 计算RMS值的分布
        frame_length = 2048
        hop_length = 512
        rms = librosa.feature.rms(y=y, frame_length=frame_length, hop_length=hop_length)
        
        # 计算动态范围（以dB为单位）
        rms_db = 20 * np.log10(rms + 1e-10)
        dynamic_range = np.percentile(rms_db, 95) - np.percentile(rms_db, 5)
        
        return dynamic_range

    def _estimate_peak_level(self, y):
        """估计峰值电平"""
        peak = np.max(np.abs(y))
        return 20 * np.log10(peak + 1e-10)



    def _estimate_background_separation_need(self, y, sr):
        """
        评估是否需要使用UVR5模型进行预处理
        
        Args:
            y: 输入音频信号
            sr: 采样率
            
        Returns:
            bool: 是否需要UVR5预处理
        """
        try:
            # 1. 计算短时能量变化
            frame_length = 2048
            hop_length = 512
            rms = librosa.feature.rms(y=y, frame_length=frame_length, hop_length=hop_length)[0]
            rms_db = 20 * np.log10(rms + 1e-10)
            
            # 2. 计算能量突变点
            rms_diff = np.diff(rms_db)
            sudden_changes = np.where(np.abs(rms_diff) > 10)[0]  # 10dB的突变阈值
            
            # 3. 计算频谱对比度
            spec = np.abs(librosa.stft(y))
            contrast = librosa.feature.spectral_contrast(S=spec, sr=sr)
            mean_contrast = np.mean(contrast)
            
            # 4. 计算信号的峰度（用于检测瞬态信号）
            kurt = kurtosis(y)
            
            # 评估条件：
            # 1. 存在明显的能量突变
            has_sudden_changes = len(sudden_changes) > 5  # 每段音频超过5个突变点
            
            # 2. 频谱对比度高（表示有明显的前景/背景分离）
            high_contrast = mean_contrast > 20
            
            # 3. 信号峰度高（表示有很多瞬态信号）
            high_kurtosis = abs(kurt) > 5
            
            # 如果满足以下条件之一，建议使用UVR5预处理
            need_uvr5 = (
                (has_sudden_changes and high_contrast) or
                (has_sudden_changes and high_kurtosis) or
                (high_contrast and high_kurtosis)
            )
            
            logger.info(f"音频特征分析结果:能量突变点数量: {len(sudden_changes)},平均频谱对比度: {mean_contrast:.2f},信号峰度: {kurt:.2f},是否建议使用UVR5预处理: {need_uvr5}")

            return need_uvr5
            
        except Exception as e:
            logger_error(f"UVR5预处理评估失败: {str(e)}")
            return False



    def _estimate_spectral_centroid(self, y, sr):      
        """估计频谱中心"""
        centroid = librosa.feature.spectral_centroid(y=y, sr=sr)
        return np.mean(centroid)

    def _evaluate_processing_needs(self, metrics):
        """评估是否需要进行音频处理"""
        needs = {
            'need_denoise': False,
            'need_dereverberation': False,
            'need_dynamic_processing': False,
            'need_normalization': False,
            'need_clarity_enhancement': False,
            'need_background_separation': False  # 新增背景分离需求
        }
        
        # 评估降噪需求
        if (metrics['noise_level'] > self.thresholds['noise_level'] or 
            metrics['snr'] < self.thresholds['snr']):
            needs['need_denoise'] = True
            
        # 评估去混响需求
        # if metrics['reverb']['decay_rate'] > self.thresholds['reverb']:
        if metrics['reverb']['rt60'] > self.thresholds['reverb']:
            needs['need_dereverberation'] = True
            
        # 评估动态处理需求
        if metrics['dynamic_range'] > self.thresholds['dynamic_range']:
            needs['need_dynamic_processing'] = True
            
        # 评估音量标准化需求
        if abs(metrics['peak_level']) > abs(self.thresholds['peak_level']):
            needs['need_normalization'] = True
            
        # 评估清晰度增强需求
        if metrics['clarity'] < self.thresholds['clarity']:
            needs['need_clarity_enhancement'] = True
            
        # 评估失真处理需求
        if metrics['distortion']['distortion_score'] > self.thresholds['distortion']:
            needs['need_distortion_reduction'] = True
            
        # 评估背景分离需求
        if metrics['background_separation']:
            needs['need_background_separation'] = True
            
        return needs

    def _calculate_quality_score(self, metrics):
        """计算综合质量评分（0-100）"""
        weights = {
            'snr': 0.3,
            'clarity': 0.2,
            'distortion': 0.2,
            'dynamic_range': 0.15,
            'reverb': 0.15
        }
        
        scores = {
            'snr': min(max(metrics['snr'] / 30.0, 0), 1),  # 归一化到0-1
            'clarity': metrics['clarity'],
            'distortion': 1 - metrics['distortion']['distortion_score'],
            'dynamic_range': min(metrics['dynamic_range'] / 60.0, 1),
            'reverb': 1 - min(metrics['reverb']['decay_rate'], 1)
        }
        
        total_score = sum(score * weights[key] for key, score in scores.items())
        return total_score * 100  # 转换为0-100分制

    def _generate_recommendations(self, metrics):
        """生成处理建议"""
        recommendations = []
        
        # 评估降噪需求
        if (metrics['noise_level'] > self.thresholds['noise_level'] or 
            metrics['snr'] < self.thresholds['snr']):
            recommendations.append("建议进行降噪处理")
            
        # 评估去混响需求
        if metrics['reverb']['decay_rate'] > self.thresholds['reverb']:
            recommendations.append("建议进行去混响处理")
            
        # 评估动态处理需求
        if metrics['dynamic_range'] > self.thresholds['dynamic_range']:
            recommendations.append("建议进行动态范围压缩")
            
        # 评估音量标准化需求
        if abs(metrics['peak_level']) > abs(self.thresholds['peak_level']):
            recommendations.append("建议进行音量标准化")
            
        # 评估清晰度增强需求
        if metrics['clarity'] < self.thresholds['clarity']:
            recommendations.append("建议进行清晰度增强")
            
        # 评估失真处理需求
        if metrics['distortion']['distortion_score'] > self.thresholds['distortion']:
            recommendations.append("建议检查并减少信号失真")
            
        # 评估背景分离需求
        if metrics['background_separation']:
            recommendations.append("建议进行背景分离处理")
            
        return recommendations

    @staticmethod
    def _apply_dynamic_compression(voice_path,output_file, threshold_db=-20, ratio=4, attack_ms=5, release_ms=50):
        """
        应用动态范围压缩
        
        Args:
            voice_path: 输入音频路径
            threshold_db: 压缩阈值(dB)
            ratio: 压缩比率
            attack_ms: 启动时间(ms)
            release_ms: 释放时间(ms)
        """
        try:
            # 将信号转换为dB
            y, sr = librosa.load(voice_path)
            db = 20 * np.log10(np.abs(y) + 1e-10)
            
            # 计算增益衰减
            db_reduced = np.clip(db - threshold_db, 0, None)
            gain_reduction = db_reduced * (1 - 1/ratio)
            
            # 应用时间常数
            attack_samples = int(attack_ms * 0.001 * sr)
            release_samples = int(release_ms * 0.001 * sr)
            
            # 平滑增益变化
            gain_reduction_smoothed = np.zeros_like(gain_reduction)
            for i in range(1, len(y)):
                if gain_reduction[i] > gain_reduction_smoothed[i-1]:
                    coeff = 1 - np.exp(-1/attack_samples)
                else:
                    coeff = 1 - np.exp(-1/release_samples)
                gain_reduction_smoothed[i] = gain_reduction_smoothed[i-1] + \
                    coeff * (gain_reduction[i] - gain_reduction_smoothed[i-1])
            
            # 应用增益
            gain_linear = np.power(10, -gain_reduction_smoothed/20)
            y_processed = y * gain_linear
            sf.write(output_file, y_processed, sr)
            return True, ""

        except Exception as e:
            logger_error(f"动态范围压缩失败: {str(e)}")
            return False, str(e)
    
    @staticmethod
    def _apply_enhance_clarity(voice_path,output_file):
        """
        增强音频清晰度
        
        Args:
            y: 输入音频信号
            sr: 采样率
        """
        try:
            # 1. 高频提升
            y, sr = librosa.load(voice_path, sr=None)
            nyquist = sr // 2
            cutoff_freq = 3000  # Hz
            order = 4
            
            b, a = signal.butter(order, cutoff_freq/nyquist, btype='highpass')
            high_freq = signal.filtfilt(b, a, y)
            
            # 2. 中频增强
            b2, a2 = signal.butter(order, [1000/nyquist, 4000/nyquist], btype='bandpass')
            mid_freq = signal.filtfilt(b2, a2, y)
            
            # 混合信号
            enhanced = y + 0.3 * high_freq + 0.5 * mid_freq
            
            # 归一化
            enhanced = enhanced / np.max(np.abs(enhanced))
            
            sf.write(output_file, enhanced, sr)
            return True, ""
        except Exception as e:
            logger_error(f"清晰度增强失败: {str(e)}")
            return False, str(e)
    
    @staticmethod
    def _apply_reduce_distortion(voice_path,output_file, clip_threshold=0.95):
        """
        减少信号失真
        
        Args:
            y: 输入音频信号
            clip_threshold: 削波阈值
        """
        try:
            logger.info(f"开始进行失真处理,输入音频路径: {voice_path},输出音频路径: {output_file}")
            y, sr = librosa.load(voice_path, sr=None)
            # 1. 软削波处理
            def soft_clip(x, threshold):
                return np.where(
                    np.abs(x) <= threshold,
                    x,
                    threshold * np.sign(x) + (1 - threshold) * np.tanh((np.abs(x) - threshold) / (1 - threshold)) * np.sign(x)
                )
            
            # 2. 应用软削波
            y_processed = soft_clip(y, clip_threshold)
            
            # 3. 去除直流偏置
            y_processed = signal.detrend(y_processed)
            
            # 4. 应用低通滤波器去除高频失真
            nyquist = sr // 2
            cutoff = 18000  # Hz
            order = 6
            b, a = signal.butter(order, cutoff/nyquist, btype='lowpass')
            y_processed = signal.filtfilt(b, a, y_processed)
            
            sf.write(output_file, y_processed, sr)
            return True, ""
        except Exception as e:
            logger_error(f"失真处理失败: {str(e)}")
            return False, str(e)
    

    def _apply_normalization( voice_path, output_file, target_db=-20):
        """
        应用音量标准化
        
        Args:
            voice_path: 输入音频文件路径
            output_file: 输出音频文件路径
            target_db: 目标音量（dB）
            
        Returns:
            tuple: (是否成功, 错误信息)
        """
        try:
            y, sr = librosa.load(voice_path, sr=None)
            
            # 计算当前音频的RMS
            current_rms = np.sqrt(np.mean(y**2))
            current_db = 20 * np.log10(current_rms + 1e-10)
            
            # 计算需要的增益
            gain_db = target_db - current_db
            gain = 10 ** (gain_db / 20)
            
            # 应用增益
            y_normalized = y * gain
            
            # 防止削波
            max_val = np.max(np.abs(y_normalized))
            if max_val > 1.0:
                y_normalized = y_normalized / max_val
            
            # 保存标准化后的音频
            sf.write(output_file, y_normalized, sr)
            return True, ""
        
        except Exception as e:
            logger.error(f"音量标准化失败: {str(e)}")
            return False, str(e)



    
def process_audio_with_quality_check(input_file, output_file):
    """根据音频质量分析结果进行处理"""
    # 创建分析器实例
    analyzer = Audio_Quality_Analyzer()
    
    # 分析音频质量
    analysis_result = analyzer.analyze_audio(input_file)
    
    if analysis_result is None:
        logger.error("音频质量分析失败")
        return False
        
    logger.info(f"音频质量评分: {analysis_result['quality_score']:.2f}/100")
    logger.info("音频质量指标:")
    for metric, value in analysis_result['metrics'].items():
        logger.info(f"{metric}: {value}")
        
    logger.info("处理建议:")
    for recommendation in analysis_result['recommendations']:
        logger.info(recommendation)
    
    # 读取音频
    y, sr = librosa.load(input_file, sr=None)
    
    # 根据分析结果决定处理流程
    if analysis_result['processing_needs']['need_dereverberation']:
        # 执行去混响
        pass
        
    if analysis_result['processing_needs']['need_denoise']:
        # 执行降噪
        pass
        
    if analysis_result['processing_needs']['need_dynamic_processing']:
        logger.info("正在进行动态范围压缩...")
        y = analyzer._apply_dynamic_compression(y)
        
    if analysis_result['processing_needs']['need_clarity_enhancement']:
        logger.info("正在进行清晰度增强...")
        y = analyzer._enhance_clarity(y, sr)
        
    if analysis_result['processing_needs']['need_distortion_reduction']:
        logger.info("正在进行失真处理...")
        y = analyzer._reduce_distortion(y)
        
    if analysis_result['processing_needs']['need_normalization']:
        # 执行音量标准化
        pass
    
    # 保存处理后的音频
    sf.write(output_file, y, sr)
    logger.info(f"处理完成，已保存至: {output_file}")
    
    return True



def process_audio_with_quality_check(input_file, output_file, uvr5_model=None):
    """
    根据音频质量分析结果进行处理
    
    Args:
        input_file: 输入音频文件路径
        output_file: 输出音频文件路径
        uvr5_model: UVR5模型实例（可选）
    """
    # 创建分析器实例
    analyzer = AudioQualityAnalyzer()
    
    # 读取音频
    y, sr = librosa.load(input_file, sr=None)
    
    # 评估是否需要UVR5预处理
    if analyzer._should_use_uvr5_preprocess(y, sr) and uvr5_model is not None:
        logger.info("正在使用UVR5模型进行预处理...")
        try:
            # 使用UVR5处理音频
            y = uvr5_model.process(y, sr)
            logger.info("UVR5预处理完成")
        except Exception as e:
            logger.error(f"UVR5处理失败: {str(e)}")
    
    # 分析音频质量
    analysis_result = analyzer.analyze_audio(input_file)
    
    if analysis_result is None:
        logger.error("音频质量分析失败")
        return False
    
    # ... 其余处理逻辑保持不变 ...

if __name__ == "__main__":
    # 使用示例
    input_file = "input.wav"
    output_file = "output.wav"
    process_audio_with_quality_check(input_file, output_file)
