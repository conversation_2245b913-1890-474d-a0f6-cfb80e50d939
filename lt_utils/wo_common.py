# -*-coding: utf-8 -*-
# @Time : 2024/4/17 18:16
# <AUTHOR> lishengjing
# @Email : <EMAIL>
# @File : utils.py

import sys
import os
import shutil
import time
import requests
import subprocess
import numpy as np
import platform
import psutil
import signal
from urllib.request import urlretrieve
from loguru import logger
import datetime



def del_files(path):
    try:
        if os.path.isfile(path):
            os.remove(path)
        elif os.path.isdir(path):
            shutil.rmtree(path, ignore_errors=True)
    except:
        pass

# 为了防止模

def get_sizes(fileOrFolderPath):
    """get size for file or folder"""
    totalSize = 0

    if not os.path.exists(fileOrFolderPath):
        return totalSize

    if os.path.isfile(fileOrFolderPath):
        totalSize = os.path.getsize(fileOrFolderPath)  # 5041481
        # return totalSize/1024/1024/1024
        return totalSize/1073741824  # 024*1024*1024=1073741824 byte-->gb

    if os.path.isdir(fileOrFolderPath):
        with os.scandir(fileOrFolderPath) as dirEntryList:
            for curSubEntry in dirEntryList:
                curSubEntryFullPath = os.path.join(fileOrFolderPath, curSubEntry.name)
                if curSubEntry.is_dir():
                    curSubFolderSize = get_sizes(curSubEntryFullPath)  # 5800007
                    totalSize += curSubFolderSize
                elif curSubEntry.is_file():
                    curSubFileSize = os.path.getsize(curSubEntryFullPath)  # 1891
                    totalSize += curSubFileSize/1073741824 # 1024*1024*1024=1073741824 byte-->gb

            return totalSize



# source:https://github.com/crifan/crifanLibPython/blob/master/crifanLib/crifanFile.py
def files_sorted_by_time(file_path):
    return sorted(os.listdir(file_path), key=lambda x: os.path.getmtime(os.path.join(file_path, x)))


system=platform.system()
def kill_process(pid):
    def kill_proc_tree(pid, including_parent=True):
        try:
            parent = psutil.Process(pid)
        except psutil.NoSuchProcess:
            # Process already terminated
            return

        children = parent.children(recursive=True)
        for child in children:
            try:
                os.kill(child.pid, signal.SIGTERM)  # or signal.SIGKILL
            except OSError:
                pass
        if including_parent:
            try:
                os.kill(parent.pid, signal.SIGTERM)  # or signal.SIGKILL
            except OSError:
                pass

    if (system == "Windows"):
        cmd = "taskkill /t /f /pid %s" % pid
        os.system(cmd)
    else:
        kill_proc_tree(pid)

def clean_path(path_str):
    if platform.system() == 'Windows':
        path_str = path_str.replace('/', '\\')
    return path_str.strip(" ").strip('"').strip("\n").strip('"').strip(" ")


def upload_file(params,file_path,oss_server,auto_switch_oss,result_oss_dir=None):
    _Msg = ""
    t5 = time.time()
    media_name = os.path.basename(file_path)
    if params.wom_ai_environment == "Debug":
        logger.info("Debug mode, no upload")
        result_file = datetime.datetime.now().strftime("%Y-%m-%d_%H-%M-%S_%f") + "_" + media_name
        shutil.copy(file_path, result_file)
        return True, 'success',result_file

    else:  # 上传到oss
        auto_switch_oss(params.wom_ai_environment,oss_server)
        if not oss_server.connect_seccuss:  # 检查oss链接是否有问题
            return False, "oss连接失败", None

        # 上传结果
        result_oss_dir = "watermarkresults" + "/" + params.place_id + "/" + datetime.date.today().strftime(
                "%Y-%m-%d") + "/"  if result_oss_dir is None else result_oss_dir
        media_oss_path = result_oss_dir + os.path.basename(file_path)
    
        if os.path.exists(file_path):
            oss_server.process_upload_file_2_oss(file_path, media_oss_path, log_info=True)
            if params.presigned_days is not None and params.presigned_days > 0:  # 签名
                    media_oss_path = oss_server.presigned(media_oss_path, oss_server.BUCKET_NAME,
                                                                params.presigned_days)
            t6 = time.time()
            _Msg += ">> upload time:{:.3f}s <<  ".format(t6-t5)
            logger.info("【success! >> upload time:{:.3f}s <<  {}】".format(t6-t5,_Msg))
            logger.info("文件数据上传: {} - >{}".format(file_path, media_oss_path))
            return True,  'success!',media_oss_path
            
        else:
            logger.info("文件数据上传失败: {}".format(file_path))
            return False,"文件数据上传失败: {}".format(file_path),None

import cv2
class videocap_local_imgs():

    def __init__(self,imgs,use_npy=False):
        self.is_open = False
        self.idx = 0
        self.len = 0
        self.use_npy = use_npy
        if isinstance(imgs,str): # 输入地址
            if os.path.exists(imgs) and os.path.isdir(imgs):
                self.img_list = [os.path.join(imgs,f) for f in os.listdir(imgs) if f.endswith(('.png','.jpg','.jpeg','.npy'))]
                self.img_list = sorted(self.img_list)
                self.len = len(self.img_list)
                self.is_open = True

        elif isinstance(imgs,list):
            self.img_list = imgs
            self.len = len(self.img_list)
            self.is_open = True
        else:
            raise ValueError("imgs must be a string or a list")
    def read(self):
        if not self.is_open:
            return False,None
        if self.idx >= self.len:
            return False,None
        # 读取图片
        t1 = time.time()
        if self.use_npy:
            img = np.load(self.img_list[self.idx],mmap_mode='r')
        else:
            img = cv2.imread(self.img_list[self.idx])
        self.idx += 1
        logger.info(f"read img: {self.img_list[self.idx-1]},use time: {(time.time()-t1)*1000}ms")
        return True,img
    def get_frame_count(self):
        return self.len
    def release(self):
        self.is_open = False
    
    def isOpened(self):
        return self.is_open

def check_mp4(video_file,start_cmd,logger_error,identify_file):
    if video_file is None:
        return False,video_file,"训练视频下载失败,请检查视频链接是否正确 :{}".format(video_file)
    
    _filetype = identify_file(video_file)
    if not _filetype in ['video']:
        return False,video_file,"训练视频格式错误 :{}".format(_filetype)
    
    # 转换成mp4
    if not video_file.endswith('.mp4'):
        new_video_file = os.path.splitext(video_file)[0] + ".mp4"
        # 使用ffmpeg转换
        try:
            cmd = ['ffmpeg','-i',video_file,'-c:v','libx264','-c:a','aac',new_video_file]
            logger.info(f"视频不是mp4格式，转换视频命令：{' '.join(cmd)}")
            is_success, _msg = start_cmd(cmd,timeout=600)
            if not is_success:
                logger.error(f"转换视频失败: {_msg}")
                return False,video_file,"训练视频格式错误,不能转化为mp4 "+_msg
            video_file = new_video_file
            logger.info(f"转换视频成功，新视频路径为：{video_file}")
        except Exception as e:
            logger_error(e)
            return False,video_file,"训练视频格式错误,不能转化为mp4 "+str(e)
    return True,video_file,'success'
        
def get_first_frame_from_video(video_file,download_dir,logger_error):
    # 提取首帧 
    preview_img_path = os.path.join(download_dir, "preview.png")
    try:
        cap = cv2.VideoCapture(video_file)
        if not cap.isOpened():
            return False,preview_img_path, "提取首帧失败"

        ret, frame = cap.read()
        if not ret:
            return False,preview_img_path, "提取首帧失败"
        cv2.imwrite(preview_img_path, frame)
        cap.release()
    except Exception as e:
        logger_error(e)

        return False,preview_img_path, "提取首帧失败"
    
    logger.info(f"提取首帧成功，路径为：{preview_img_path}")
    return True,preview_img_path,"success"

if __name__ == '__main__':
    # ext_path = r"D:\gitlab\code\humanmatting\workspace\result"
    # png_path = r'D:\gitlab\code\humanmatting\workspace\tmp_result'
    # src_path = r"D:\gitlab\code\humanmatting\workspace\source.mp4"
    #
    # # # replace bg
    # bg_path = r"C:\Users\<USER>\Desktop\bgimg.jpg"
    # png_path2 = r'D:\gitlab\code\humanmatting\workspace\tmp_result2'
    # os.makedirs(png_path2,exist_ok=True)

    src = r'D:\gitlab\docker\wotts\models\speaker_models\123456'
    dst = r'D:\gitlab\code\wotts\workspace\123456'
    format ='zip'
    shutil.make_archive(dst,format,src)


def locate_exception():
    """
    locate error filename object line
    """
    exc_type, exc_obj, exc_tb = sys.exc_info()
    fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
    return "{},{},{}".format(exc_type, fname, exc_tb.tb_lineno)