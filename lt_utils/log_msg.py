# -*-coding: utf-8 -*-
# @Time : 2023/12/25 15:20
# <AUTHOR> lishengjing
# @Email : <EMAIL>
# @File : log_msg.py
import json
import sys
import os
import time
from loguru import logger

def print_colors(text,color='red',):
    colors = {"red":"\033[31m", "green":"\033[32m", "blue":"\033[34m","black":"\033[30m","yellow":"\033[33m","cyan":"\033[36m","white":"\033[37m"}
    if color is not None and color.lower() in colors.keys():
        text = colors[color] + text + '\033[0m\n'
    return text


def write_log(text):
    f = open('logs.log', 'a')
    sys.stdout.write(text+'\n')
    f.write(text+'\n')
    f.close()
# -------------------------------------------------------------------------------------------------



algo_2_log_msg= {
    'text_2_img': [
        'sub_algorithm','base_model','prompt','negative_prompt',
        'sampler_name','width','height','n_iter','batch_size',
        'seed','steps','cfg_scale','callback_url','vae_model','refiner_checkpoint',"refiner_switch_at","extensions"
    ],
    "clip_interrogator": [
        'sub_algorithm',
        'callback_url'
    ],
    "blip_interrogator":[
        'sub_algorithm',
        'callback_url'
    ],
    'img2img':[
        'sub_algorithm','base_model','prompt','negative_prompt',"scale_by","img2img_mode",
        'sampler_name','width','height','n_iter','batch_size','vae_model',
        'seed','steps','cfg_scale','callback_url','denoising_strength',"extensions"
    ],
    "sketch":[
        'sub_algorithm','base_model','prompt','negative_prompt',"scale_by","img2img_mode",
        'sampler_name','width','height','n_iter','batch_size','vae_model',
        'seed','steps','cfg_scale','callback_url','denoising_strength',"extensions"
    ],
    "inpaint":[
        'sub_algorithm','base_model','prompt','negative_prompt',"scale_by","img2img_mode",
        'sampler_name','width','height','n_iter','batch_size','vae_model',
        'seed','steps','cfg_scale','callback_url','denoising_strength',"extensions"
    ],
    "inpain_sketch":[
        'sub_algorithm','base_model','prompt','negative_prompt',"scale_by","img2img_mode",
        'sampler_name','width','height','n_iter','batch_size','vae_model',
        'seed','steps','cfg_scale','callback_url','denoising_strength',"extensions"
    ],
    "upload_mask":[
        'sub_algorithm','base_model','prompt','negative_prompt',"scale_by","img2img_mode",
        'sampler_name','width','height','n_iter','batch_size','vae_model',
        'seed','steps','cfg_scale','callback_url','denoising_strength',"extensions"
    ],
    "style_transfer":[
        'sub_algorithm','base_model','prompt','negative_prompt',"scale_by","img2img_mode",
        'sampler_name','width','height','n_iter','batch_size','vae_model',
        'seed','steps','cfg_scale','callback_url','denoising_strength',"extensions"
    ],

}
def logging_params(job_id, job_params):
    callback_url = job_params.get('callback_url', "no_callback_url")
    backend_id = os.path.split(callback_url)[-1]
    return_msg = 'backend_id : {} job_id : {}, '.format(backend_id,job_id)
    return_msg += json.dumps(job_params)
    return return_msg






en_ch = {"text_2_img":"文生图","img_2_img":"图生图","clip_interrogator":"图片反推文字",
         "img2img":"图生图","sketch":"涂鸦", "inpaint":"局部重绘", "inpain_sketch":"涂鸦重绘",
         "upload_mask":"上传重绘模板", "style_transfer":"图片风格化"}
def logging_process_time(t_start,job_id,job_params):
    t_end = time.time()


    try:
        sub_algorithm = job_params.get("sub_algorithm",'nofound')
        img2img_mode = job_params.get("img2img_mode",'nofound')
        img2img_mode =en_ch[img2img_mode] if sub_algorithm in ['img_2_img'] else "无"

        sub_algorithm = en_ch.get(sub_algorithm, sub_algorithm)
        logger.info("任务处理信息：任务id:{},任务类型：{}，子任务：{},处理时间：{:.1f}s\n\n".format(job_id,sub_algorithm,img2img_mode,(t_end-t_start)))
    except Exception as E:
        logger.error(E)
        logger.error("无法打印信息")


def locate_exception():
    """
    locate error filename object line
    """
    exc_type, exc_obj, exc_tb = sys.exc_info()
    fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
    return "{},{},{}".format(exc_type, fname, exc_tb.tb_lineno)



def logger_error(E):
    exc_type, exc_obj, exc_tb = sys.exc_info()
    fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
    logger.error(locate_exception())
    logger.error("error {}".format(E))

