# -*-coding: utf-8 -*-
# @Time : 2024/11/2 11:25
# <AUTHOR> lishengjing
# @Email : <EMAIL>
# @File : media_type_detector.py
import filetype
def identify_file(filename):
    """
    判断文件类型，返回文件类型
    """
    kind = filetype.guess(filename)
    if kind is None:
        return "unknown"
    elif kind.mime.startswith('image/'):
        return "image"
    elif kind.mime.startswith('video/'):
        return "video"
    elif kind.mime.startswith('audio/'):
        return "audio"
    else:
        return "others"


def is_image(filename):
    """判断文件是否为图片"""
    import imghdr
    return imghdr.what(filename) is not None


def is_video(filename):

    """判断文件是否为视频"""
    from moviepy.editor import VideoFileClip
    import cv2
    try:
        # 使用moviepy尝试打开视频文件
        with VideoFileClip(filename) as video:
            return True
    except Exception:
        pass

    try:
        # 使用opencv尝试读取视频文件
        cap = cv2.VideoCapture(filename)
        if not cap.isOpened():
            return False
        cap.release()
        return True
    except Exception:
        return False

def file_type(filename):
    """
    有点慢，先不用
    """
    """判断文件类型"""
    if is_image(filename):
        return "图片"
    elif is_video(filename):
        return "视频"
    else:
        return "两者都不是"

if __name__ == '__main__':

    # 使用示例
    import time
    t1= time.time()
    for i in range(1000):
        filename = r"D:\gitlab\code\humanmatting\workspace\result.mov"  # 替换为你的文件名
        t = identify_file(filename)
    print(t)
    t2 = time.time()
    print(t2-t1)