# -*-coding: utf-8 -*-
# @Time : 2024/5/13 11:54
# <AUTHOR> lishengjing
# @Email : <EMAIL>
# @File : autolabel_unit.py
import os
import subprocess
import traceback
import stat
from tools.asr.config import asr_dict
from pathlib import Path
from .wo_common import *

def get_vocal(uvr5_params):
    """处理音频分离"""
    try:
        # 确保所有需要的目录都存在且可写
        required_dirs = [
            uvr5_params.save_root_vocal,
            uvr5_params.save_root_ins,
            uvr5_params.TEMP
        ]
        
        for directory in required_dirs:
            if not ensure_directory_writable(directory):
                return False, f"无法创建或写入目录: {directory}"
        
        model_names = uvr5_params.model_name
        model_names_numb = len(model_names)
        input_root =uvr5_params.input_root

        for model_idx,model_name in enumerate(model_names):
            save_idx_root = os.path.join(uvr5_params.TEMP,"{}_{}".format(model_name,model_idx))
            save_root_vocal = os.path.join(save_idx_root,"vocal")
            save_root_ins = os.path.join(save_idx_root,"instrument")
            paths = 'pass'
            if model_idx == 0: #第一个模型,使用初始输入路径
                input_root = uvr5_params.input_root
            paths = uvr5_params.paths
            # continue

            if model_idx == model_names_numb-1 :  # 最后一个模型
                save_root_vocal = uvr5_params.save_root_vocal
                save_root_ins = uvr5_params.save_root_ins
        
            # os.makedirs(save_root_vocal,exist_ok=True)

            logger.info('开始处理第{} / {}个模型:{}'.format(model_idx+1,model_names_numb,model_name))

            # 构建命令
            cmd = [
                uvr5_params.python_exec,
                'tools/cmd_uvr5.py',
                '--input_root', input_root,
                '--model_name', model_name,
                '--paths', paths,
                '--save_root_vocal', save_root_vocal,
                '--save_root_ins', save_root_ins,
                '--weight_uvr5_root', uvr5_params.weight_uvr5_root,
                '--TEMP', uvr5_params.TEMP
            ]

            half = True if model_name == "VR-DeEchoAggressive" else True
            if not half:
                cmd.append('--nohalf')
            print(cmd)
            # 使用列表形式运行命令，避免shell注入问题
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                universal_newlines=True
            )
            
            # 实时输出日志
            while True:
                output = process.stdout.readline()
                if output == '' and process.poll() is not None:
                    break
                if output:
                    logger.info(output.strip())

            # 等待进程完成
            return_code = process.wait()
            
            # 检查是否成功
            if return_code != 0:
                error_output = process.stderr.read()
                logger.error(f"处理失败: {error_output}")
                return False, f"模型 {model_name} 处理失败"

            try:
                kill_process(process.pid)
            except:
                pass

            input_root = save_root_vocal

        return True, 'success!'
    
    except Exception as e:
        logger.error(f"处理过程发生错误: {e}")
        return False, str(e)


def denoise_vocals(denoise_params):
    cmd = '{python_exec} tools/cmd-denoise.py -i {denoise_inp_dir} -o {denoise_opt_dir} -p {p} -m {m}'.format(
        python_exec=denoise_params.python_exec,
        denoise_inp_dir=denoise_params.denoise_inp_dir,
        denoise_opt_dir=denoise_params.denoise_opt_dir,
        p="float16" if denoise_params.is_half == True else "float32",
        m=denoise_params.denoise_model
    )
    print(cmd)
    p_denoise = subprocess.Popen(cmd, shell=True)
    p_denoise.wait()
    kill_process(p_denoise.pid)
    print("语音降噪任务完成, 查看终端进行下一步")

def split_vocals(split_params):
    inp = clean_path(split_params.inp)
    opt_root = clean_path(split_params.opt_root)
    if (os.path.exists(inp) == False):
        return False,"输入路径不存在"
    n_parts =split_params.n_parts
    if os.path.isfile(inp):
        n_parts = 1
    elif os.path.isdir(inp):
        pass
    else:
        return False, "输入路径存在但既不是文件也不是文件夹"
    ps_slice = []
    if (ps_slice == []):
        for i_part in range(n_parts):
            cmd = '{python_exec} tools/slice_audio.py {inp} {opt_root} {threshold} {min_length} {min_interval} {hop_size} {max_sil_kept} ' \
                  '{_max} {alpha} {i_part} {n_parts} ' .format(
            python_exec = split_params.python_exec,
            inp=inp,
            opt_root=opt_root,
            threshold=split_params.threshold,
            min_length=split_params.min_length,
            min_interval=split_params.min_interval,
            hop_size=split_params.hop_size,
            max_sil_kept=split_params.max_sil_kept,
            _max=split_params._max,
            alpha=split_params.alpha,
            i_part=i_part,
            n_parts=n_parts)
            print(cmd)
            p = subprocess.Popen(cmd, shell=True)
            ps_slice.append(p)
        for p in ps_slice:
            p.wait()

        # 杀死进程
        for p_slice in ps_slice:
            try:
                kill_process(p_slice.pid)
            except:
                traceback.print_exc()

    return True, "切分成功"


def asr_vocals(asr_params):
    cmd = '{python_exec} tools/asr/{asr_model} -i {asr_inp_dir}  -o {asr_opt_dir} -s {asr_model_size}  -l {asr_lang} -p {p} -m {m}'.format(
        python_exec=asr_params.python_exec,
        asr_inp_dir=asr_params.asr_inp_dir,
        asr_opt_dir=asr_params.asr_opt_dir,
        asr_model=asr_dict[asr_params.asr_model]["path"],
        asr_model_size=asr_params.asr_model_size,
        asr_lang=asr_params.asr_lang,
        m=asr_params.model_path,
        p="float16" if asr_params.is_half == True else "float32"
    )
    print(cmd)
    p_asr = subprocess.Popen(cmd, shell=True)
    p_asr.wait()

    kill_process(p_asr.pid)


def sdr_process(sdr_params):
    cmd = '{python_exec} tools/cmd_audio_separator.py -i {input_audio}  -o {save_root} -w {weight_separated_root} -m {model_filename}'.format(
    python_exec=sdr_params.python_exec,
    input_audio=sdr_params.input_audio,
    save_root=sdr_params.save_root,
    weight_separated_root=sdr_params.weight_separated_root,
    model_filename=sdr_params.model_filename
    )
    print(cmd)
    p_asr = subprocess.Popen(cmd, shell=True)
    p_asr.wait()

    kill_process(p_asr.pid)

    # 检查分离结果  返回名字是vocal_input_audio.wav和ins_input_audio.wav
    vocal_path = os.path.join(sdr_params.save_root,f"vocal_{os.path.splitext(os.path.basename(sdr_params.input_audio))[0]}.wav")
    ins_path = os.path.join(sdr_params.save_root,f"ins_{os.path.splitext(os.path.basename(sdr_params.input_audio))[0]}.wav")
    if os.path.isfile(vocal_path):
        return True,vocal_path,ins_path,"分离成功"
    else:
        return False,vocal_path,ins_path, "分离失败"


def ensure_directory_writable(directory):
    """确保目录可写"""
    try:
        # 创建目录（如果不存在）
        os.makedirs(directory, mode=0o777, exist_ok=True)
        
        # 获取目录的Path对象
        dir_path = Path(directory)
        
        if platform.system() == 'Windows':
            # Windows系统下的权限设置
            subprocess.run(['icacls', str(dir_path), '/grant', 'Everyone:(OI)(CI)F'], 
                         shell=True, 
                         check=True)
        else:
            # Linux/Mac系统下的权限设置
            os.chmod(directory, 
                    stat.S_IRWXU |  # 用户权限：读、写、执行
                    stat.S_IRWXG |  # 组权限：读、写、执行
                    stat.S_IRWXO)   # 其他用户权限：读、写、执行
            
        # 测试目录是否可写
        test_file = os.path.join(directory, '.write_test')
        try:
            with open(test_file, 'w') as f:
                f.write('test')
            os.remove(test_file)
            return True
        except Exception as e:
            logger.error(f"目录写入测试失败: {e}")
            return False
            
    except Exception as e:
        logger.error(f"设置目录权限失败: {e}")
        return False