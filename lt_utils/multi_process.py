
# -*-coding: utf-8 -*-
# @Time : 2024/4/26 10:32
# <AUTHOR> lishengjing
# @Email : <EMAIL>
# @File : brirmbg_detector.py


from skimage import io
import os,cv2
from PIL import Image
from loguru import logger
from multiprocessing import Process, Queue
import time
import numpy as np
from imwatermark.watermark import WatermarkEncoder, WatermarkDecoder
from lt_utils.log_msg import logger_error


class MultiProcess_for_func():
    def __init__(self,process_num=4):
        self.process_num = process_num
        self.is_init = False
        self.callback_msg = {'code':0, 'msg':'success!', 'algorithm': "watermark", 'job_id': 0,"result_path":None,"wm_text":None}  # callback format

    def _init(self):
        if self.is_init:
            return
 
        self.data_queue = Queue(maxsize=64)
        self.res_queue = Queue(maxsize=64)
        self.workers = [] 

        for idx in range(self.process_num):
            worker = Process(target=self.start_worker, args=(self.data_queue, self.res_queue))
            worker.start()
            self.workers.append(worker)
        self.is_init = True

    def reset(self):
        self.close()
        self._init()

    
    def close(self):
        for worker in self.workers:
            worker.join()
            worker.terminate()
        self.data_queue.close()
        self.res_queue.close()
        self.is_init = False


    def start_worker(self,data_queue,res_queue):
        # Init
        logger.info("初始化模型")
        # Start
        while True:
            try:
                # Load Image
                data = data_queue.get(block=True)

                task_type = data.get("task_type",'encode')
                image_path = data.get("image_path",None)
                save_path = data.get("save_path",None)
                method = data.get("method",'dwtDctSvd')
                wm_text = data.get("wm_text",None)
                job_id = data.get("job_id",None)
                embed_length = data.get("embed_length",64)

                callback_msg= self.callback_msg.copy()
                callback_msg['job_id'] = job_id
   
                if task_type == 'encode':
                    save_path,msg = encode_one_image(image_path,save_path,method,wm_text)
                    callback_msg['result_path'] = save_path
                    if save_path is None:  # 失败
                        callback_msg['code'] = -1
                        callback_msg['msg'] = msg
                else:
                    text,msg = decode_one_image(image_path, method, embed_length)
                    callback_msg['wm_text'] = text
                    if text is None:  # 失败
                        callback_msg['code'] = -1
                        callback_msg['msg'] = msg
    
                res_queue.put((callback_msg),block=True)
                        
            except Exception as e:
                _msg = str(e)
                logger_error(e)
    
                callback_msg= self.callback_msg.copy()
                callback_msg['job_id'] = job_id
                callback_msg['code'] = -1
                callback_msg['msg'] = _msg
                res_queue.put((callback_msg),block=True)


    def clear_queue(self):
        clear_number = 0
        while not self.data_queue.empty():
            try:
                self.data_queue.get()
                clear_number += 1
            except Exception as e:
                pass
            time.sleep(0.001)
        return clear_number


# 解密图片
@logger.catch()
def decode_one_image(image_path, method, embed):
    """
    image_path: 图片路径
    method: 解密方法 与加密方法一致
    embed: 水印长度 字节(一个字母=4字节)
    return: 水印文本
    """
    try:
        bgr = cv2.imread(image_path)
        decoder = WatermarkDecoder('bytes', length=embed)
        watermark = decoder.decode(bgr, method)
        return watermark,'success'
    except Exception as e:
        logger_error(e)
        return None,str(e)



# 加密图片
@logger.catch()
def encode_one_image(image_path, save_path, method, wm_text):
    """
    image_path: 图片路径
    save_path: 保存路径
    method: 加密方法
    wm_text: 水印文本
    return: 保存路径
    """
    try:
        bgr = cv2.imread(image_path)
        shape = bgr.shape
        encoder = WatermarkEncoder()
        encoder.set_watermark('bytes', wm_text.encode('utf-8'))
        bgr_encoded = encoder.encode(bgr, method)
        cv2.imwrite(save_path, bgr_encoded)
        return save_path,'success'
    except Exception as e:
        logger_error(e)
        return None,str(e)




if __name__ == '__main__':
    # 测试
    mp_process = MultiProcess_for_func(process_num=8)
    loop_num = 5000
    t1 = time.time()
    try:
        for i in range(loop_num):
            mp_process.add_task(test_func, (i,))
            # time.sleep(0.01)
        
        # 等待一段时间让任务执行完成
        # time.sleep(0.01)
    finally:
        mp_process.close()
    t2 = time.time()
    

    # 测试单进程
    t3 = time.time()
    for i in range(loop_num):
        test_func(i)
    t4 = time.time()
    print(f"MultiProcess Time taken: {t2 - t1} seconds")
    print(f"Single Process Time taken: {t4 - t3} seconds")

