# -*-coding: utf-8 -*-
# @Time : 2024/11/2 14:34
# <AUTHOR> lish<PERSON>jing
# @Email : <EMAIL>
# @File : process_download.py

import os, time, shutil, requests
from loguru import logger
from urllib.parse import urlparse
from lt_utils.log_msg import logger_error
from lt_utils.media_type_detector import  identify_file



# 下载音频和视频

def copy_local_file(source_image, workspace, dst_name):
    try:
        # 拷贝本地文件
        ext = os.path.splitext(source_image)[-1]
        if dst_name is None:
            dst_name = os.path.basename(source_image)
        source_path = os.path.join(workspace, dst_name + ext)
        shutil.copy(source_image, source_path)
        return True, source_path, "success download"
    except Exception as E:
        logger_error(E)
    return False, None, "Error: Copy local file failed!"


def download_file_from_url(url, work_space, new_filename=None, max_retries=3):
    """
    从远程 URL 下载文件到本地目录，并支持重命名文件。如果下载失败或文件大小不一致，最多尝试三次。

    参数:
    - url: 远程文件的 URL。
    - local_dir: 本地保存文件的目录，默认为当前目录。
    - new_filename: 下载后文件的新名称，如果不提供则使用远程文件的名称。
    - max_retries: 最大重试次数，默认为3次。

    返回:
    - 成功下载的文件路径，如果失败则返回 None。
    """
    for attempt in range(max_retries + 1):
        try:
            # 获取远程文件的名称
            parsed_url = urlparse(url)
            remote_filename = os.path.basename(parsed_url.path)

            # 如果提供了新文件名，则使用新文件名
            if new_filename:
                local_filename = new_filename + os.path.splitext(remote_filename)[-1]
            else:
                local_filename = remote_filename

            # 确保本地目录存在
            os.makedirs(work_space, exist_ok=True)

            # 本地文件的完整路径
            local_filepath = os.path.join(work_space, local_filename)

            # 发送 HTTP GET 请求
            start_time = time.time()
            with requests.get(url, stream=True) as response:
                response.raise_for_status()  # 检查请求是否成功

                # 获取远程文件的大小
                remote_file_size = int(response.headers.get('content-length', 0))

                # 写入文件
                with open(local_filepath, 'wb') as file:
                    for chunk in response.iter_content(chunk_size=8192):
                        file.write(chunk)

                # 获取本地文件的大小
                local_file_size = os.path.getsize(local_filepath)

                # 计算下载速度
                elapsed_time = time.time() - start_time
                speed = local_file_size / elapsed_time / (1024 * 1024)  # MB/s

                # 检查文件大小是否一致
                if local_file_size != remote_file_size:
                    os.remove(local_filepath)  # 删除不完整的文件
                    logger.error(
                        "Error: 下载的文件大小和线上的不一致. {} vs {}".format(remote_file_size, local_file_size))
                    raise ValueError("Downloaded file size does not match the remote file size.")

                logger.info(
                    " 下载成功：loc_file_size:{:.1f}m or {}B, speed:{:.3f} m/s".format(local_file_size / 1024 / 1024,
                                                                                      local_file_size, speed))
                return True, local_filepath, ""

        except (requests.exceptions.RequestException, ValueError) as e:
            logger_error(e)
            if attempt < max_retries:
                logger.error("下载失败：5秒后第 {}/{} 次重新下载...".format(attempt + 1, max_retries))
                time.sleep(5)
            else:
                logger.error("Max retries reached. Download failed.")
                return False, new_filename, "下载文件失败超过三次，检查链接是否正常"
        except Exception as e:
            logger_error(e)
            if attempt < max_retries:
                logger.error("下载失败：5秒后第 {}/{} 次重新下载...".format(attempt + 1, max_retries))
                time.sleep(5)
            else:
                logger.error("下载失败.")
                return False, new_filename, "下载文件失败超过三次，检查链接是否正常"


def auto_download_one_file(source_image, _workspace, dst_name='source'):
    """
       处理下载过程。

       该函数首先尝试创建用于存储的文件夹，然后检查source_image是否为本地文件。
       如果是本地文件，则直接拷贝到目标文件夹；如果不是，则调用download_file函数进行下载。

       参数:
       source_image: str - 图片的路径或URL。
       _workspace: str - 用于存储图片的工作目录路径。
       dst_name: str - 存储图片时使用的名称，默认为'source'。

       返回:
       成功时返回(True, 图片路径, "success download")，
       失败时返回download_file函数的返回值。
       """

    # 创建储存文件夹
    os.makedirs(_workspace, exist_ok=True)
    if os.path.isfile(source_image):
        # 拷贝本地图片
        return copy_local_file(source_image, _workspace, dst_name)
    else:
        # 下载图片
        return download_file_from_url(source_image, _workspace, dst_name)


def del_files(path):
    try:
        if os.path.isfile(path):
            os.remove(path)
        elif os.path.isdir(path):
            shutil.rmtree(path, ignore_errors=True)
    except:
        pass



def download_train_files(video_url,mask_url,audio_url,speaker_id,download_dir,start_cmd):
    media_file = {}
    is_success,media_video,msg = download_train_video(video_url,mask_url,speaker_id,download_dir)
    if not is_success:
        return False,media_file,msg
    media_file.update(media_video)
    is_success,media_audio,msg = download_train_audio(audio_url,media_video['video'],speaker_id,download_dir,start_cmd)
    if not is_success:
        return False,media_file,msg
    media_file.update(media_audio)
    return True,media_file,"success"

def download_train_video(video_url,mask_url,speaker_id,download_dir):
    media_file = {}
    mask_download_flag = mask_url is not None and len(mask_url) > 4 # 防止只是随便输入一个字节
    logger.info("开始下载视频:video : {}   || mask :{}".format(video_url,mask_url))
    for media_url,media_name in zip([video_url,mask_url],['video','mask']):
        t_d = time.time()
        if media_name== 'mask' and not mask_download_flag :
            logger.info("{} is {},不下载".format(media_name,media_url))
            continue
        video_ext = speaker_id +"_" +media_name  # workspace/speaker_id/speaker_id_video.mp4
        Isdownload, filename, _msg = auto_download_one_file(media_url,download_dir, video_ext)
        media_file[media_name] = filename
        if not Isdownload:
            e_msg = "训练视频下载失败 : {}".format(_msg)
            return False,media_file,e_msg
        logger.info("【{}】:下载视频成功，下载时间：{:.3f} s 路径为：{}".format(media_name,time.time()-t_d,filename))
    return True,media_file,"success"
            
def download_train_audio(audio_url,local_video,speaker_id,download_dir,start_cmd):
    # 下载驱动音频
    media_file = {}
    
    audio_dir = os.path.join(download_dir,'src_audio')
    audio_path = os.path.join(audio_dir, f"{speaker_id}_audio.wav")
    os.makedirs(audio_dir, exist_ok=True)

    if audio_url is not None:  #从网上下载或者本地拷贝
        Isdownload, audio_path, _msg = auto_download_one_file(audio_url,audio_dir,f"{speaker_id}_audio")
    else:
        # 当没有音频链接时，从视频中提取音频
        audio_path = os.path.join(audio_dir, f"{speaker_id}_audio.wav")
        # 使用ffmpeg从视频中提取音频
        cmd = f"ffmpeg -i {local_video} -q:a 0 -map a {audio_path} -y"
        is_success, _msg = start_cmd(cmd,timeout=600)
        if not is_success:
            Isdownload = False

            _msg = f"从视频中提取音频失败: {_msg}"
            logger.error(_msg)
        else:
            Isdownload = True
            _msg = "成功从视频中提取音频"

    media_file['audio'] = audio_path
    return Isdownload,media_file,_msg




def download_and_unpack_model_from_oss(train_dir,oss_server,oss_file_path):
    if not os.path.isdir(train_dir): # train/speaker_id：
        # 下载模型到上级目录
        t1 = time.time()
        logger.info('开始执行下载文件: {}\n'.format(oss_file_path))
        parent_dir = os.path.dirname(train_dir)
        os.makedirs(parent_dir,exist_ok=True)

        objs = list(oss_server.oss_bucket.objects.filter(Prefix=oss_file_path))
        if len(objs) == 0:
            logger.error(f"oss file not exist : {oss_file_path}")
            return False,train_dir,"oss file not exist"

        # 下载模型
        for obj in objs:
            oss_server.client.download_file(oss_server.BUCKET_NAME, obj.key, os.path.join(parent_dir, obj.key.split('/')[-1]))

        # 判断文件是否存在
        model_zip_path = os.path.join(parent_dir, os.path.basename(oss_file_path))
        if not os.path.isfile(model_zip_path):
            _msg = "download model error,can not find model zip file"
            logger.error(_msg)
            return False,train_dir,_msg
        
        # 打印下载速度
        log_info =True
        if log_info:
            fsize = os.path.getsize(model_zip_path) / 1024 / 1024
            str_fsize = "{:.2f}GB".format(fsize / 1024) if fsize > 1024 else "{:.1f}MB".format(fsize)
            cos_time = "{:.1f}s".format(time.time() - t1)
            speeds = "{:.1f}MB/s".format(fsize / (time.time() - t1))
            logger.info(
                '文件下载完成：<<文件名字：{local_file_path}，文件容量：{fsize},下载时间：{cos_time} 平均网速：{speeds} >>'.format(
                    local_file_path=model_zip_path, fsize=str_fsize, cos_time=cos_time, speeds=speeds))
            
        # 解压下载的zip文件
        try:
            shutil.unpack_archive(model_zip_path,train_dir)
            logger.info(f"解压模型文件成功 : {train_dir}")
        except:
            _msg = "unpack model zip file error"
            logger.error(_msg)
            return False,train_dir,_msg
        # 删除下载的zip文件
        try:
            os.remove(model_zip_path)
        except:
            pass
        return True, train_dir, "download and unpack model success"

    else:  # 模型文件已存在
        logger.info(f"model file is exist : {train_dir}")  
        return True, train_dir, "model file is exist"
    


@logger.catch()
def download_and_unpack_model(model_url,train_dir):
    try:

        if not os.path.isdir(train_dir): # train/speaker_id
            # 获取上一层目录
            parent_dir = os.path.dirname(train_dir)
            spk_id = os.path.basename(train_dir)
            os.makedirs(parent_dir,exist_ok=True)
            # 下载模型到上级目录

            IsSuccess, model_path, _msg = auto_download_one_file(model_url,parent_dir,spk_id)
            if not IsSuccess:
                logger.error(f"download model error : {model_url}")
                return False,train_dir,_msg
            #判断是zip还是MP4

            filetype = identify_file(model_path)
            if  filetype in ["video"]:
                os.makedirs(train_dir,exist_ok=True)
                try:

                    base_name = os.path.basename(model_path)
                    new_name = os.path.splitext(base_name)[0] + "_video" + os.path.splitext(base_name)[-1]   # spk_id.mp4 --> spk_id_video.mp4
                    os.renames(model_path,os.path.join(train_dir,new_name))
                except Exception as e:
                    logger_error(e)
                    return False,e,"rename model file error"
            elif model_path.endswith(".zip"):

                # 解压下载的zip文件
                shutil.unpack_archive(model_path,train_dir)
                # 删除下载的zip文件
                # try:
                #     os.remove(model_path)
                # except:
                    # pass
                return True, train_dir, "download and unpack model success"
        
        else:
            logger.info(f"model file is exist : {train_dir}")  
            return True, train_dir, "model file is exist"
        return True, train_dir, "download and unpack model success"
    except Exception as e:
        logger_error(e)
        return False,e,"download and unpack model error"
