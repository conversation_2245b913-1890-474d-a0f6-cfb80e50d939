# -*- coding: utf-8 -*-
# @Time : 2023/10/7 8:56 
# <AUTHOR>
# @Email : <EMAIL>
# @File : wo_oss.py

import sys
import boto3
import time
import os
from loguru import logger
from botocore.client import ClientError

"""
            "access_key": "FNANRNWVDYC1LY53SKG1",
            "secret_key": "PUjWkIA7V3pOzWG7smpl5ja1LKXr3dlCgTBooDl5",
            "endpoint": "http://172.16.17.245:8880",
            "bucket_name": "voicedenoise",
"""

PRO_AWS_SERVER_PUBLIC_KEY = "J70ORSJ6RP77IL8ICOFS"
PRO_AWS_SERVER_SECRET_KEY = "j2VzwTKl8NYWvo75U7OCybt55j2X7Li4CvbUTWa7"
PRO_ENDPOINT_URL = "http://172.16.17.245:8880"
PRO_REGION_NAME = "digitalhumanmodels" # useless
PRO_BUCKET_NAME = "digitalhumanmodels"

DEV_AWS_SERVER_PUBLIC_KEY = "U8ODJDR4L5JCYW5B1B1K"
DEV_AWS_SERVER_SECRET_KEY = "GFkxo1amwHiFDOIKUEE5j8BFAC9KXJNM8CbKnrvf"
DEV_ENDPOINT_URL = "http://172.16.104.110:5050"
DEV_REGION_NAME = "digitalhumanmodels" # useless
DEV_BUCKET_NAME = "digitalhumanmodels"


logger.info("请问你开了vpn了么？")


def check_env(WOM_AI_ENVIRONMENT=None):
    """
    判断当前是正式环境还是测试环境
    """
    is_check = False
    if WOM_AI_ENVIRONMENT is None:
        WOM_AI_ENVIRONMENT = os.getenv('WOM_AI_ENVIRONMENT', None)  # Production Development
    AWS_SERVER_PUBLIC_KEY = PRO_AWS_SERVER_PUBLIC_KEY
    AWS_SERVER_SECRET_KEY = PRO_AWS_SERVER_SECRET_KEY
    BUCKET_NAME = PRO_BUCKET_NAME
    REGION_NAME = PRO_REGION_NAME
    ENDPOINT_URL = PRO_ENDPOINT_URL

    if WOM_AI_ENVIRONMENT is None or WOM_AI_ENVIRONMENT == "Debug":
        logger.info("WOM_AI_ENVIRONMENT 配置为：{}，使用DEBUG模式，不进行连接oss".format(WOM_AI_ENVIRONMENT))
        AWS_SERVER_PUBLIC_KEY = "Debug"
        AWS_SERVER_SECRET_KEY = "Debug"
        BUCKET_NAME = "Debug"
        REGION_NAME = "Debug"
        ENDPOINT_URL = "Debug"
        BUCKET_RESULT = 'Debug'
        WOM_AI_ENVIRONMENT = "Debug"

    elif WOM_AI_ENVIRONMENT == "Production":
        logger.info("WOM_AI_ENVIRONMENT 配置为：{}，使用正式环境的对象存储".format(WOM_AI_ENVIRONMENT))
        is_check = True
    elif WOM_AI_ENVIRONMENT == "Development":
        logger.info("WOM_AI_ENVIRONMENT 配置为：{}，使用开发环境的对象存储".format(WOM_AI_ENVIRONMENT))
        AWS_SERVER_PUBLIC_KEY = DEV_AWS_SERVER_PUBLIC_KEY
        AWS_SERVER_SECRET_KEY = DEV_AWS_SERVER_SECRET_KEY
        BUCKET_NAME = DEV_BUCKET_NAME
        REGION_NAME = DEV_REGION_NAME
        ENDPOINT_URL = DEV_ENDPOINT_URL
        is_check = True
    else:
        logger.error("WOM_AI_ENVIRONMENT 配置错误！:{},使用正式环境的对象存储".format(WOM_AI_ENVIRONMENT))
    return is_check, WOM_AI_ENVIRONMENT,AWS_SERVER_PUBLIC_KEY,AWS_SERVER_SECRET_KEY,BUCKET_NAME,REGION_NAME,ENDPOINT_URL

env_is_check, WOM_AI_ENVIRONMENT, AWS_SERVER_PUBLIC_KEY, AWS_SERVER_SECRET_KEY, BUCKET_NAME, REGION_NAME, ENDPOINT_URL = check_env()


class LianTong_OSS():
    def __init__(self,AWS_SERVER_PUBLIC_KEY,AWS_SERVER_SECRET_KEY,BUCKET_NAME,REGION_NAME,ENDPOINT_URL,WOM_AI_ENVIRONMENT='Production'):
        self.AWS_SERVER_PUBLIC_KEY=AWS_SERVER_PUBLIC_KEY
        self.AWS_SERVER_SECRET_KEY=AWS_SERVER_SECRET_KEY
        self.BUCKET_NAME=BUCKET_NAME
        self.REGION_NAME=REGION_NAME
        self.ENDPOINT_URL = ENDPOINT_URL
        self.connect_seccuss=False
        self.WOM_AI_ENVIRONMENT=WOM_AI_ENVIRONMENT
        if WOM_AI_ENVIRONMENT != "Debug": #debug模式下不连接
            self.client, self.oss_bucket,self.connect_seccuss = self.init()  # 初始化，连接oss

    def init(self):
        i = 0
        connect_seccuss = False
        self.client, self.oss_bucket =None,None
        while i < 2 and not connect_seccuss:
            try:
                i += 1
                self.client = boto3.client('s3',
                                           aws_access_key_id=self.AWS_SERVER_PUBLIC_KEY,
                                           aws_secret_access_key=self.AWS_SERVER_SECRET_KEY,
                                           endpoint_url=self.ENDPOINT_URL,
                                           region_name=self.REGION_NAME)

                # 判断oss上是否已存在指定目录，不存在则创建
                try:
                    self.client.head_bucket(Bucket=self.BUCKET_NAME)
                except ClientError:
                    self.client.create_bucket(Bucket=self.BUCKET_NAME)

                self.s3 = boto3.resource('s3',
                                         aws_access_key_id=self.AWS_SERVER_PUBLIC_KEY,
                                         aws_secret_access_key=self.AWS_SERVER_SECRET_KEY,
                                         endpoint_url=self.ENDPOINT_URL,
                                         region_name=self.REGION_NAME)

                self.oss_bucket = self.s3.Bucket(self.BUCKET_NAME)
                connect_seccuss = True
                logger.info("oss 链接成功")
            except:
                logger.error("oss 链接失败，重新链接中")
        return self.client,self.oss_bucket,connect_seccuss

    def process_delete_oss_file(self,filename,log_info=False):
        try:
            if log_info:
                logger.info('开始执行删除文件: {}'.format(filename))
            self.client.delete_object(Bucket=self.BUCKET_NAME, Key=filename)
            if log_info:
                logger.info("模型删除成功：{}".format(filename))
        except Exception as e:
            logger.error('删除失败，重新初始化！')
            logger.error(e)
            try:
                self.client, self.oss_bucket = self.init()
                self.client.delete_object(Bucket=self.BUCKET_NAME, Key=filename)
            except Exception as e:
                logger.error('文件二次删除失败！')
                logger.error(e)

    @logger.catch()
    def process_upload_file_2_oss(self,local_file_path,oss_file_path,log_info=False):
        t1 = time.time()
        if log_info:
            logger.info('开始执行上传文件: {}\n'.format(local_file_path))
        filename = os.path.split(local_file_path)[-1]

        self.client.upload_file(local_file_path, self.BUCKET_NAME, oss_file_path)
        # 统计时间
        if log_info:
            fsize = os.path.getsize(local_file_path) / 1024 / 1024
            str_fsize = "{:.1f}GB".format(fsize / 1024) if fsize > 1024 else "{:.1f}MB".format(fsize)
            cos_time = "{:.1f}s".format(time.time() - t1)
            speeds = "{:.1f}MB/s".format(fsize / (time.time() - t1))
            logger.info(
                '文件上传完成：<<文件名字:{models_name}，文件容量：{fsize}, 上传时间：{cos_time} 平均网速：{speeds} >>'.format(
                    models_name=filename, fsize=str_fsize, cos_time=cos_time, speeds=speeds))

    def process_upload_dir_2_oss(self,local_dir,oss_dir,log_info=False):
        t1 = time.time()
        local_files = os.listdir(local_dir)
        total_size = 0
        for f in local_files:
            local_file_path = os.path.join(local_dir,f)
            fsize = os.path.getsize(local_file_path) / 1024 / 1024
            total_size +=fsize
            oss_file_path = oss_dir.rstrip("/").rstrip("\\") + '/'+ f
            self.process_upload_file_2_oss(local_file_path,oss_file_path)
        if log_info:
            str_fsize = "{:.1f}GB".format(total_size / 1024) if total_size > 1024 else "{:.1f}MB".format(total_size)
            cos_time = "{:.1f}s".format(time.time() - t1)
            speeds = "{:.1f}MB/s".format(total_size / (time.time() - t1))
            logger.info(
                '文件夹上传完成：<<文件夹名字{models_name}，文件数量：{file_num}，文件容量：{fsize}, 上传时间：{cos_time} 平均网速：{speeds} >>'.format(
                    models_name=local_dir,file_num=len(local_files), fsize=str_fsize, cos_time=cos_time, speeds=speeds))

    @logger.catch()
    def process_download_files_from_oss(self,save_file_path,oss_file_path,log_info=False):
        if log_info:
            logger.info('开始执行下载文件: {}\n'.format(oss_file_path))

        t1 = time.time()
        self.client.download_file(self.BUCKET_NAME, oss_file_path, save_file_path)

        # 统计时间
        if not os.path.exists(save_file_path):
            logger.info("{}文件下载失败！".format(save_file_path))
            return None
        if log_info:
            fsize = os.path.getsize(save_file_path) / 1024 / 1024
            str_fsize = "{:.2f}GB".format(fsize / 1024) if fsize > 1024 else "{:.1f}MB".format(fsize)
            cos_time = "{:.1f}s".format(time.time() - t1)
            speeds = "{:.1f}MB/s".format(fsize / (time.time() - t1))
            logger.info(
                '文件下载完成：<<文件名字：{local_file_path}，文件容量：{fsize},下载时间：{cos_time} 平均网速：{speeds} >>'.format(
                    local_file_path=save_file_path, fsize=str_fsize, cos_time=cos_time, speeds=speeds))
        return save_file_path

    def process_download_dir_from_oss(self,save_dir,oss_dir_path,log_info=False,remote_origin=True):
        t1= time.time()

        # oss_without_name_path = os.path.dirname(oss_dir_path)  # BUCKET_NAME/abc/123/aa.txt-->BUCKET_NAME/abc/123/
        # without_bucket_name_path = oss_dir_path.replace(BUCKET_NAME + "/", "")


        objs = list(self.oss_bucket.objects.filter(Prefix=oss_dir_path))
        all_download_file_number = len(objs)
        logger.info('需要下载 {}个模型'.format(all_download_file_number))

        os.makedirs(save_dir, exist_ok=True)


        # 下载文件
        has_download = 0
        total_size = 0
        for obj in objs:
            filename = obj.key.split('/')[-1]
            if filename is None or filename=="":
                continue

            local_file_path = os.path.join(save_dir,filename)
            download_flag = True
            if os.path.isfile(local_file_path):  # 模型已存在,删除重新下载
                if remote_origin: # 删除原有的，重新下载
                    if log_info:
                        logger.info('本地文件已存在，删除本地文件重新从oss上下载')
                    os.remove(local_file_path)
                else: # 不删除原来有的，跳过
                    download_flag =False

            if download_flag:
                self.client.download_file(self.BUCKET_NAME, obj.key,local_file_path )
            has_download +=1
            # 统计时间
            if not os.path.exists(local_file_path):
                logger.info("{}文件下载失败！".format(local_file_path))
            else:
                fsize = os.path.getsize(local_file_path) / 1024 / 1024
                total_size += fsize

        if log_info:
            str_fsize = "{:.2f}GB".format(total_size / 1024) if total_size > 1024 else "{:.1f}MB".format(total_size)
            cos_time = "{:.1f}s".format(time.time() - t1)
            speeds = "{:.1f}MB/s".format(total_size / (time.time() - t1))
            logger.info(
                '【{has_download}/{all_download_file_number}】 文件下载完成：<<文件目录：{local_file_path}，文件容量：{fsize},下载时间：{cos_time} 平均网速：{speeds} >>'.format(
                    has_download =has_download,all_download_file_number=all_download_file_number,local_file_path=save_dir, fsize=str_fsize, cos_time=cos_time, speeds=speeds))
        return save_dir

    def reconnect_oss(self, access_key, secret_key, bucket_name, endpoint,WOM_AI_ENVIRONMENT):
        if access_key is None or secret_key is None or bucket_name is None or endpoint is None:  # 使用默认对象存储
            return
        if access_key != self.AWS_SERVER_PUBLIC_KEY or secret_key != self.AWS_SERVER_SECRET_KEY or bucket_name != self.BUCKET_NAME or endpoint != self.ENDPOINT_URL:
            self.AWS_SERVER_PUBLIC_KEY = access_key
            self.AWS_SERVER_SECRET_KEY = secret_key
            self.BUCKET_NAME = bucket_name
            self.ENDPOINT_URL = endpoint
            self.WOM_AI_ENVIRONMENT = WOM_AI_ENVIRONMENT
            self.client, self.oss_bucket, self.connect_seccuss = self.init()  # 初始化，连接oss

    def presigned(self, oss_file_path, bucket_name=None, expiresin=1):
        """
        生成一个预签名的URL，用于共享对象。

        预签名的URL允许在限定时间内，即使对象所在的存储桶是私有的，也可以通过该URL直接访问对象。
        这个方法主要用于临时共享对象，避免长期暴露对象的安全风险。

        参数:
        - oss_file_path (str): 待分享对象在存储桶中的路径，包括对象的名称。
        - bucket_name (str, 可选): 待分享对象所在的存储桶名称。如果未提供，则使用实例初始化时设置的默认存储桶名称。
        - expiresin (int, 可选): 预签名URL的有效期，以天为单位。默认为1天。

        返回:
        - presigned_url (str): 生成的预签名URL，用于临时访问指定的对象。
        """

        # 确定使用哪个存储桶名称
        Bucket = bucket_name if bucket_name is not None else self.BUCKET_NAME

        # 生成预签名URL
        presigned_url = self.client.generate_presigned_url(
            ClientMethod='get_object',
            Params={
                'Bucket': Bucket,  # 待分享对象所在存储桶
                'Key': oss_file_path  # 待分享对象
            },
            ExpiresIn=(3600 * 24 * expiresin)  # 1 day
        )

        # 返回生成的预签名URL
        return presigned_url

    def presigned_public_read_url(self, oss_file_path, expire_time=3600):
        """
        生成允许公开读取的预签名URL
        
        参数:
            oss_file_path: OSS文件路径
            expire_time: URL有效期，单位秒，默认3600秒（1小时）
            
        返回:
            预签名URL字符串
        """
        try:
            # 生成预签名URL，使用传入的有效期
            url = self.bucket.sign_url('GET', oss_file_path, expire_time)
            return url
        except Exception as e:
            # 处理异常情况
            logger.error(f"生成预签名URL失败: {str(e)}")
            raise

def  auto_switch_oss(WOM_AI_ENVIRONMENT,oss_server):
    env_is_check, _WOM_AI_ENVIRONMENT, AWS_SERVER_PUBLIC_KEY, AWS_SERVER_SECRET_KEY, BUCKET_NAME, REGION_NAME, ENDPOINT_URL = check_env()
    if WOM_AI_ENVIRONMENT != _WOM_AI_ENVIRONMENT:
        oss_server.reconnect_oss(AWS_SERVER_PUBLIC_KEY,AWS_SERVER_SECRET_KEY,BUCKET_NAME,ENDPOINT_URL,_WOM_AI_ENVIRONMENT)

    return oss_server

# oss_server = LianTong_OSS(AWS_SERVER_PUBLIC_KEY,AWS_SERVER_SECRET_KEY,BUCKET_NAME,REGION_NAME,ENDPOINT_URL)
def handle_oss_server(WOM_AI_ENVIRONMENT=None):
    env_is_check, WOM_AI_ENVIRONMENT, AWS_SERVER_PUBLIC_KEY, AWS_SERVER_SECRET_KEY, BUCKET_NAME, REGION_NAME, ENDPOINT_URL = check_env(WOM_AI_ENVIRONMENT)
    logger.info("使用{}环境!准备连接oss，打开vpn了么？".format(WOM_AI_ENVIRONMENT))
    oss_server = LianTong_OSS(AWS_SERVER_PUBLIC_KEY,AWS_SERVER_SECRET_KEY,BUCKET_NAME,REGION_NAME,ENDPOINT_URL,WOM_AI_ENVIRONMENT)
    return oss_server

oss_server =handle_oss_server()
# model_oss_server = handle_oss_server() #保存模型到oss的oss服务器

# if __name__ == '__main__':
    # models_path ="/data02/ljy/stable-diffusion-webui/models/Stable-diffusion" # 上传测试模型存放路径
    # models_path ="D:\project\sd-webui-aki\sd-webui-aki-v4.5\models\Lora" # 上传测试模型存放路径
    # model_filename = 'C4D_midjourney_v1.0.safetensors' # 模型名字
    # # model_filename = 'realistic_Ultra_realistic_beautiful_v1.safetensors' # 模型名字
    # bucket_name =OSS_LORA15 # oss 保存目录
    # # bucket_name =oss_sd15 # oss 保存目录
    # model_save_path =r"D:\Kugou"  # 下载测试保存目录
    # model_save_path =r"./kugou"  # 下载测试保存目录
    #
    # # 上传测试
    # upload_model_2_oss(os.path.join(model_save_path,model_filename),os.path.join(bucket_name,model_filename))
    #
    # # # # 下载测试
    # bucket_path = bucket_name+"/"+model_filename #oss模型文件位置
    # download_model_from_oss(model_save_path, bucket_path)
    # #
    # # # 删除测试
    # delete_oss_models(bucket_name,model_filename)
    # 批量下载测试
    # model_save_path = '/data02/lsj/sd_models/models/VAE'
    # oss_save_path = BUCKET_NAME + "/" + 'VAE'
    # oss_server.process_download_dir_from_oss(model_save_path,oss_save_path,log_info=True)
    # oss_server.process_download_files_from_oss(model_save_path,oss_save_path,log_info=True)

    #
    #
    # models_path = "/data02/ljy/stable-diffusion-webui/models/Lora"  # 上传测试模型存放路径
    # bucket_name = "lora15"  # oss 保存目录
    # file_list = os.listdir(models_path)
    # for f in file_list:
    #     if f.endswith(".safetensors") or f.endswith(".ckpt"):
    #         upload_model_2_oss(os.path.join(models_path, f), bucket_name)
    #'aigo-wodraw-models\\lora15\\C4D_midjourney_v1.0.safetensors'
    #'D:\\project\\sd-webui-aki\\sd-webui-aki-v4.5\\models\\Lora'
