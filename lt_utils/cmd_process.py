import os
from loguru import logger
import subprocess
import os
from loguru import logger
import subprocess
from subprocess import TimeoutExpired
import shlex

def start_cmd(command, timeout=600):
    """
    执行命令并设置超时机制，实时输出日志
    参数：
        command: 要执行的命令列表
        timeout: 超时时间（秒）
    """
    try:
        process = subprocess.Popen(
            command,
            stdout=subprocess.PIPE,
            stderr=subprocess.STDOUT,  # 合并错误输出到标准输出
            text=True,
            bufsize=1,  # 行缓冲
            universal_newlines=True
        )

        # 实时读取输出
        while True:
            output = process.stdout.readline()
            if output == '' and process.poll() is not None:
                break
            if output:
                logger.info(output.strip())

        # 等待进程完成或超时
        try:
            return_code = process.wait(timeout=timeout)
        except TimeoutExpired:
            process.kill()
            logger.error(f"Command timed out after {timeout} seconds")
            return False, f"Command timed out after {timeout} seconds"

        if return_code == 0:
            return True, ""
        else:
            error_output = process.stderr.read() if process.stderr else ""
            logger.error(f"Process failed: {error_output}")
            return False, error_output

    except Exception as e:
        logger.error(f"Error: {str(e)}")
        return False, str(e)

# def start_cmd(command, timeout=600):
#     """
#     执行命令并设置超时机制。

#     参数：
#         command: 要执行的命令字符串
#         timeout: 超时时间（秒），默认30秒

#     返回：
#         (bool, str): 是否成功执行，错误消息或空字符串
#     """
#     try:
#         # 使用 subprocess.run 执行命令
#         result = subprocess.run(
#             command,
#             shell=True,  # 使用 shell 执行命令
#             stdout=subprocess.PIPE,
#             stderr=subprocess.PIPE,
#             text=True,  # 返回字符串而非字节
#             timeout=timeout  # 设置超时
#         )

#         # 记录标准输出和标准错误
#         if result.stdout:
#             logger.info(result.stdout.strip())
#         if result.stderr:
#             if result.stderr.find("UserWarning") < 0:
#                 logger.error(result.stderr.strip())

#         # 检查返回码
#         if result.returncode == 0:
#             return True, ""  # 成功执行
#         else:
#             return False, result.stderr  # 返回错误信息

#     except subprocess.TimeoutExpired:
#         # 超时处理
#         logger.error(f"Command timed out after {timeout} seconds")
#         return False, f"Command timed out after {timeout} seconds"
#     except Exception as e:
#         # 捕获其他异常
#         logger.error(f"Error: {e}")
#         return False, f"Error: {e}"


# # 示例用法
# command = "ffmpeg -i _src_path -q:a 0 -map a audio_path"
# is_success, msg = start_cmd(command, timeout=30)

# if is_success:
#     print("命令执行成功")
# else:
#     print(f"命令执行失败: {msg}")