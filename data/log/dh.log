[2025-07-09 16:04:31,279] [server.py[line:707]] [INFO] [server listening on 127.0.0.1:8765]
[2025-07-09 16:04:31,280] [run_local.py[line:248]] [INFO] [��start_websocket_server��WebSocket���������� ws://127.0.0.1:8765 ����]
[2025-07-09 16:04:31,280] [run_local.py[line:249]] [INFO] [��start_websocket_server���ȴ�������... (���300����û�����ӽ��Զ��ر�)]
[2025-07-09 16:04:37,873] [server.py[line:642]] [INFO] [connection open]
[2025-07-09 16:04:37,874] [run_local.py[line:241]] [INFO] [��start_websocket_server�����յ���һ���ͻ������ӣ����������������С�]
[2025-07-09 16:04:37,874] [run_local.py[line:166]] [INFO] [��websocket_handler��WebSocket client ��ʼ��������: v3720p]
[2025-07-09 16:04:37,874] [run_local.py[line:178]] [INFO] [��websocket_handler���Ѿ�������ʽ����: v3720p]
[2025-07-09 16:04:37,876] [run_local.py[line:193]] [INFO] [��websocket_handler����ʽ���� fa3ba59424e711f091782228ff08ca3c ��ʼ��ʽ����~ ���~~.]
[2025-07-09 16:04:37,876] [run_local.py[line:257]] [INFO] [��start_websocket_server�����������ڽ����������С�]
[2025-07-09 16:04:57,031] [ice.py[line:1114]] [INFO] [Connection(0) Could not bind to 169.254.123.208 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-09 16:04:57,031] [ice.py[line:1114]] [INFO] [Connection(0) Could not bind to 169.254.26.119 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-09 16:05:02,047] [web_log.py[line:211]] [INFO] [127.0.0.1 [09/Jul/2025:16:04:57 +0800] "POST /offer HTTP/1.1" 200 3687 "-" "Python/3.10 aiohttp/3.9.5"]
[2025-07-09 16:05:02,048] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.96', 53397) -> ('2.2.2.96', 53386)) State.FROZEN -> State.WAITING]
[2025-07-09 16:05:02,048] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.19.96.1', 53398) -> ('2.2.2.96', 53386)) State.FROZEN -> State.WAITING]
[2025-07-09 16:05:02,048] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 53399) -> ('2.2.2.96', 53386)) State.FROZEN -> State.WAITING]
[2025-07-09 16:05:02,048] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 53400) -> ('2.2.2.96', 53386)) State.FROZEN -> State.WAITING]
[2025-07-09 16:05:02,048] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.239.42', 53401) -> ('2.2.2.96', 53386)) State.FROZEN -> State.WAITING]
[2025-07-09 16:05:02,049] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.17.240.1', 53402) -> ('2.2.2.96', 53386)) State.FROZEN -> State.WAITING]
[2025-07-09 16:05:02,049] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.96', 53397) -> ('172.19.96.1', 53388)) State.FROZEN -> State.WAITING]
[2025-07-09 16:05:02,050] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.96', 53397) -> ('2.2.2.96', 53386)) State.WAITING -> State.IN_PROGRESS]
[2025-07-09 16:05:02,051] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.96', 53397) -> ('2.2.2.96', 53386)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-09 16:05:02,052] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.96', 53397) -> ('10.118.7.130', 53390)) State.FROZEN -> State.WAITING]
[2025-07-09 16:05:02,052] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.96', 53397) -> ('169.254.43.7', 53392)) State.FROZEN -> State.WAITING]
[2025-07-09 16:05:02,052] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.96', 53397) -> ('10.118.239.42', 53394)) State.FROZEN -> State.WAITING]
[2025-07-09 16:05:02,052] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.96', 53397) -> ('172.17.240.1', 53396)) State.FROZEN -> State.WAITING]
[2025-07-09 16:05:02,052] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.96', 53397) -> ('58.248.180.197', 36219)) State.FROZEN -> State.WAITING]
[2025-07-09 16:05:02,076] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.19.96.1', 53398) -> ('2.2.2.96', 53386)) State.WAITING -> State.IN_PROGRESS]
[2025-07-09 16:05:02,101] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 53399) -> ('2.2.2.96', 53386)) State.WAITING -> State.IN_PROGRESS]
[2025-07-09 16:05:02,108] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 53400) -> ('2.2.2.96', 53386)) State.WAITING -> State.IN_PROGRESS]
[2025-07-09 16:05:02,127] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.239.42', 53401) -> ('2.2.2.96', 53386)) State.WAITING -> State.IN_PROGRESS]
[2025-07-09 16:05:02,154] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.17.240.1', 53402) -> ('2.2.2.96', 53386)) State.WAITING -> State.IN_PROGRESS]
[2025-07-09 16:05:02,185] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.96', 53397) -> ('172.19.96.1', 53388)) State.WAITING -> State.IN_PROGRESS]
[2025-07-09 16:05:02,215] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.96', 53397) -> ('10.118.7.130', 53390)) State.WAITING -> State.IN_PROGRESS]
[2025-07-09 16:05:02,216] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.96', 53397) -> ('169.254.43.7', 53392)) State.WAITING -> State.IN_PROGRESS]
[2025-07-09 16:05:02,246] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.96', 53397) -> ('10.118.239.42', 53394)) State.WAITING -> State.IN_PROGRESS]
[2025-07-09 16:05:02,273] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.96', 53397) -> ('172.17.240.1', 53396)) State.WAITING -> State.IN_PROGRESS]
[2025-07-09 16:05:02,280] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.96', 53397) -> ('58.248.180.197', 36219)) State.WAITING -> State.IN_PROGRESS]
[2025-07-09 16:05:02,294] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.19.96.1', 53398) -> ('172.19.96.1', 53388)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-09 16:05:02,323] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 53399) -> ('172.19.96.1', 53388)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-09 16:05:02,325] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 53400) -> ('172.19.96.1', 53388)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-09 16:05:02,354] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.239.42', 53401) -> ('172.19.96.1', 53388)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-09 16:05:02,385] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.17.240.1', 53402) -> ('172.19.96.1', 53388)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-09 16:05:02,416] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.19.96.1', 53398) -> ('10.118.7.130', 53390)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-09 16:05:02,439] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 53399) -> ('10.118.7.130', 53390)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-09 16:05:02,453] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 53400) -> ('10.118.7.130', 53390)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-09 16:05:02,461] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.239.42', 53401) -> ('10.118.7.130', 53390)) State.FROZEN -> State.FAILED]
[2025-07-09 16:05:02,461] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.17.240.1', 53402) -> ('10.118.7.130', 53390)) State.FROZEN -> State.FAILED]
[2025-07-09 16:05:02,462] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.19.96.1', 53398) -> ('169.254.43.7', 53392)) State.FROZEN -> State.FAILED]
[2025-07-09 16:05:02,462] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 53399) -> ('169.254.43.7', 53392)) State.FROZEN -> State.FAILED]
[2025-07-09 16:05:02,462] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 53400) -> ('169.254.43.7', 53392)) State.FROZEN -> State.FAILED]
[2025-07-09 16:05:02,462] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.239.42', 53401) -> ('169.254.43.7', 53392)) State.FROZEN -> State.FAILED]
[2025-07-09 16:05:02,462] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.17.240.1', 53402) -> ('169.254.43.7', 53392)) State.FROZEN -> State.FAILED]
[2025-07-09 16:05:02,462] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.19.96.1', 53398) -> ('10.118.239.42', 53394)) State.FROZEN -> State.FAILED]
[2025-07-09 16:05:02,463] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 53399) -> ('10.118.239.42', 53394)) State.FROZEN -> State.FAILED]
[2025-07-09 16:05:02,463] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 53400) -> ('10.118.239.42', 53394)) State.FROZEN -> State.FAILED]
[2025-07-09 16:05:02,463] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.239.42', 53401) -> ('10.118.239.42', 53394)) State.FROZEN -> State.FAILED]
[2025-07-09 16:05:02,463] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.17.240.1', 53402) -> ('10.118.239.42', 53394)) State.FROZEN -> State.FAILED]
[2025-07-09 16:05:02,463] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.19.96.1', 53398) -> ('172.17.240.1', 53396)) State.FROZEN -> State.FAILED]
[2025-07-09 16:05:02,463] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 53399) -> ('172.17.240.1', 53396)) State.FROZEN -> State.FAILED]
[2025-07-09 16:05:02,463] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 53400) -> ('172.17.240.1', 53396)) State.FROZEN -> State.FAILED]
[2025-07-09 16:05:02,463] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.239.42', 53401) -> ('172.17.240.1', 53396)) State.FROZEN -> State.FAILED]
[2025-07-09 16:05:02,464] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.17.240.1', 53402) -> ('172.17.240.1', 53396)) State.FROZEN -> State.FAILED]
[2025-07-09 16:05:02,464] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.19.96.1', 53398) -> ('58.248.180.197', 36219)) State.FROZEN -> State.FAILED]
[2025-07-09 16:05:02,464] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 53399) -> ('58.248.180.197', 36219)) State.FROZEN -> State.FAILED]
[2025-07-09 16:05:02,464] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 53400) -> ('58.248.180.197', 36219)) State.FROZEN -> State.FAILED]
[2025-07-09 16:05:02,465] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.239.42', 53401) -> ('58.248.180.197', 36219)) State.FROZEN -> State.FAILED]
[2025-07-09 16:05:02,465] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.17.240.1', 53402) -> ('58.248.180.197', 36219)) State.FROZEN -> State.FAILED]
[2025-07-09 16:05:02,465] [ice.py[line:1114]] [INFO] [Connection(0) ICE completed]
[2025-07-09 16:05:02,466] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.19.96.1', 53398) -> ('172.19.96.1', 53388)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-09 16:05:02,467] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 53399) -> ('10.118.7.130', 53390)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-09 16:05:02,492] [rtcrtpsender.py[line:463]] [WARNING] [RTCRtpsender(audio) Traceback (most recent call last):
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\rtcrtpsender.py", line 350, in _run_rtp
    timestamp = uint32_add(timestamp_origin, enc_frame.timestamp)
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\utils.py", line 39, in uint32_add
    return (a + b) & 0xFFFFFFFF
TypeError: unsupported operand type(s) for +: 'int' and 'NoneType'
]
[2025-07-09 16:05:02,500] [rtcrtpsender.py[line:463]] [WARNING] [RTCRtpsender(video) Traceback (most recent call last):
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\rtcrtpsender.py", line 346, in _run_rtp
    enc_frame = await self._next_encoded_frame(codec)
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\rtcrtpsender.py", line 293, in _next_encoded_frame
    payloads, timestamp = await self.__loop.run_in_executor(
  File "D:\gitlab\code\*********************\py38\lib\concurrent\futures\thread.py", line 57, in run
    result = self.fn(*self.args, **self.kwargs)
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\codecs\vpx.py", line 322, in encode
    lib.vpx_codec_encode(
TypeError: an integer is required
]
[2025-07-09 16:06:02,833] [run_local.py[line:223]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������.]
[2025-07-09 16:06:02,833] [run_local.py[line:225]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������.]
[2025-07-09 16:06:02,834] [server.py[line:264]] [INFO] [connection closed]
[2025-07-09 16:06:02,834] [run_local.py[line:265]] [INFO] [��start_websocket_server�����������б�ȡ����]
[2025-07-09 16:06:02,834] [run_local.py[line:268]] [INFO] [��start_websocket_server�����ڹرշ�����...]
[2025-07-09 16:06:02,834] [server.py[line:759]] [INFO] [server closing]
[2025-07-09 16:06:02,835] [server.py[line:793]] [INFO] [server closed]
[2025-07-09 16:06:02,835] [run_local.py[line:271]] [INFO] [��start_websocket_server���������ѳɹ��رա�]
[2025-07-09 16:06:02,836] [run_local.py[line:279]] [INFO] [
��⵽�û��жϣ����������ڹرա�]
[2025-07-09 16:23:28,138] [server.py[line:707]] [INFO] [server listening on 127.0.0.1:8765]
[2025-07-09 16:23:28,138] [run_local.py[line:248]] [INFO] [��start_websocket_server��WebSocket���������� ws://127.0.0.1:8765 ����]
[2025-07-09 16:23:28,139] [run_local.py[line:249]] [INFO] [��start_websocket_server���ȴ�������... (���300����û�����ӽ��Զ��ر�)]
[2025-07-09 16:23:32,322] [server.py[line:642]] [INFO] [connection open]
[2025-07-09 16:23:32,322] [run_local.py[line:241]] [INFO] [��start_websocket_server�����յ���һ���ͻ������ӣ����������������С�]
[2025-07-09 16:23:32,323] [run_local.py[line:166]] [INFO] [��websocket_handler��WebSocket client ��ʼ��������: v3720p]
[2025-07-09 16:23:32,323] [run_local.py[line:178]] [INFO] [��websocket_handler���Ѿ�������ʽ����: v3720p]
[2025-07-09 16:23:32,324] [run_local.py[line:193]] [INFO] [��websocket_handler����ʽ���� fa3ba59424e711f091782228ff08ca3c ��ʼ��ʽ����~ ���~~.]
[2025-07-09 16:23:32,325] [run_local.py[line:257]] [INFO] [��start_websocket_server�����������ڽ����������С�]
[2025-07-09 16:23:42,240] [ice.py[line:1114]] [INFO] [Connection(0) Could not bind to 169.254.123.208 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-09 16:23:42,240] [ice.py[line:1114]] [INFO] [Connection(0) Could not bind to 169.254.26.119 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-09 16:23:47,245] [web_log.py[line:211]] [INFO] [127.0.0.1 [09/Jul/2025:16:23:42 +0800] "POST /offer HTTP/1.1" 200 2270 "-" "Python/3.10 aiohttp/3.9.5"]
[2025-07-09 16:23:47,246] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.96', 65007) -> ('2.2.2.96', 55446)) State.FROZEN -> State.WAITING]
[2025-07-09 16:23:47,246] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.19.96.1', 65008) -> ('2.2.2.96', 55446)) State.FROZEN -> State.WAITING]
[2025-07-09 16:23:47,246] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 65009) -> ('2.2.2.96', 55446)) State.FROZEN -> State.WAITING]
[2025-07-09 16:23:47,247] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 65010) -> ('2.2.2.96', 55446)) State.FROZEN -> State.WAITING]
[2025-07-09 16:23:47,247] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.239.42', 65011) -> ('2.2.2.96', 55446)) State.FROZEN -> State.WAITING]
[2025-07-09 16:23:47,247] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.17.240.1', 65012) -> ('2.2.2.96', 55446)) State.FROZEN -> State.WAITING]
[2025-07-09 16:23:47,247] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.96', 65007) -> ('172.19.96.1', 55448)) State.FROZEN -> State.WAITING]
[2025-07-09 16:23:47,248] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.96', 65007) -> ('2.2.2.96', 55446)) State.WAITING -> State.IN_PROGRESS]
[2025-07-09 16:23:47,268] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.19.96.1', 65008) -> ('2.2.2.96', 55446)) State.WAITING -> State.IN_PROGRESS]
[2025-07-09 16:23:47,284] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 65009) -> ('2.2.2.96', 55446)) State.WAITING -> State.IN_PROGRESS]
[2025-07-09 16:23:47,319] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 65010) -> ('2.2.2.96', 55446)) State.WAITING -> State.IN_PROGRESS]
[2025-07-09 16:23:47,350] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.239.42', 65011) -> ('2.2.2.96', 55446)) State.WAITING -> State.IN_PROGRESS]
[2025-07-09 16:23:47,381] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.17.240.1', 65012) -> ('2.2.2.96', 55446)) State.WAITING -> State.IN_PROGRESS]
[2025-07-09 16:23:47,413] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.96', 65007) -> ('172.19.96.1', 55448)) State.WAITING -> State.IN_PROGRESS]
[2025-07-09 16:23:47,425] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.19.96.1', 65008) -> ('172.19.96.1', 55448)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-09 16:23:47,438] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 65009) -> ('172.19.96.1', 55448)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-09 16:23:47,476] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 65010) -> ('172.19.96.1', 55448)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-09 16:23:47,507] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.239.42', 65011) -> ('172.19.96.1', 55448)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-09 16:23:47,537] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.17.240.1', 65012) -> ('172.19.96.1', 55448)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-09 16:23:47,567] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.96', 65007) -> ('10.118.7.130', 55450)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-09 16:23:47,587] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.19.96.1', 65008) -> ('10.118.7.130', 55450)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-09 16:23:47,596] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 65009) -> ('10.118.7.130', 55450)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-09 16:23:47,610] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 65010) -> ('10.118.7.130', 55450)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-09 16:23:47,641] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.239.42', 65011) -> ('10.118.7.130', 55450)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-09 16:23:47,643] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.96', 65007) -> ('2.2.2.96', 55446)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-09 16:23:47,643] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.96', 65007) -> ('169.254.43.7', 55452)) State.FROZEN -> State.WAITING]
[2025-07-09 16:23:47,643] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.96', 65007) -> ('10.118.239.42', 55454)) State.FROZEN -> State.WAITING]
[2025-07-09 16:23:47,643] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.96', 65007) -> ('172.17.240.1', 55456)) State.FROZEN -> State.WAITING]
[2025-07-09 16:23:47,643] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.96', 65007) -> ('58.248.180.197', 23485)) State.FROZEN -> State.WAITING]
[2025-07-09 16:23:47,645] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.17.240.1', 65012) -> ('10.118.7.130', 55450)) State.FROZEN -> State.FAILED]
[2025-07-09 16:23:47,645] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.96', 65007) -> ('169.254.43.7', 55452)) State.WAITING -> State.FAILED]
[2025-07-09 16:23:47,646] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.19.96.1', 65008) -> ('169.254.43.7', 55452)) State.FROZEN -> State.FAILED]
[2025-07-09 16:23:47,646] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 65009) -> ('169.254.43.7', 55452)) State.FROZEN -> State.FAILED]
[2025-07-09 16:23:47,646] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 65010) -> ('169.254.43.7', 55452)) State.FROZEN -> State.FAILED]
[2025-07-09 16:23:47,647] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.239.42', 65011) -> ('169.254.43.7', 55452)) State.FROZEN -> State.FAILED]
[2025-07-09 16:23:47,647] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.17.240.1', 65012) -> ('169.254.43.7', 55452)) State.FROZEN -> State.FAILED]
[2025-07-09 16:23:47,647] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.96', 65007) -> ('10.118.239.42', 55454)) State.WAITING -> State.FAILED]
[2025-07-09 16:23:47,647] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.19.96.1', 65008) -> ('10.118.239.42', 55454)) State.FROZEN -> State.FAILED]
[2025-07-09 16:23:47,647] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 65009) -> ('10.118.239.42', 55454)) State.FROZEN -> State.FAILED]
[2025-07-09 16:23:47,648] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 65010) -> ('10.118.239.42', 55454)) State.FROZEN -> State.FAILED]
[2025-07-09 16:23:47,648] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.239.42', 65011) -> ('10.118.239.42', 55454)) State.FROZEN -> State.FAILED]
[2025-07-09 16:23:47,648] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.17.240.1', 65012) -> ('10.118.239.42', 55454)) State.FROZEN -> State.FAILED]
[2025-07-09 16:23:47,648] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.96', 65007) -> ('172.17.240.1', 55456)) State.WAITING -> State.FAILED]
[2025-07-09 16:23:47,649] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.19.96.1', 65008) -> ('172.17.240.1', 55456)) State.FROZEN -> State.FAILED]
[2025-07-09 16:23:47,649] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 65009) -> ('172.17.240.1', 55456)) State.FROZEN -> State.FAILED]
[2025-07-09 16:23:47,649] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 65010) -> ('172.17.240.1', 55456)) State.FROZEN -> State.FAILED]
[2025-07-09 16:23:47,649] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.239.42', 65011) -> ('172.17.240.1', 55456)) State.FROZEN -> State.FAILED]
[2025-07-09 16:23:47,649] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.17.240.1', 65012) -> ('172.17.240.1', 55456)) State.FROZEN -> State.FAILED]
[2025-07-09 16:23:47,650] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.96', 65007) -> ('58.248.180.197', 23485)) State.WAITING -> State.FAILED]
[2025-07-09 16:23:47,650] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.19.96.1', 65008) -> ('58.248.180.197', 23485)) State.FROZEN -> State.FAILED]
[2025-07-09 16:23:47,650] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 65009) -> ('58.248.180.197', 23485)) State.FROZEN -> State.FAILED]
[2025-07-09 16:23:47,650] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 65010) -> ('58.248.180.197', 23485)) State.FROZEN -> State.FAILED]
[2025-07-09 16:23:47,650] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.239.42', 65011) -> ('58.248.180.197', 23485)) State.FROZEN -> State.FAILED]
[2025-07-09 16:23:47,652] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.17.240.1', 65012) -> ('58.248.180.197', 23485)) State.FROZEN -> State.FAILED]
[2025-07-09 16:23:47,652] [ice.py[line:1114]] [INFO] [Connection(0) ICE completed]
[2025-07-09 16:23:47,653] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.19.96.1', 65008) -> ('172.19.96.1', 55448)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-09 16:23:47,653] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 65009) -> ('10.118.7.130', 55450)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-09 16:23:47,699] [rtcrtpsender.py[line:463]] [WARNING] [RTCRtpsender(audio) Traceback (most recent call last):
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\rtcrtpsender.py", line 350, in _run_rtp
    timestamp = uint32_add(timestamp_origin, enc_frame.timestamp)
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\utils.py", line 39, in uint32_add
    return (a + b) & 0xFFFFFFFF
TypeError: unsupported operand type(s) for +: 'int' and 'NoneType'
]
[2025-07-09 16:23:47,712] [rtcrtpsender.py[line:463]] [WARNING] [RTCRtpsender(video) Traceback (most recent call last):
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\rtcrtpsender.py", line 346, in _run_rtp
    enc_frame = await self._next_encoded_frame(codec)
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\rtcrtpsender.py", line 293, in _next_encoded_frame
    payloads, timestamp = await self.__loop.run_in_executor(
  File "D:\gitlab\code\*********************\py38\lib\concurrent\futures\thread.py", line 57, in run
    result = self.fn(*self.args, **self.kwargs)
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\codecs\vpx.py", line 322, in encode
    lib.vpx_codec_encode(
TypeError: an integer is required
]
[2025-07-09 16:25:26,682] [ice.py[line:1114]] [INFO] [Connection(2) Could not bind to 169.254.123.208 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-09 16:25:26,682] [ice.py[line:1114]] [INFO] [Connection(2) Could not bind to 169.254.26.119 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-09 16:25:31,683] [web_log.py[line:211]] [INFO] [127.0.0.1 [09/Jul/2025:16:25:26 +0800] "POST /offer HTTP/1.1" 200 2269 "-" "Python/3.10 aiohttp/3.9.5"]
[2025-07-09 16:25:31,685] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.96', 59758) -> ('2.2.2.96', 59746)) State.FROZEN -> State.WAITING]
[2025-07-09 16:25:31,685] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.19.96.1', 59759) -> ('2.2.2.96', 59746)) State.FROZEN -> State.WAITING]
[2025-07-09 16:25:31,686] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 59760) -> ('2.2.2.96', 59746)) State.FROZEN -> State.WAITING]
[2025-07-09 16:25:31,686] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 59761) -> ('2.2.2.96', 59746)) State.FROZEN -> State.WAITING]
[2025-07-09 16:25:31,686] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.239.42', 59762) -> ('2.2.2.96', 59746)) State.FROZEN -> State.WAITING]
[2025-07-09 16:25:31,686] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.17.240.1', 59763) -> ('2.2.2.96', 59746)) State.FROZEN -> State.WAITING]
[2025-07-09 16:25:31,686] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.96', 59758) -> ('172.19.96.1', 59748)) State.FROZEN -> State.WAITING]
[2025-07-09 16:25:31,688] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.96', 59758) -> ('2.2.2.96', 59746)) State.WAITING -> State.IN_PROGRESS]
[2025-07-09 16:25:31,712] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.19.96.1', 59759) -> ('2.2.2.96', 59746)) State.WAITING -> State.IN_PROGRESS]
[2025-07-09 16:25:31,742] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 59760) -> ('2.2.2.96', 59746)) State.WAITING -> State.IN_PROGRESS]
[2025-07-09 16:25:31,774] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 59761) -> ('2.2.2.96', 59746)) State.WAITING -> State.IN_PROGRESS]
[2025-07-09 16:25:31,780] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.239.42', 59762) -> ('2.2.2.96', 59746)) State.WAITING -> State.IN_PROGRESS]
[2025-07-09 16:25:31,795] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.17.240.1', 59763) -> ('2.2.2.96', 59746)) State.WAITING -> State.IN_PROGRESS]
[2025-07-09 16:25:31,821] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.96', 59758) -> ('172.19.96.1', 59748)) State.WAITING -> State.IN_PROGRESS]
[2025-07-09 16:25:31,828] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.19.96.1', 59759) -> ('172.19.96.1', 59748)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-09 16:25:31,830] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.96', 59758) -> ('2.2.2.96', 59746)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-09 16:25:31,830] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 59760) -> ('172.19.96.1', 59748)) State.FROZEN -> State.FAILED]
[2025-07-09 16:25:31,831] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 59761) -> ('172.19.96.1', 59748)) State.FROZEN -> State.FAILED]
[2025-07-09 16:25:31,831] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.239.42', 59762) -> ('172.19.96.1', 59748)) State.FROZEN -> State.FAILED]
[2025-07-09 16:25:31,831] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.17.240.1', 59763) -> ('172.19.96.1', 59748)) State.FROZEN -> State.FAILED]
[2025-07-09 16:25:31,831] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.96', 59758) -> ('10.118.7.130', 59750)) State.FROZEN -> State.FAILED]
[2025-07-09 16:25:31,831] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.19.96.1', 59759) -> ('10.118.7.130', 59750)) State.FROZEN -> State.FAILED]
[2025-07-09 16:25:31,831] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 59760) -> ('10.118.7.130', 59750)) State.FROZEN -> State.FAILED]
[2025-07-09 16:25:31,832] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 59761) -> ('10.118.7.130', 59750)) State.FROZEN -> State.FAILED]
[2025-07-09 16:25:31,832] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.239.42', 59762) -> ('10.118.7.130', 59750)) State.FROZEN -> State.FAILED]
[2025-07-09 16:25:31,832] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.17.240.1', 59763) -> ('10.118.7.130', 59750)) State.FROZEN -> State.FAILED]
[2025-07-09 16:25:31,832] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.96', 59758) -> ('169.254.43.7', 59752)) State.FROZEN -> State.FAILED]
[2025-07-09 16:25:31,833] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.19.96.1', 59759) -> ('169.254.43.7', 59752)) State.FROZEN -> State.FAILED]
[2025-07-09 16:25:31,833] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 59760) -> ('169.254.43.7', 59752)) State.FROZEN -> State.FAILED]
[2025-07-09 16:25:31,833] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 59761) -> ('169.254.43.7', 59752)) State.FROZEN -> State.FAILED]
[2025-07-09 16:25:31,833] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.239.42', 59762) -> ('169.254.43.7', 59752)) State.FROZEN -> State.FAILED]
[2025-07-09 16:25:31,834] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.17.240.1', 59763) -> ('169.254.43.7', 59752)) State.FROZEN -> State.FAILED]
[2025-07-09 16:25:31,834] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.96', 59758) -> ('10.118.239.42', 59754)) State.FROZEN -> State.FAILED]
[2025-07-09 16:25:31,834] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.19.96.1', 59759) -> ('10.118.239.42', 59754)) State.FROZEN -> State.FAILED]
[2025-07-09 16:25:31,835] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 59760) -> ('10.118.239.42', 59754)) State.FROZEN -> State.FAILED]
[2025-07-09 16:25:31,835] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 59761) -> ('10.118.239.42', 59754)) State.FROZEN -> State.FAILED]
[2025-07-09 16:25:31,835] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.239.42', 59762) -> ('10.118.239.42', 59754)) State.FROZEN -> State.FAILED]
[2025-07-09 16:25:31,835] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.17.240.1', 59763) -> ('10.118.239.42', 59754)) State.FROZEN -> State.FAILED]
[2025-07-09 16:25:31,836] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.96', 59758) -> ('172.17.240.1', 59756)) State.FROZEN -> State.FAILED]
[2025-07-09 16:25:31,836] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.19.96.1', 59759) -> ('172.17.240.1', 59756)) State.FROZEN -> State.FAILED]
[2025-07-09 16:25:31,836] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 59760) -> ('172.17.240.1', 59756)) State.FROZEN -> State.FAILED]
[2025-07-09 16:25:31,836] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 59761) -> ('172.17.240.1', 59756)) State.FROZEN -> State.FAILED]
[2025-07-09 16:25:31,836] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.239.42', 59762) -> ('172.17.240.1', 59756)) State.FROZEN -> State.FAILED]
[2025-07-09 16:25:31,837] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.17.240.1', 59763) -> ('172.17.240.1', 59756)) State.FROZEN -> State.FAILED]
[2025-07-09 16:25:31,837] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.96', 59758) -> ('58.248.180.197', 24023)) State.FROZEN -> State.FAILED]
[2025-07-09 16:25:31,837] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.19.96.1', 59759) -> ('58.248.180.197', 24023)) State.FROZEN -> State.FAILED]
[2025-07-09 16:25:31,837] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 59760) -> ('58.248.180.197', 24023)) State.FROZEN -> State.FAILED]
[2025-07-09 16:25:31,837] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 59761) -> ('58.248.180.197', 24023)) State.FROZEN -> State.FAILED]
[2025-07-09 16:25:31,837] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.239.42', 59762) -> ('58.248.180.197', 24023)) State.FROZEN -> State.FAILED]
[2025-07-09 16:25:31,837] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.17.240.1', 59763) -> ('58.248.180.197', 24023)) State.FROZEN -> State.FAILED]
[2025-07-09 16:25:31,838] [ice.py[line:1114]] [INFO] [Connection(2) ICE completed]
[2025-07-09 16:25:31,839] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.19.96.1', 59759) -> ('172.19.96.1', 59748)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-09 16:25:31,878] [rtcrtpsender.py[line:463]] [WARNING] [RTCRtpsender(audio) Traceback (most recent call last):
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\rtcrtpsender.py", line 350, in _run_rtp
    timestamp = uint32_add(timestamp_origin, enc_frame.timestamp)
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\utils.py", line 39, in uint32_add
    return (a + b) & 0xFFFFFFFF
TypeError: unsupported operand type(s) for +: 'int' and 'NoneType'
]
[2025-07-09 16:25:31,887] [rtcrtpsender.py[line:463]] [WARNING] [RTCRtpsender(video) Traceback (most recent call last):
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\rtcrtpsender.py", line 346, in _run_rtp
    enc_frame = await self._next_encoded_frame(codec)
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\rtcrtpsender.py", line 293, in _next_encoded_frame
    payloads, timestamp = await self.__loop.run_in_executor(
  File "D:\gitlab\code\*********************\py38\lib\concurrent\futures\thread.py", line 57, in run
    result = self.fn(*self.args, **self.kwargs)
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\codecs\vpx.py", line 322, in encode
    lib.vpx_codec_encode(
TypeError: an integer is required
]
[2025-07-09 16:26:22,032] [web_log.py[line:211]] [INFO] [127.0.0.1 [09/Jul/2025:16:26:22 +0800] "GET /offer HTTP/1.1" 405 205 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0"]
[2025-07-09 16:26:22,143] [web_log.py[line:211]] [INFO] [127.0.0.1 [09/Jul/2025:16:26:22 +0800] "GET /favicon.ico HTTP/1.1" 404 174 "http://127.0.0.1:8080/offer" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0"]
[2025-07-09 16:26:25,261] [web_log.py[line:211]] [INFO] [127.0.0.1 [09/Jul/2025:16:26:25 +0800] "GET /offer HTTP/1.1" 405 205 "-" "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0"]
[2025-07-09 16:26:38,667] [run_local.py[line:223]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������.]
[2025-07-09 16:26:39,897] [server.py[line:264]] [INFO] [connection closed]
[2025-07-09 16:26:39,898] [run_local.py[line:265]] [INFO] [��start_websocket_server�����������б�ȡ����]
[2025-07-09 16:26:39,898] [run_local.py[line:268]] [INFO] [��start_websocket_server�����ڹرշ�����...]
[2025-07-09 16:26:39,899] [server.py[line:759]] [INFO] [server closing]
[2025-07-09 16:26:39,899] [server.py[line:793]] [INFO] [server closed]
[2025-07-09 16:26:39,899] [run_local.py[line:271]] [INFO] [��start_websocket_server���������ѳɹ��رա�]
[2025-07-09 16:26:41,522] [run_local.py[line:279]] [INFO] [
��⵽�û��жϣ����������ڹرա�]
[2025-07-09 17:55:29,862] [server.py[line:707]] [INFO] [server listening on 127.0.0.1:8765]
[2025-07-09 17:55:29,862] [run_local.py[line:248]] [INFO] [��start_websocket_server��WebSocket���������� ws://127.0.0.1:8765 ����]
[2025-07-09 17:55:29,862] [run_local.py[line:249]] [INFO] [��start_websocket_server���ȴ�������... (���300����û�����ӽ��Զ��ر�)]
[2025-07-09 17:55:33,522] [server.py[line:642]] [INFO] [connection open]
[2025-07-09 17:55:33,526] [run_local.py[line:241]] [INFO] [��start_websocket_server�����յ���һ���ͻ������ӣ����������������С�]
[2025-07-09 17:55:33,526] [run_local.py[line:166]] [INFO] [��websocket_handler��WebSocket client ��ʼ��������: v3720p]
[2025-07-09 17:55:33,526] [run_local.py[line:178]] [INFO] [��websocket_handler���Ѿ�������ʽ����: v3720p]
[2025-07-09 17:55:33,526] [run_local.py[line:193]] [INFO] [��websocket_handler����ʽ���� fa3ba59424e711f091782228ff08ca3c ��ʼ��ʽ����~ ���~~.]
[2025-07-09 17:55:33,526] [run_local.py[line:257]] [INFO] [��start_websocket_server�����������ڽ����������С�]
[2025-07-09 17:56:06,468] [ice.py[line:1114]] [INFO] [Connection(0) Could not bind to 169.254.123.208 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-09 17:56:06,468] [ice.py[line:1114]] [INFO] [Connection(0) Could not bind to 169.254.26.119 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-09 17:56:11,471] [web_log.py[line:211]] [INFO] [127.0.0.1 [09/Jul/2025:17:56:06 +0800] "POST /offer HTTP/1.1" 200 2269 "-" "Python/3.10 aiohttp/3.9.5"]
[2025-07-09 17:56:11,472] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.38', 61044) -> ('2.2.2.38', 64293)) State.FROZEN -> State.WAITING]
[2025-07-09 17:56:11,472] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 61045) -> ('2.2.2.38', 64293)) State.FROZEN -> State.WAITING]
[2025-07-09 17:56:11,473] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 61046) -> ('2.2.2.38', 64293)) State.FROZEN -> State.WAITING]
[2025-07-09 17:56:11,473] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 61047) -> ('2.2.2.38', 64293)) State.FROZEN -> State.WAITING]
[2025-07-09 17:56:11,473] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.25.32.1', 61048) -> ('2.2.2.38', 64293)) State.FROZEN -> State.WAITING]
[2025-07-09 17:56:11,473] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.38', 61044) -> ('10.118.7.130', 64295)) State.FROZEN -> State.WAITING]
[2025-07-09 17:56:11,474] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.38', 61044) -> ('2.2.2.38', 64293)) State.WAITING -> State.IN_PROGRESS]
[2025-07-09 17:56:11,499] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 61045) -> ('2.2.2.38', 64293)) State.WAITING -> State.IN_PROGRESS]
[2025-07-09 17:56:11,535] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 61046) -> ('2.2.2.38', 64293)) State.WAITING -> State.IN_PROGRESS]
[2025-07-09 17:56:11,555] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 61047) -> ('2.2.2.38', 64293)) State.WAITING -> State.IN_PROGRESS]
[2025-07-09 17:56:11,578] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.25.32.1', 61048) -> ('2.2.2.38', 64293)) State.WAITING -> State.IN_PROGRESS]
[2025-07-09 17:56:11,603] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.38', 61044) -> ('10.118.7.130', 64295)) State.WAITING -> State.IN_PROGRESS]
[2025-07-09 17:56:11,615] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 61045) -> ('10.118.7.130', 64295)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-09 17:56:11,631] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 61046) -> ('10.118.7.130', 64295)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-09 17:56:11,635] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.38', 61044) -> ('2.2.2.38', 64293)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-09 17:56:11,636] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 61047) -> ('10.118.7.130', 64295)) State.FROZEN -> State.FAILED]
[2025-07-09 17:56:11,636] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.25.32.1', 61048) -> ('10.118.7.130', 64295)) State.FROZEN -> State.FAILED]
[2025-07-09 17:56:11,637] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.38', 61044) -> ('169.254.208.220', 64297)) State.FROZEN -> State.FAILED]
[2025-07-09 17:56:11,637] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 61045) -> ('169.254.208.220', 64297)) State.FROZEN -> State.FAILED]
[2025-07-09 17:56:11,637] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 61046) -> ('169.254.208.220', 64297)) State.FROZEN -> State.FAILED]
[2025-07-09 17:56:11,637] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 61047) -> ('169.254.208.220', 64297)) State.FROZEN -> State.FAILED]
[2025-07-09 17:56:11,637] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.25.32.1', 61048) -> ('169.254.208.220', 64297)) State.FROZEN -> State.FAILED]
[2025-07-09 17:56:11,637] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.38', 61044) -> ('169.254.43.7', 64299)) State.FROZEN -> State.FAILED]
[2025-07-09 17:56:11,639] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 61045) -> ('169.254.43.7', 64299)) State.FROZEN -> State.FAILED]
[2025-07-09 17:56:11,639] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 61046) -> ('169.254.43.7', 64299)) State.FROZEN -> State.FAILED]
[2025-07-09 17:56:11,639] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 61047) -> ('169.254.43.7', 64299)) State.FROZEN -> State.FAILED]
[2025-07-09 17:56:11,639] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.25.32.1', 61048) -> ('169.254.43.7', 64299)) State.FROZEN -> State.FAILED]
[2025-07-09 17:56:11,639] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.38', 61044) -> ('172.25.32.1', 64301)) State.FROZEN -> State.FAILED]
[2025-07-09 17:56:11,640] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 61045) -> ('172.25.32.1', 64301)) State.FROZEN -> State.FAILED]
[2025-07-09 17:56:11,640] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 61046) -> ('172.25.32.1', 64301)) State.FROZEN -> State.FAILED]
[2025-07-09 17:56:11,640] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 61047) -> ('172.25.32.1', 64301)) State.FROZEN -> State.FAILED]
[2025-07-09 17:56:11,641] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.25.32.1', 61048) -> ('172.25.32.1', 64301)) State.FROZEN -> State.FAILED]
[2025-07-09 17:56:11,641] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.38', 61044) -> ('58.248.180.197', 38977)) State.FROZEN -> State.FAILED]
[2025-07-09 17:56:11,641] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 61045) -> ('58.248.180.197', 38977)) State.FROZEN -> State.FAILED]
[2025-07-09 17:56:11,641] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 61046) -> ('58.248.180.197', 38977)) State.FROZEN -> State.FAILED]
[2025-07-09 17:56:11,641] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 61047) -> ('58.248.180.197', 38977)) State.FROZEN -> State.FAILED]
[2025-07-09 17:56:11,641] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.25.32.1', 61048) -> ('58.248.180.197', 38977)) State.FROZEN -> State.FAILED]
[2025-07-09 17:56:11,642] [ice.py[line:1114]] [INFO] [Connection(0) ICE completed]
[2025-07-09 17:56:11,643] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 61045) -> ('10.118.7.130', 64295)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-09 17:56:11,669] [rtcrtpsender.py[line:463]] [WARNING] [RTCRtpsender(audio) Traceback (most recent call last):
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\rtcrtpsender.py", line 350, in _run_rtp
    timestamp = uint32_add(timestamp_origin, enc_frame.timestamp)
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\utils.py", line 39, in uint32_add
    return (a + b) & 0xFFFFFFFF
TypeError: unsupported operand type(s) for +: 'int' and 'NoneType'
]
[2025-07-09 17:56:11,679] [rtcrtpsender.py[line:463]] [WARNING] [RTCRtpsender(video) Traceback (most recent call last):
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\rtcrtpsender.py", line 346, in _run_rtp
    enc_frame = await self._next_encoded_frame(codec)
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\rtcrtpsender.py", line 293, in _next_encoded_frame
    payloads, timestamp = await self.__loop.run_in_executor(
  File "D:\gitlab\code\*********************\py38\lib\concurrent\futures\thread.py", line 57, in run
    result = self.fn(*self.args, **self.kwargs)
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\codecs\vpx.py", line 322, in encode
    lib.vpx_codec_encode(
TypeError: an integer is required
]
[2025-07-09 17:56:45,406] [run_local.py[line:223]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������.]
[2025-07-09 17:56:46,977] [server.py[line:264]] [INFO] [connection closed]
[2025-07-09 17:56:46,978] [run_local.py[line:265]] [INFO] [��start_websocket_server�����������б�ȡ����]
[2025-07-09 17:56:46,978] [run_local.py[line:268]] [INFO] [��start_websocket_server�����ڹرշ�����...]
[2025-07-09 17:56:46,978] [server.py[line:759]] [INFO] [server closing]
[2025-07-09 17:56:46,978] [server.py[line:793]] [INFO] [server closed]
[2025-07-09 17:56:46,978] [run_local.py[line:271]] [INFO] [��start_websocket_server���������ѳɹ��رա�]
[2025-07-09 17:56:56,989] [run_local.py[line:279]] [INFO] [
��⵽�û��жϣ����������ڹرա�]
[2025-07-09 20:33:00,789] [server.py[line:707]] [INFO] [server listening on 127.0.0.1:8765]
[2025-07-09 20:33:00,789] [run_local.py[line:248]] [INFO] [��start_websocket_server��WebSocket���������� ws://127.0.0.1:8765 ����]
[2025-07-09 20:33:00,789] [run_local.py[line:249]] [INFO] [��start_websocket_server���ȴ�������... (���300����û�����ӽ��Զ��ر�)]
[2025-07-09 20:33:07,175] [ice.py[line:1114]] [INFO] [Connection(0) Could not bind to 169.254.123.208 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-09 20:33:07,179] [ice.py[line:1114]] [INFO] [Connection(0) Could not bind to 169.254.26.119 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-09 20:33:07,542] [server.py[line:642]] [INFO] [connection open]
[2025-07-09 20:33:07,542] [run_local.py[line:241]] [INFO] [��start_websocket_server�����յ���һ���ͻ������ӣ����������������С�]
[2025-07-09 20:33:07,542] [run_local.py[line:166]] [INFO] [��websocket_handler��WebSocket client ��ʼ��������: v3720p]
[2025-07-09 20:33:07,542] [run_local.py[line:178]] [INFO] [��websocket_handler���Ѿ�������ʽ����: v3720p]
[2025-07-09 20:33:07,542] [run_local.py[line:193]] [INFO] [��websocket_handler����ʽ���� fa3ba59424e711f091782228ff08ca3c ��ʼ��ʽ����~ ���~~.]
[2025-07-09 20:33:07,542] [run_local.py[line:257]] [INFO] [��start_websocket_server�����������ڽ����������С�]
[2025-07-09 20:33:12,192] [web_log.py[line:211]] [INFO] [127.0.0.1 [09/Jul/2025:20:33:07 +0800] "POST /offer HTTP/1.1" 200 2268 "-" "Python/3.10 aiohttp/3.9.5"]
[2025-07-09 20:33:12,192] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.38', 62393) -> ('2.2.2.38', 62383)) State.FROZEN -> State.WAITING]
[2025-07-09 20:33:12,192] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 62394) -> ('2.2.2.38', 62383)) State.FROZEN -> State.WAITING]
[2025-07-09 20:33:12,192] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 62395) -> ('2.2.2.38', 62383)) State.FROZEN -> State.WAITING]
[2025-07-09 20:33:12,192] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 62396) -> ('2.2.2.38', 62383)) State.FROZEN -> State.WAITING]
[2025-07-09 20:33:12,192] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.25.32.1', 62397) -> ('2.2.2.38', 62383)) State.FROZEN -> State.WAITING]
[2025-07-09 20:33:12,192] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.38', 62393) -> ('10.118.7.130', 62385)) State.FROZEN -> State.WAITING]
[2025-07-09 20:33:12,192] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.38', 62393) -> ('2.2.2.38', 62383)) State.WAITING -> State.IN_PROGRESS]
[2025-07-09 20:33:12,198] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.38', 62393) -> ('2.2.2.38', 62383)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-09 20:33:12,199] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.38', 62393) -> ('169.254.208.220', 62387)) State.FROZEN -> State.WAITING]
[2025-07-09 20:33:12,199] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.38', 62393) -> ('169.254.43.7', 62389)) State.FROZEN -> State.WAITING]
[2025-07-09 20:33:12,199] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.38', 62393) -> ('172.25.32.1', 62391)) State.FROZEN -> State.WAITING]
[2025-07-09 20:33:12,199] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.38', 62393) -> ('58.248.180.197', 23647)) State.FROZEN -> State.WAITING]
[2025-07-09 20:33:12,207] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 62394) -> ('2.2.2.38', 62383)) State.WAITING -> State.IN_PROGRESS]
[2025-07-09 20:33:12,232] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 62395) -> ('2.2.2.38', 62383)) State.WAITING -> State.IN_PROGRESS]
[2025-07-09 20:33:12,264] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 62396) -> ('2.2.2.38', 62383)) State.WAITING -> State.IN_PROGRESS]
[2025-07-09 20:33:12,286] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.25.32.1', 62397) -> ('2.2.2.38', 62383)) State.WAITING -> State.IN_PROGRESS]
[2025-07-09 20:33:12,301] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.38', 62393) -> ('10.118.7.130', 62385)) State.WAITING -> State.IN_PROGRESS]
[2025-07-09 20:33:12,315] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.38', 62393) -> ('169.254.208.220', 62387)) State.WAITING -> State.IN_PROGRESS]
[2025-07-09 20:33:12,341] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.38', 62393) -> ('169.254.43.7', 62389)) State.WAITING -> State.IN_PROGRESS]
[2025-07-09 20:33:12,365] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.38', 62393) -> ('172.25.32.1', 62391)) State.WAITING -> State.IN_PROGRESS]
[2025-07-09 20:33:12,390] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.38', 62393) -> ('58.248.180.197', 23647)) State.WAITING -> State.IN_PROGRESS]
[2025-07-09 20:33:12,413] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 62394) -> ('10.118.7.130', 62385)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-09 20:33:12,438] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 62395) -> ('10.118.7.130', 62385)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-09 20:33:12,455] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 62396) -> ('10.118.7.130', 62385)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-09 20:33:12,468] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.25.32.1', 62397) -> ('10.118.7.130', 62385)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-09 20:33:12,498] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 62394) -> ('169.254.208.220', 62387)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-09 20:33:12,521] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 62395) -> ('169.254.208.220', 62387)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-09 20:33:12,531] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 62396) -> ('169.254.208.220', 62387)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-09 20:33:12,533] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.25.32.1', 62397) -> ('169.254.208.220', 62387)) State.FROZEN -> State.FAILED]
[2025-07-09 20:33:12,533] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 62394) -> ('169.254.43.7', 62389)) State.FROZEN -> State.FAILED]
[2025-07-09 20:33:12,534] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 62395) -> ('169.254.43.7', 62389)) State.FROZEN -> State.FAILED]
[2025-07-09 20:33:12,534] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 62396) -> ('169.254.43.7', 62389)) State.FROZEN -> State.FAILED]
[2025-07-09 20:33:12,534] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.25.32.1', 62397) -> ('169.254.43.7', 62389)) State.FROZEN -> State.FAILED]
[2025-07-09 20:33:12,535] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 62394) -> ('172.25.32.1', 62391)) State.FROZEN -> State.FAILED]
[2025-07-09 20:33:12,535] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 62395) -> ('172.25.32.1', 62391)) State.FROZEN -> State.FAILED]
[2025-07-09 20:33:12,536] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 62396) -> ('172.25.32.1', 62391)) State.FROZEN -> State.FAILED]
[2025-07-09 20:33:12,536] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.25.32.1', 62397) -> ('172.25.32.1', 62391)) State.FROZEN -> State.FAILED]
[2025-07-09 20:33:12,536] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 62394) -> ('58.248.180.197', 23647)) State.FROZEN -> State.FAILED]
[2025-07-09 20:33:12,536] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 62395) -> ('58.248.180.197', 23647)) State.FROZEN -> State.FAILED]
[2025-07-09 20:33:12,536] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 62396) -> ('58.248.180.197', 23647)) State.FROZEN -> State.FAILED]
[2025-07-09 20:33:12,537] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.25.32.1', 62397) -> ('58.248.180.197', 23647)) State.FROZEN -> State.FAILED]
[2025-07-09 20:33:12,537] [ice.py[line:1114]] [INFO] [Connection(0) ICE completed]
[2025-07-09 20:33:12,537] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 62394) -> ('10.118.7.130', 62385)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-09 20:33:12,538] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 62395) -> ('169.254.208.220', 62387)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-09 20:33:12,565] [rtcrtpsender.py[line:463]] [WARNING] [RTCRtpsender(audio) Traceback (most recent call last):
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\rtcrtpsender.py", line 350, in _run_rtp
    timestamp = uint32_add(timestamp_origin, enc_frame.timestamp)
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\utils.py", line 39, in uint32_add
    return (a + b) & 0xFFFFFFFF
TypeError: unsupported operand type(s) for +: 'int' and 'NoneType'
]
[2025-07-09 20:33:12,580] [rtcrtpsender.py[line:463]] [WARNING] [RTCRtpsender(video) Traceback (most recent call last):
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\rtcrtpsender.py", line 346, in _run_rtp
    enc_frame = await self._next_encoded_frame(codec)
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\rtcrtpsender.py", line 293, in _next_encoded_frame
    payloads, timestamp = await self.__loop.run_in_executor(
  File "D:\gitlab\code\*********************\py38\lib\concurrent\futures\thread.py", line 57, in run
    result = self.fn(*self.args, **self.kwargs)
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\codecs\vpx.py", line 322, in encode
    lib.vpx_codec_encode(
TypeError: an integer is required
]
[2025-07-09 20:33:35,705] [run_local.py[line:223]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������.]
[2025-07-11 10:38:14,687] [server.py[line:707]] [INFO] [server listening on 127.0.0.1:8765]
[2025-07-11 10:38:14,687] [run_local.py[line:248]] [INFO] [��start_websocket_server��WebSocket���������� ws://127.0.0.1:8765 ����]
[2025-07-11 10:38:14,687] [run_local.py[line:249]] [INFO] [��start_websocket_server���ȴ�������... (���300����û�����ӽ��Զ��ر�)]
[2025-07-11 10:38:14,906] [server.py[line:642]] [INFO] [connection open]
[2025-07-11 10:38:14,906] [run_local.py[line:241]] [INFO] [��start_websocket_server�����յ���һ���ͻ������ӣ����������������С�]
[2025-07-11 10:38:14,906] [run_local.py[line:166]] [INFO] [��websocket_handler��WebSocket client ��ʼ��������: v3720p]
[2025-07-11 10:38:14,906] [run_local.py[line:178]] [INFO] [��websocket_handler���Ѿ�������ʽ����: v3720p]
[2025-07-11 10:38:14,906] [run_local.py[line:193]] [INFO] [��websocket_handler����ʽ���� fa3ba59424e711f091782228ff08ca3c ��ʼ��ʽ����~ ���~~.]
[2025-07-11 10:38:14,906] [run_local.py[line:257]] [INFO] [��start_websocket_server�����������ڽ����������С�]
[2025-07-11 10:38:39,060] [ice.py[line:1114]] [INFO] [Connection(0) Could not bind to 169.254.123.208 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 10:38:39,061] [ice.py[line:1114]] [INFO] [Connection(0) Could not bind to 169.254.26.119 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 10:38:44,070] [web_log.py[line:211]] [INFO] [127.0.0.1 [11/Jul/2025:10:38:39 +0800] "POST /offer HTTP/1.1" 200 3513 "-" "Python/3.10 aiohttp/3.9.5"]
[2025-07-11 10:38:44,070] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 50235) -> ('2.2.2.106', 50225)) State.FROZEN -> State.WAITING]
[2025-07-11 10:38:44,070] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 50236) -> ('2.2.2.106', 50225)) State.FROZEN -> State.WAITING]
[2025-07-11 10:38:44,070] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 50237) -> ('2.2.2.106', 50225)) State.FROZEN -> State.WAITING]
[2025-07-11 10:38:44,070] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 50238) -> ('2.2.2.106', 50225)) State.FROZEN -> State.WAITING]
[2025-07-11 10:38:44,070] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 50239) -> ('2.2.2.106', 50225)) State.FROZEN -> State.WAITING]
[2025-07-11 10:38:44,070] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 50235) -> ('172.26.128.1', 50227)) State.FROZEN -> State.WAITING]
[2025-07-11 10:38:44,070] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 50235) -> ('2.2.2.106', 50225)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 10:38:44,077] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 50235) -> ('2.2.2.106', 50225)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-11 10:38:44,077] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 50235) -> ('10.118.7.130', 50229)) State.FROZEN -> State.WAITING]
[2025-07-11 10:38:44,078] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 50235) -> ('169.254.43.7', 50231)) State.FROZEN -> State.WAITING]
[2025-07-11 10:38:44,078] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 50235) -> ('169.254.208.220', 50233)) State.FROZEN -> State.WAITING]
[2025-07-11 10:38:44,078] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 50235) -> ('58.248.180.197', 32259)) State.FROZEN -> State.WAITING]
[2025-07-11 10:38:44,079] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 50236) -> ('2.2.2.106', 50225)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 10:38:44,101] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 50237) -> ('2.2.2.106', 50225)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 10:38:44,125] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 50238) -> ('2.2.2.106', 50225)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 10:38:44,146] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 50239) -> ('2.2.2.106', 50225)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 10:38:44,231] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 50235) -> ('172.26.128.1', 50227)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 10:38:44,232] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 50235) -> ('10.118.7.130', 50229)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 10:38:44,270] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 50235) -> ('169.254.43.7', 50231)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 10:38:44,295] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 50235) -> ('169.254.208.220', 50233)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 10:38:44,316] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 50235) -> ('58.248.180.197', 32259)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 10:38:44,331] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 50236) -> ('172.26.128.1', 50227)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-11 10:38:44,343] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 50237) -> ('172.26.128.1', 50227)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-11 10:38:44,366] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 50238) -> ('172.26.128.1', 50227)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-11 10:38:44,402] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 50239) -> ('172.26.128.1', 50227)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-11 10:38:44,423] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 50236) -> ('10.118.7.130', 50229)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-11 10:38:44,449] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 50237) -> ('10.118.7.130', 50229)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-11 10:38:44,471] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 50238) -> ('10.118.7.130', 50229)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-11 10:38:44,477] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 50239) -> ('10.118.7.130', 50229)) State.FROZEN -> State.FAILED]
[2025-07-11 10:38:44,477] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 50236) -> ('169.254.43.7', 50231)) State.FROZEN -> State.FAILED]
[2025-07-11 10:38:44,478] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 50237) -> ('169.254.43.7', 50231)) State.FROZEN -> State.FAILED]
[2025-07-11 10:38:44,478] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 50238) -> ('169.254.43.7', 50231)) State.FROZEN -> State.FAILED]
[2025-07-11 10:38:44,478] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 50239) -> ('169.254.43.7', 50231)) State.FROZEN -> State.FAILED]
[2025-07-11 10:38:44,479] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 50236) -> ('169.254.208.220', 50233)) State.FROZEN -> State.FAILED]
[2025-07-11 10:38:44,479] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 50237) -> ('169.254.208.220', 50233)) State.FROZEN -> State.FAILED]
[2025-07-11 10:38:44,479] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 50238) -> ('169.254.208.220', 50233)) State.FROZEN -> State.FAILED]
[2025-07-11 10:38:44,479] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 50239) -> ('169.254.208.220', 50233)) State.FROZEN -> State.FAILED]
[2025-07-11 10:38:44,479] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 50236) -> ('58.248.180.197', 32259)) State.FROZEN -> State.FAILED]
[2025-07-11 10:38:44,479] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 50237) -> ('58.248.180.197', 32259)) State.FROZEN -> State.FAILED]
[2025-07-11 10:38:44,479] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 50238) -> ('58.248.180.197', 32259)) State.FROZEN -> State.FAILED]
[2025-07-11 10:38:44,479] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 50239) -> ('58.248.180.197', 32259)) State.FROZEN -> State.FAILED]
[2025-07-11 10:38:44,480] [ice.py[line:1114]] [INFO] [Connection(0) ICE completed]
[2025-07-11 10:38:44,480] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 50236) -> ('172.26.128.1', 50227)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-11 10:38:44,480] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 50237) -> ('10.118.7.130', 50229)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-11 10:38:44,524] [rtcrtpsender.py[line:463]] [WARNING] [RTCRtpsender(audio) Traceback (most recent call last):
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\rtcrtpsender.py", line 350, in _run_rtp
    timestamp = uint32_add(timestamp_origin, enc_frame.timestamp)
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\utils.py", line 39, in uint32_add
    return (a + b) & 0xFFFFFFFF
TypeError: unsupported operand type(s) for +: 'int' and 'NoneType'
]
[2025-07-11 10:38:44,534] [rtcrtpsender.py[line:463]] [WARNING] [RTCRtpsender(video) Traceback (most recent call last):
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\rtcrtpsender.py", line 346, in _run_rtp
    enc_frame = await self._next_encoded_frame(codec)
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\rtcrtpsender.py", line 293, in _next_encoded_frame
    payloads, timestamp = await self.__loop.run_in_executor(
  File "D:\gitlab\code\*********************\py38\lib\concurrent\futures\thread.py", line 57, in run
    result = self.fn(*self.args, **self.kwargs)
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\codecs\vpx.py", line 322, in encode
    lib.vpx_codec_encode(
TypeError: an integer is required
]
[2025-07-11 10:39:45,961] [server.py[line:642]] [INFO] [connection open]
[2025-07-11 10:39:45,961] [run_local.py[line:166]] [INFO] [��websocket_handler��WebSocket client ��ʼ��������: v3720p]
[2025-07-11 10:39:45,961] [run_local.py[line:178]] [INFO] [��websocket_handler���Ѿ�������ʽ����: v3720p]
[2025-07-11 10:39:45,961] [run_local.py[line:193]] [INFO] [��websocket_handler����ʽ���� fa3ba59424e711f091782228ff08ca3c ��ʼ��ʽ����~ ���~~.]
[2025-07-11 10:39:50,919] [run_local.py[line:216]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������: sent 1011 (internal error) keepalive ping timeout; no close frame received]
[2025-07-11 10:39:50,919] [run_local.py[line:223]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������.]
[2025-07-11 10:39:50,919] [run_local.py[line:225]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������.]
[2025-07-11 10:39:50,919] [server.py[line:264]] [INFO] [connection closed]
[2025-07-11 10:40:16,040] [run_local.py[line:223]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������.]
[2025-07-11 10:40:16,288] [server.py[line:264]] [INFO] [connection closed]
[2025-07-11 10:40:16,289] [run_local.py[line:265]] [INFO] [��start_websocket_server�����������б�ȡ����]
[2025-07-11 10:40:16,289] [run_local.py[line:268]] [INFO] [��start_websocket_server�����ڹرշ�����...]
[2025-07-11 10:40:16,289] [server.py[line:759]] [INFO] [server closing]
[2025-07-11 10:40:16,289] [server.py[line:793]] [INFO] [server closed]
[2025-07-11 10:40:16,289] [run_local.py[line:271]] [INFO] [��start_websocket_server���������ѳɹ��رա�]
[2025-07-11 10:40:26,289] [run_local.py[line:279]] [INFO] [
��⵽�û��жϣ����������ڹرա�]
[2025-07-11 10:46:54,674] [server.py[line:707]] [INFO] [server listening on 127.0.0.1:8765]
[2025-07-11 10:46:54,674] [run_local.py[line:248]] [INFO] [��start_websocket_server��WebSocket���������� ws://127.0.0.1:8765 ����]
[2025-07-11 10:46:54,674] [run_local.py[line:249]] [INFO] [��start_websocket_server���ȴ�������... (���300����û�����ӽ��Զ��ر�)]
[2025-07-11 10:46:58,849] [server.py[line:642]] [INFO] [connection open]
[2025-07-11 10:46:58,849] [run_local.py[line:241]] [INFO] [��start_websocket_server�����յ���һ���ͻ������ӣ����������������С�]
[2025-07-11 10:46:58,849] [run_local.py[line:166]] [INFO] [��websocket_handler��WebSocket client ��ʼ��������: v3720p]
[2025-07-11 10:46:58,849] [run_local.py[line:178]] [INFO] [��websocket_handler���Ѿ�������ʽ����: v3720p]
[2025-07-11 10:46:58,849] [run_local.py[line:193]] [INFO] [��websocket_handler����ʽ���� fa3ba59424e711f091782228ff08ca3c ��ʼ��ʽ����~ ���~~.]
[2025-07-11 10:46:58,849] [run_local.py[line:257]] [INFO] [��start_websocket_server�����������ڽ����������С�]
[2025-07-11 10:47:04,524] [ice.py[line:1114]] [INFO] [Connection(0) Could not bind to 169.254.123.208 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 10:47:04,528] [ice.py[line:1114]] [INFO] [Connection(0) Could not bind to 169.254.26.119 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 10:47:09,534] [web_log.py[line:211]] [INFO] [127.0.0.1 [11/Jul/2025:10:47:04 +0800] "POST /offer HTTP/1.1" 200 3513 "-" "Python/3.10 aiohttp/3.9.5"]
[2025-07-11 10:47:09,534] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 56507) -> ('2.2.2.106', 54567)) State.FROZEN -> State.WAITING]
[2025-07-11 10:47:09,534] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 56508) -> ('2.2.2.106', 54567)) State.FROZEN -> State.WAITING]
[2025-07-11 10:47:09,534] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 56509) -> ('2.2.2.106', 54567)) State.FROZEN -> State.WAITING]
[2025-07-11 10:47:09,534] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 56510) -> ('2.2.2.106', 54567)) State.FROZEN -> State.WAITING]
[2025-07-11 10:47:09,534] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 56511) -> ('2.2.2.106', 54567)) State.FROZEN -> State.WAITING]
[2025-07-11 10:47:09,534] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 56507) -> ('172.26.128.1', 54569)) State.FROZEN -> State.WAITING]
[2025-07-11 10:47:09,534] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 56507) -> ('2.2.2.106', 54567)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 10:47:09,575] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 56508) -> ('2.2.2.106', 54567)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 10:47:09,576] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 56509) -> ('2.2.2.106', 54567)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 10:47:09,593] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 56510) -> ('2.2.2.106', 54567)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 10:47:09,620] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 56511) -> ('2.2.2.106', 54567)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 10:47:09,645] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 56507) -> ('172.26.128.1', 54569)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 10:47:09,669] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 56508) -> ('172.26.128.1', 54569)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-11 10:47:09,692] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 56509) -> ('172.26.128.1', 54569)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-11 10:47:09,693] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 56508) -> ('172.26.128.1', 54569)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-11 10:47:09,693] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 56508) -> ('10.118.7.130', 54571)) State.FROZEN -> State.WAITING]
[2025-07-11 10:47:09,693] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 56508) -> ('169.254.43.7', 54573)) State.FROZEN -> State.WAITING]
[2025-07-11 10:47:09,693] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 56508) -> ('169.254.208.220', 54575)) State.FROZEN -> State.WAITING]
[2025-07-11 10:47:09,693] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 56508) -> ('58.248.180.197', 37054)) State.FROZEN -> State.WAITING]
[2025-07-11 10:47:09,693] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 56507) -> ('2.2.2.106', 54567)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-11 10:47:09,693] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 56510) -> ('172.26.128.1', 54569)) State.FROZEN -> State.FAILED]
[2025-07-11 10:47:09,693] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 56511) -> ('172.26.128.1', 54569)) State.FROZEN -> State.FAILED]
[2025-07-11 10:47:09,693] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 56507) -> ('10.118.7.130', 54571)) State.FROZEN -> State.FAILED]
[2025-07-11 10:47:09,693] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 56508) -> ('10.118.7.130', 54571)) State.WAITING -> State.FAILED]
[2025-07-11 10:47:09,693] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 56509) -> ('10.118.7.130', 54571)) State.FROZEN -> State.FAILED]
[2025-07-11 10:47:09,693] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 56510) -> ('10.118.7.130', 54571)) State.FROZEN -> State.FAILED]
[2025-07-11 10:47:09,693] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 56511) -> ('10.118.7.130', 54571)) State.FROZEN -> State.FAILED]
[2025-07-11 10:47:09,693] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 56507) -> ('169.254.43.7', 54573)) State.FROZEN -> State.FAILED]
[2025-07-11 10:47:09,693] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 56508) -> ('169.254.43.7', 54573)) State.WAITING -> State.FAILED]
[2025-07-11 10:47:09,693] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 56509) -> ('169.254.43.7', 54573)) State.FROZEN -> State.FAILED]
[2025-07-11 10:47:09,693] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 56510) -> ('169.254.43.7', 54573)) State.FROZEN -> State.FAILED]
[2025-07-11 10:47:09,699] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 56511) -> ('169.254.43.7', 54573)) State.FROZEN -> State.FAILED]
[2025-07-11 10:47:09,699] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 56507) -> ('169.254.208.220', 54575)) State.FROZEN -> State.FAILED]
[2025-07-11 10:47:09,699] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 56508) -> ('169.254.208.220', 54575)) State.WAITING -> State.FAILED]
[2025-07-11 10:47:09,699] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 56509) -> ('169.254.208.220', 54575)) State.FROZEN -> State.FAILED]
[2025-07-11 10:47:09,700] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 56510) -> ('169.254.208.220', 54575)) State.FROZEN -> State.FAILED]
[2025-07-11 10:47:09,700] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 56511) -> ('169.254.208.220', 54575)) State.FROZEN -> State.FAILED]
[2025-07-11 10:47:09,700] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 56507) -> ('58.248.180.197', 37054)) State.FROZEN -> State.FAILED]
[2025-07-11 10:47:09,700] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 56508) -> ('58.248.180.197', 37054)) State.WAITING -> State.FAILED]
[2025-07-11 10:47:09,700] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 56509) -> ('58.248.180.197', 37054)) State.FROZEN -> State.FAILED]
[2025-07-11 10:47:09,700] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 56510) -> ('58.248.180.197', 37054)) State.FROZEN -> State.FAILED]
[2025-07-11 10:47:09,701] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 56511) -> ('58.248.180.197', 37054)) State.FROZEN -> State.FAILED]
[2025-07-11 10:47:09,701] [ice.py[line:1114]] [INFO] [Connection(0) ICE completed]
[2025-07-11 10:47:09,720] [rtcrtpsender.py[line:463]] [WARNING] [RTCRtpsender(audio) Traceback (most recent call last):
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\rtcrtpsender.py", line 350, in _run_rtp
    timestamp = uint32_add(timestamp_origin, enc_frame.timestamp)
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\utils.py", line 39, in uint32_add
    return (a + b) & 0xFFFFFFFF
TypeError: unsupported operand type(s) for +: 'int' and 'NoneType'
]
[2025-07-11 10:47:09,730] [rtcrtpsender.py[line:463]] [WARNING] [RTCRtpsender(video) Traceback (most recent call last):
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\rtcrtpsender.py", line 346, in _run_rtp
    enc_frame = await self._next_encoded_frame(codec)
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\rtcrtpsender.py", line 293, in _next_encoded_frame
    payloads, timestamp = await self.__loop.run_in_executor(
  File "D:\gitlab\code\*********************\py38\lib\concurrent\futures\thread.py", line 57, in run
    result = self.fn(*self.args, **self.kwargs)
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\codecs\vpx.py", line 322, in encode
    lib.vpx_codec_encode(
TypeError: an integer is required
]
[2025-07-11 10:48:09,682] [run_local.py[line:223]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������.]
[2025-07-11 10:48:10,444] [server.py[line:264]] [INFO] [connection closed]
[2025-07-11 10:48:10,446] [run_local.py[line:265]] [INFO] [��start_websocket_server�����������б�ȡ����]
[2025-07-11 10:48:10,446] [run_local.py[line:268]] [INFO] [��start_websocket_server�����ڹرշ�����...]
[2025-07-11 10:48:10,446] [server.py[line:759]] [INFO] [server closing]
[2025-07-11 10:48:10,447] [server.py[line:793]] [INFO] [server closed]
[2025-07-11 10:48:10,448] [run_local.py[line:271]] [INFO] [��start_websocket_server���������ѳɹ��رա�]
[2025-07-11 10:48:11,678] [run_local.py[line:279]] [INFO] [
��⵽�û��жϣ����������ڹرա�]
[2025-07-11 10:53:44,554] [server.py[line:707]] [INFO] [server listening on 127.0.0.1:8765]
[2025-07-11 10:53:44,556] [run_local.py[line:248]] [INFO] [��start_websocket_server��WebSocket���������� ws://127.0.0.1:8765 ����]
[2025-07-11 10:53:44,556] [run_local.py[line:249]] [INFO] [��start_websocket_server���ȴ�������... (���300����û�����ӽ��Զ��ر�)]
[2025-07-11 10:53:44,869] [server.py[line:642]] [INFO] [connection open]
[2025-07-11 10:53:44,869] [run_local.py[line:241]] [INFO] [��start_websocket_server�����յ���һ���ͻ������ӣ����������������С�]
[2025-07-11 10:53:44,869] [run_local.py[line:166]] [INFO] [��websocket_handler��WebSocket client ��ʼ��������: v3720p]
[2025-07-11 10:53:44,869] [run_local.py[line:178]] [INFO] [��websocket_handler���Ѿ�������ʽ����: v3720p]
[2025-07-11 10:53:44,869] [run_local.py[line:193]] [INFO] [��websocket_handler����ʽ���� fa3ba59424e711f091782228ff08ca3c ��ʼ��ʽ����~ ���~~.]
[2025-07-11 10:53:44,869] [run_local.py[line:257]] [INFO] [��start_websocket_server�����������ڽ����������С�]
[2025-07-11 10:54:22,696] [ice.py[line:1114]] [INFO] [Connection(0) Could not bind to 169.254.123.208 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 10:54:22,696] [ice.py[line:1114]] [INFO] [Connection(0) Could not bind to 169.254.26.119 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 10:54:27,702] [web_log.py[line:211]] [INFO] [127.0.0.1 [11/Jul/2025:10:54:22 +0800] "POST /offer HTTP/1.1" 200 3513 "-" "Python/3.10 aiohttp/3.9.5"]
[2025-07-11 10:54:27,702] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 55183) -> ('2.2.2.106', 50268)) State.FROZEN -> State.WAITING]
[2025-07-11 10:54:27,702] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 55184) -> ('2.2.2.106', 50268)) State.FROZEN -> State.WAITING]
[2025-07-11 10:54:27,702] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 55185) -> ('2.2.2.106', 50268)) State.FROZEN -> State.WAITING]
[2025-07-11 10:54:27,702] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 55186) -> ('2.2.2.106', 50268)) State.FROZEN -> State.WAITING]
[2025-07-11 10:54:27,702] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 55187) -> ('2.2.2.106', 50268)) State.FROZEN -> State.WAITING]
[2025-07-11 10:54:27,702] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 55183) -> ('172.26.128.1', 50270)) State.FROZEN -> State.WAITING]
[2025-07-11 10:54:27,702] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 55183) -> ('2.2.2.106', 50268)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 10:54:27,722] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 55184) -> ('2.2.2.106', 50268)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 10:54:27,739] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 55185) -> ('2.2.2.106', 50268)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 10:54:27,762] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 55186) -> ('2.2.2.106', 50268)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 10:54:27,764] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 55187) -> ('2.2.2.106', 50268)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 10:54:27,787] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 55183) -> ('172.26.128.1', 50270)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 10:54:27,811] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 55184) -> ('172.26.128.1', 50270)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-11 10:54:27,835] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 55185) -> ('172.26.128.1', 50270)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-11 10:54:27,859] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 55186) -> ('172.26.128.1', 50270)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-11 10:54:27,880] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 55187) -> ('172.26.128.1', 50270)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-11 10:54:27,893] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 55183) -> ('10.118.7.130', 50272)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-11 10:54:27,907] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 55184) -> ('10.118.7.130', 50272)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-11 10:54:27,909] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 55184) -> ('172.26.128.1', 50270)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-11 10:54:27,910] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 55184) -> ('169.254.43.7', 50274)) State.FROZEN -> State.WAITING]
[2025-07-11 10:54:27,910] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 55184) -> ('169.254.208.220', 50276)) State.FROZEN -> State.WAITING]
[2025-07-11 10:54:27,910] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 55184) -> ('58.248.180.197', 41737)) State.FROZEN -> State.WAITING]
[2025-07-11 10:54:27,910] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 55183) -> ('2.2.2.106', 50268)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-11 10:54:27,910] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 55185) -> ('10.118.7.130', 50272)) State.FROZEN -> State.FAILED]
[2025-07-11 10:54:27,910] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 55186) -> ('10.118.7.130', 50272)) State.FROZEN -> State.FAILED]
[2025-07-11 10:54:27,910] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 55187) -> ('10.118.7.130', 50272)) State.FROZEN -> State.FAILED]
[2025-07-11 10:54:27,910] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 55183) -> ('169.254.43.7', 50274)) State.FROZEN -> State.FAILED]
[2025-07-11 10:54:27,910] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 55184) -> ('169.254.43.7', 50274)) State.WAITING -> State.FAILED]
[2025-07-11 10:54:27,912] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 55185) -> ('169.254.43.7', 50274)) State.FROZEN -> State.FAILED]
[2025-07-11 10:54:27,912] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 55186) -> ('169.254.43.7', 50274)) State.FROZEN -> State.FAILED]
[2025-07-11 10:54:27,912] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 55187) -> ('169.254.43.7', 50274)) State.FROZEN -> State.FAILED]
[2025-07-11 10:54:27,912] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 55183) -> ('169.254.208.220', 50276)) State.FROZEN -> State.FAILED]
[2025-07-11 10:54:27,912] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 55184) -> ('169.254.208.220', 50276)) State.WAITING -> State.FAILED]
[2025-07-11 10:54:27,913] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 55185) -> ('169.254.208.220', 50276)) State.FROZEN -> State.FAILED]
[2025-07-11 10:54:27,913] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 55186) -> ('169.254.208.220', 50276)) State.FROZEN -> State.FAILED]
[2025-07-11 10:54:27,913] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 55187) -> ('169.254.208.220', 50276)) State.FROZEN -> State.FAILED]
[2025-07-11 10:54:27,914] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 55183) -> ('58.248.180.197', 41737)) State.FROZEN -> State.FAILED]
[2025-07-11 10:54:27,914] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 55184) -> ('58.248.180.197', 41737)) State.WAITING -> State.FAILED]
[2025-07-11 10:54:27,915] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 55185) -> ('58.248.180.197', 41737)) State.FROZEN -> State.FAILED]
[2025-07-11 10:54:27,915] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 55186) -> ('58.248.180.197', 41737)) State.FROZEN -> State.FAILED]
[2025-07-11 10:54:27,915] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 55187) -> ('58.248.180.197', 41737)) State.FROZEN -> State.FAILED]
[2025-07-11 10:54:27,916] [ice.py[line:1114]] [INFO] [Connection(0) ICE completed]
[2025-07-11 10:54:27,954] [rtcrtpsender.py[line:463]] [WARNING] [RTCRtpsender(audio) Traceback (most recent call last):
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\rtcrtpsender.py", line 350, in _run_rtp
    timestamp = uint32_add(timestamp_origin, enc_frame.timestamp)
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\utils.py", line 39, in uint32_add
    return (a + b) & 0xFFFFFFFF
TypeError: unsupported operand type(s) for +: 'int' and 'NoneType'
]
[2025-07-11 10:54:27,960] [rtcrtpsender.py[line:463]] [WARNING] [RTCRtpsender(video) Traceback (most recent call last):
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\rtcrtpsender.py", line 346, in _run_rtp
    enc_frame = await self._next_encoded_frame(codec)
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\rtcrtpsender.py", line 293, in _next_encoded_frame
    payloads, timestamp = await self.__loop.run_in_executor(
  File "D:\gitlab\code\*********************\py38\lib\concurrent\futures\thread.py", line 57, in run
    result = self.fn(*self.args, **self.kwargs)
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\codecs\vpx.py", line 322, in encode
    lib.vpx_codec_encode(
TypeError: an integer is required
]
[2025-07-11 10:55:13,664] [server.py[line:642]] [INFO] [connection open]
[2025-07-11 10:55:13,664] [run_local.py[line:166]] [INFO] [��websocket_handler��WebSocket client ��ʼ��������: v3720p]
[2025-07-11 10:55:13,664] [run_local.py[line:178]] [INFO] [��websocket_handler���Ѿ�������ʽ����: v3720p]
[2025-07-11 10:55:13,664] [run_local.py[line:193]] [INFO] [��websocket_handler����ʽ���� fa3ba59424e711f091782228ff08ca3c ��ʼ��ʽ����~ ���~~.]
[2025-07-11 10:55:18,657] [run_local.py[line:216]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������: sent 1011 (internal error) keepalive ping timeout; no close frame received]
[2025-07-11 10:55:18,657] [run_local.py[line:223]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������.]
[2025-07-11 10:55:18,657] [run_local.py[line:225]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������.]
[2025-07-11 10:55:18,657] [server.py[line:264]] [INFO] [connection closed]
[2025-07-11 11:19:35,648] [run_local.py[line:223]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������.]
[2025-07-11 11:19:36,329] [server.py[line:264]] [INFO] [connection closed]
[2025-07-11 11:19:36,329] [run_local.py[line:265]] [INFO] [��start_websocket_server�����������б�ȡ����]
[2025-07-11 11:19:36,329] [run_local.py[line:268]] [INFO] [��start_websocket_server�����ڹرշ�����...]
[2025-07-11 11:19:36,332] [server.py[line:759]] [INFO] [server closing]
[2025-07-11 11:19:36,334] [server.py[line:793]] [INFO] [server closed]
[2025-07-11 11:19:36,335] [run_local.py[line:271]] [INFO] [��start_websocket_server���������ѳɹ��رա�]
[2025-07-11 11:19:38,201] [run_local.py[line:279]] [INFO] [
��⵽�û��жϣ����������ڹرա�]
[2025-07-11 11:20:08,900] [server.py[line:707]] [INFO] [server listening on 127.0.0.1:8765]
[2025-07-11 11:20:08,901] [run_local.py[line:248]] [INFO] [��start_websocket_server��WebSocket���������� ws://127.0.0.1:8765 ����]
[2025-07-11 11:20:08,901] [run_local.py[line:249]] [INFO] [��start_websocket_server���ȴ�������... (���300����û�����ӽ��Զ��ر�)]
[2025-07-11 11:20:09,039] [server.py[line:642]] [INFO] [connection open]
[2025-07-11 11:20:09,039] [run_local.py[line:241]] [INFO] [��start_websocket_server�����յ���һ���ͻ������ӣ����������������С�]
[2025-07-11 11:20:09,039] [run_local.py[line:166]] [INFO] [��websocket_handler��WebSocket client ��ʼ��������: v3720p]
[2025-07-11 11:20:09,039] [run_local.py[line:178]] [INFO] [��websocket_handler���Ѿ�������ʽ����: v3720p]
[2025-07-11 11:20:09,039] [run_local.py[line:193]] [INFO] [��websocket_handler����ʽ���� fa3ba59424e711f091782228ff08ca3c ��ʼ��ʽ����~ ���~~.]
[2025-07-11 11:20:09,043] [run_local.py[line:257]] [INFO] [��start_websocket_server�����������ڽ����������С�]
[2025-07-11 11:21:38,344] [server.py[line:707]] [INFO] [server listening on 127.0.0.1:8765]
[2025-07-11 11:21:38,344] [run_local.py[line:248]] [INFO] [��start_websocket_server��WebSocket���������� ws://127.0.0.1:8765 ����]
[2025-07-11 11:21:38,345] [run_local.py[line:249]] [INFO] [��start_websocket_server���ȴ�������... (���300����û�����ӽ��Զ��ر�)]
[2025-07-11 11:21:44,633] [server.py[line:642]] [INFO] [connection open]
[2025-07-11 11:21:44,633] [run_local.py[line:241]] [INFO] [��start_websocket_server�����յ���һ���ͻ������ӣ����������������С�]
[2025-07-11 11:21:44,633] [run_local.py[line:166]] [INFO] [��websocket_handler��WebSocket client ��ʼ��������: v3720p]
[2025-07-11 11:21:44,637] [run_local.py[line:178]] [INFO] [��websocket_handler���Ѿ�������ʽ����: v3720p]
[2025-07-11 11:21:44,637] [run_local.py[line:193]] [INFO] [��websocket_handler����ʽ���� fa3ba59424e711f091782228ff08ca3c ��ʼ��ʽ����~ ���~~.]
[2025-07-11 11:21:44,637] [run_local.py[line:257]] [INFO] [��start_websocket_server�����������ڽ����������С�]
[2025-07-11 11:22:00,180] [ice.py[line:1114]] [INFO] [Connection(0) Could not bind to 169.254.123.208 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 11:22:00,180] [ice.py[line:1114]] [INFO] [Connection(0) Could not bind to 169.254.26.119 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 11:22:05,174] [web_log.py[line:211]] [INFO] [127.0.0.1 [11/Jul/2025:11:22:00 +0800] "POST /offer HTTP/1.1" 200 3513 "-" "Python/3.8 aiohttp/3.10.11"]
[2025-07-11 11:22:05,174] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 52765) -> ('2.2.2.106', 58129)) State.FROZEN -> State.WAITING]
[2025-07-11 11:22:05,174] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 52766) -> ('2.2.2.106', 58129)) State.FROZEN -> State.WAITING]
[2025-07-11 11:22:05,174] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 52767) -> ('2.2.2.106', 58129)) State.FROZEN -> State.WAITING]
[2025-07-11 11:22:05,174] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 52768) -> ('2.2.2.106', 58129)) State.FROZEN -> State.WAITING]
[2025-07-11 11:22:05,174] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 52769) -> ('2.2.2.106', 58129)) State.FROZEN -> State.WAITING]
[2025-07-11 11:22:05,174] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 52765) -> ('172.26.128.1', 58131)) State.FROZEN -> State.WAITING]
[2025-07-11 11:22:05,174] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 52765) -> ('2.2.2.106', 58129)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 11:22:05,180] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 52765) -> ('2.2.2.106', 58129)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-11 11:22:05,180] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 52765) -> ('10.118.7.130', 58133)) State.FROZEN -> State.WAITING]
[2025-07-11 11:22:05,180] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 52765) -> ('169.254.43.7', 58135)) State.FROZEN -> State.WAITING]
[2025-07-11 11:22:05,181] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 52765) -> ('169.254.208.220', 58137)) State.FROZEN -> State.WAITING]
[2025-07-11 11:22:05,181] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 52765) -> ('58.248.180.197', 34583)) State.FROZEN -> State.WAITING]
[2025-07-11 11:22:05,204] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 52766) -> ('2.2.2.106', 58129)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 11:22:05,226] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 52767) -> ('2.2.2.106', 58129)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 11:22:05,242] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 52768) -> ('2.2.2.106', 58129)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 11:22:05,265] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 52769) -> ('2.2.2.106', 58129)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 11:22:05,289] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 52765) -> ('172.26.128.1', 58131)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 11:22:05,313] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 52765) -> ('10.118.7.130', 58133)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 11:22:05,338] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 52765) -> ('169.254.43.7', 58135)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 11:22:05,339] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 52766) -> ('172.26.128.1', 58131)) State.FROZEN -> State.FAILED]
[2025-07-11 11:22:05,339] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 52767) -> ('172.26.128.1', 58131)) State.FROZEN -> State.FAILED]
[2025-07-11 11:22:05,339] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 52768) -> ('172.26.128.1', 58131)) State.FROZEN -> State.FAILED]
[2025-07-11 11:22:05,339] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 52769) -> ('172.26.128.1', 58131)) State.FROZEN -> State.FAILED]
[2025-07-11 11:22:05,339] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 52766) -> ('10.118.7.130', 58133)) State.FROZEN -> State.FAILED]
[2025-07-11 11:22:05,339] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 52767) -> ('10.118.7.130', 58133)) State.FROZEN -> State.FAILED]
[2025-07-11 11:22:05,339] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 52768) -> ('10.118.7.130', 58133)) State.FROZEN -> State.FAILED]
[2025-07-11 11:22:05,339] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 52769) -> ('10.118.7.130', 58133)) State.FROZEN -> State.FAILED]
[2025-07-11 11:22:05,339] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 52766) -> ('169.254.43.7', 58135)) State.FROZEN -> State.FAILED]
[2025-07-11 11:22:05,339] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 52767) -> ('169.254.43.7', 58135)) State.FROZEN -> State.FAILED]
[2025-07-11 11:22:05,339] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 52768) -> ('169.254.43.7', 58135)) State.FROZEN -> State.FAILED]
[2025-07-11 11:22:05,339] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 52769) -> ('169.254.43.7', 58135)) State.FROZEN -> State.FAILED]
[2025-07-11 11:22:05,339] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 52765) -> ('169.254.208.220', 58137)) State.WAITING -> State.FAILED]
[2025-07-11 11:22:05,339] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 52766) -> ('169.254.208.220', 58137)) State.FROZEN -> State.FAILED]
[2025-07-11 11:22:05,339] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 52767) -> ('169.254.208.220', 58137)) State.FROZEN -> State.FAILED]
[2025-07-11 11:22:05,344] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 52768) -> ('169.254.208.220', 58137)) State.FROZEN -> State.FAILED]
[2025-07-11 11:22:05,344] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 52769) -> ('169.254.208.220', 58137)) State.FROZEN -> State.FAILED]
[2025-07-11 11:22:05,344] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 52765) -> ('58.248.180.197', 34583)) State.WAITING -> State.FAILED]
[2025-07-11 11:22:05,344] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 52766) -> ('58.248.180.197', 34583)) State.FROZEN -> State.FAILED]
[2025-07-11 11:22:05,344] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 52767) -> ('58.248.180.197', 34583)) State.FROZEN -> State.FAILED]
[2025-07-11 11:22:05,345] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 52768) -> ('58.248.180.197', 34583)) State.FROZEN -> State.FAILED]
[2025-07-11 11:22:05,345] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 52769) -> ('58.248.180.197', 34583)) State.FROZEN -> State.FAILED]
[2025-07-11 11:22:05,346] [ice.py[line:1114]] [INFO] [Connection(0) ICE completed]
[2025-07-11 11:22:05,363] [rtcrtpsender.py[line:463]] [WARNING] [RTCRtpsender(audio) Traceback (most recent call last):
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\rtcrtpsender.py", line 350, in _run_rtp
    timestamp = uint32_add(timestamp_origin, enc_frame.timestamp)
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\utils.py", line 39, in uint32_add
    return (a + b) & 0xFFFFFFFF
TypeError: unsupported operand type(s) for +: 'int' and 'NoneType'
]
[2025-07-11 11:22:05,370] [rtcrtpsender.py[line:463]] [WARNING] [RTCRtpsender(video) Traceback (most recent call last):
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\rtcrtpsender.py", line 346, in _run_rtp
    enc_frame = await self._next_encoded_frame(codec)
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\rtcrtpsender.py", line 293, in _next_encoded_frame
    payloads, timestamp = await self.__loop.run_in_executor(
  File "D:\gitlab\code\*********************\py38\lib\concurrent\futures\thread.py", line 57, in run
    result = self.fn(*self.args, **self.kwargs)
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\codecs\vpx.py", line 322, in encode
    lib.vpx_codec_encode(
TypeError: an integer is required
]
[2025-07-11 11:22:47,870] [run_local.py[line:223]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������.]
[2025-07-11 11:29:43,826] [server.py[line:707]] [INFO] [server listening on 127.0.0.1:8765]
[2025-07-11 11:29:43,826] [run_local.py[line:248]] [INFO] [��start_websocket_server��WebSocket���������� ws://127.0.0.1:8765 ����]
[2025-07-11 11:29:43,827] [run_local.py[line:249]] [INFO] [��start_websocket_server���ȴ�������... (���300����û�����ӽ��Զ��ر�)]
[2025-07-11 11:29:46,821] [server.py[line:642]] [INFO] [connection open]
[2025-07-11 11:29:46,821] [run_local.py[line:241]] [INFO] [��start_websocket_server�����յ���һ���ͻ������ӣ����������������С�]
[2025-07-11 11:29:46,821] [run_local.py[line:166]] [INFO] [��websocket_handler��WebSocket client ��ʼ��������: v3720p]
[2025-07-11 11:29:46,821] [run_local.py[line:178]] [INFO] [��websocket_handler���Ѿ�������ʽ����: v3720p]
[2025-07-11 11:29:46,821] [run_local.py[line:193]] [INFO] [��websocket_handler����ʽ���� fa3ba59424e711f091782228ff08ca3c ��ʼ��ʽ����~ ���~~.]
[2025-07-11 11:29:46,821] [run_local.py[line:257]] [INFO] [��start_websocket_server�����������ڽ����������С�]
[2025-07-11 11:29:54,270] [ice.py[line:1114]] [INFO] [Connection(0) Could not bind to 169.254.123.208 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 11:29:54,270] [ice.py[line:1114]] [INFO] [Connection(0) Could not bind to 169.254.26.119 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 11:29:59,280] [web_log.py[line:211]] [INFO] [127.0.0.1 [11/Jul/2025:11:29:54 +0800] "POST /offer HTTP/1.1" 200 3512 "-" "Python/3.8 aiohttp/3.10.11"]
[2025-07-11 11:29:59,280] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 60519) -> ('2.2.2.106', 53768)) State.FROZEN -> State.WAITING]
[2025-07-11 11:29:59,280] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 60520) -> ('2.2.2.106', 53768)) State.FROZEN -> State.WAITING]
[2025-07-11 11:29:59,282] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 60521) -> ('2.2.2.106', 53768)) State.FROZEN -> State.WAITING]
[2025-07-11 11:29:59,282] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 60522) -> ('2.2.2.106', 53768)) State.FROZEN -> State.WAITING]
[2025-07-11 11:29:59,283] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 60523) -> ('2.2.2.106', 53768)) State.FROZEN -> State.WAITING]
[2025-07-11 11:29:59,283] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 60519) -> ('172.26.128.1', 53770)) State.FROZEN -> State.WAITING]
[2025-07-11 11:29:59,284] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 60519) -> ('2.2.2.106', 53768)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 11:29:59,308] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 60520) -> ('2.2.2.106', 53768)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 11:29:59,331] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 60521) -> ('2.2.2.106', 53768)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 11:29:59,356] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 60522) -> ('2.2.2.106', 53768)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 11:29:59,380] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 60523) -> ('2.2.2.106', 53768)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 11:29:59,404] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 60519) -> ('172.26.128.1', 53770)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 11:29:59,424] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 60520) -> ('172.26.128.1', 53770)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-11 11:29:59,437] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 60521) -> ('172.26.128.1', 53770)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-11 11:29:59,459] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 60522) -> ('172.26.128.1', 53770)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-11 11:29:59,483] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 60523) -> ('172.26.128.1', 53770)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-11 11:29:59,507] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 60519) -> ('10.118.7.130', 53772)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-11 11:29:59,531] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 60520) -> ('10.118.7.130', 53772)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-11 11:29:59,551] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 60521) -> ('10.118.7.130', 53772)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-11 11:29:59,574] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 60522) -> ('10.118.7.130', 53772)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-11 11:29:59,588] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 60523) -> ('10.118.7.130', 53772)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-11 11:29:59,598] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 60519) -> ('169.254.43.7', 53774)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-11 11:29:59,616] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 60520) -> ('169.254.43.7', 53774)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-11 11:29:59,635] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 60521) -> ('169.254.43.7', 53774)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-11 11:29:59,637] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 60520) -> ('172.26.128.1', 53770)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-11 11:29:59,637] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 60520) -> ('169.254.208.220', 53776)) State.FROZEN -> State.WAITING]
[2025-07-11 11:29:59,638] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 60520) -> ('58.248.180.197', 40210)) State.FROZEN -> State.WAITING]
[2025-07-11 11:29:59,638] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 60521) -> ('10.118.7.130', 53772)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-11 11:29:59,639] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 60521) -> ('169.254.208.220', 53776)) State.FROZEN -> State.WAITING]
[2025-07-11 11:29:59,639] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 60521) -> ('58.248.180.197', 40210)) State.FROZEN -> State.WAITING]
[2025-07-11 11:29:59,639] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 60519) -> ('2.2.2.106', 53768)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-11 11:29:59,640] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 60522) -> ('169.254.43.7', 53774)) State.FROZEN -> State.FAILED]
[2025-07-11 11:29:59,640] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 60523) -> ('169.254.43.7', 53774)) State.FROZEN -> State.FAILED]
[2025-07-11 11:29:59,640] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 60519) -> ('169.254.208.220', 53776)) State.FROZEN -> State.FAILED]
[2025-07-11 11:29:59,641] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 60520) -> ('169.254.208.220', 53776)) State.WAITING -> State.FAILED]
[2025-07-11 11:29:59,641] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 60521) -> ('169.254.208.220', 53776)) State.WAITING -> State.FAILED]
[2025-07-11 11:29:59,641] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 60522) -> ('169.254.208.220', 53776)) State.FROZEN -> State.FAILED]
[2025-07-11 11:29:59,642] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 60523) -> ('169.254.208.220', 53776)) State.FROZEN -> State.FAILED]
[2025-07-11 11:29:59,642] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 60519) -> ('58.248.180.197', 40210)) State.FROZEN -> State.FAILED]
[2025-07-11 11:29:59,642] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 60520) -> ('58.248.180.197', 40210)) State.WAITING -> State.FAILED]
[2025-07-11 11:29:59,642] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 60521) -> ('58.248.180.197', 40210)) State.WAITING -> State.FAILED]
[2025-07-11 11:29:59,642] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 60522) -> ('58.248.180.197', 40210)) State.FROZEN -> State.FAILED]
[2025-07-11 11:29:59,643] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 60523) -> ('58.248.180.197', 40210)) State.FROZEN -> State.FAILED]
[2025-07-11 11:29:59,643] [ice.py[line:1114]] [INFO] [Connection(0) ICE completed]
[2025-07-11 11:29:59,663] [rtcrtpsender.py[line:463]] [WARNING] [RTCRtpsender(audio) Traceback (most recent call last):
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\rtcrtpsender.py", line 350, in _run_rtp
    timestamp = uint32_add(timestamp_origin, enc_frame.timestamp)
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\utils.py", line 39, in uint32_add
    return (a + b) & 0xFFFFFFFF
TypeError: unsupported operand type(s) for +: 'int' and 'NoneType'
]
[2025-07-11 11:29:59,671] [rtcrtpsender.py[line:463]] [WARNING] [RTCRtpsender(video) Traceback (most recent call last):
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\rtcrtpsender.py", line 346, in _run_rtp
    enc_frame = await self._next_encoded_frame(codec)
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\rtcrtpsender.py", line 293, in _next_encoded_frame
    payloads, timestamp = await self.__loop.run_in_executor(
  File "D:\gitlab\code\*********************\py38\lib\concurrent\futures\thread.py", line 57, in run
    result = self.fn(*self.args, **self.kwargs)
  File "D:\gitlab\code\*********************\py38\lib\site-packages\aiortc\codecs\vpx.py", line 322, in encode
    lib.vpx_codec_encode(
TypeError: an integer is required
]
[2025-07-11 11:30:03,385] [run_local.py[line:223]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������.]
[2025-07-11 14:31:00,418] [server.py[line:707]] [INFO] [server listening on 127.0.0.1:8765]
[2025-07-11 14:31:00,419] [run_local.py[line:248]] [INFO] [��start_websocket_server��WebSocket���������� ws://127.0.0.1:8765 ����]
[2025-07-11 14:31:00,419] [run_local.py[line:249]] [INFO] [��start_websocket_server���ȴ�������... (���300����û�����ӽ��Զ��ر�)]
[2025-07-11 14:31:03,629] [server.py[line:642]] [INFO] [connection open]
[2025-07-11 14:31:03,630] [run_local.py[line:241]] [INFO] [��start_websocket_server�����յ���һ���ͻ������ӣ����������������С�]
[2025-07-11 14:31:03,630] [run_local.py[line:166]] [INFO] [��websocket_handler��WebSocket client ��ʼ��������: v3720p]
[2025-07-11 14:31:03,630] [run_local.py[line:178]] [INFO] [��websocket_handler���Ѿ�������ʽ����: v3720p]
[2025-07-11 14:31:03,631] [run_local.py[line:193]] [INFO] [��websocket_handler����ʽ���� fa3ba59424e711f091782228ff08ca3c ��ʼ��ʽ����~ ���~~.]
[2025-07-11 14:31:03,632] [run_local.py[line:257]] [INFO] [��start_websocket_server�����������ڽ����������С�]
[2025-07-11 14:31:14,466] [ice.py[line:1114]] [INFO] [Connection(0) Could not bind to 169.254.123.208 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 14:31:14,467] [ice.py[line:1114]] [INFO] [Connection(0) Could not bind to 169.254.26.119 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 14:31:19,485] [web_log.py[line:211]] [INFO] [127.0.0.1 [11/Jul/2025:14:31:14 +0800] "POST /offer HTTP/1.1" 200 3513 "-" "Python/3.8 aiohttp/3.10.11"]
[2025-07-11 14:31:19,486] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 55828) -> ('2.2.2.106', 55819)) State.FROZEN -> State.WAITING]
[2025-07-11 14:31:19,486] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 55829) -> ('2.2.2.106', 55819)) State.FROZEN -> State.WAITING]
[2025-07-11 14:31:19,487] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 55830) -> ('2.2.2.106', 55819)) State.FROZEN -> State.WAITING]
[2025-07-11 14:31:19,487] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 55831) -> ('2.2.2.106', 55819)) State.FROZEN -> State.WAITING]
[2025-07-11 14:31:19,487] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 55832) -> ('2.2.2.106', 55819)) State.FROZEN -> State.WAITING]
[2025-07-11 14:31:19,487] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 55828) -> ('172.26.128.1', 55821)) State.FROZEN -> State.WAITING]
[2025-07-11 14:31:19,488] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 55828) -> ('2.2.2.106', 55819)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 14:31:19,489] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 55828) -> ('2.2.2.106', 55819)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-11 14:31:19,490] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 55828) -> ('10.118.7.130', 55823)) State.FROZEN -> State.WAITING]
[2025-07-11 14:31:19,490] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 55828) -> ('169.254.43.7', 55825)) State.FROZEN -> State.WAITING]
[2025-07-11 14:31:19,490] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 55828) -> ('169.254.208.220', 55827)) State.FROZEN -> State.WAITING]
[2025-07-11 14:31:19,490] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 55828) -> ('58.248.180.197', 28193)) State.FROZEN -> State.WAITING]
[2025-07-11 14:31:19,514] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 55829) -> ('2.2.2.106', 55819)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 14:31:19,545] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 55830) -> ('2.2.2.106', 55819)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 14:31:19,567] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 55831) -> ('2.2.2.106', 55819)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 14:31:19,583] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 55832) -> ('2.2.2.106', 55819)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 14:31:19,606] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 55828) -> ('172.26.128.1', 55821)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 14:31:19,607] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 55828) -> ('10.118.7.130', 55823)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 14:31:19,637] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 55828) -> ('169.254.43.7', 55825)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 14:31:19,667] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 55828) -> ('169.254.208.220', 55827)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 14:31:19,698] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 55828) -> ('58.248.180.197', 28193)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 14:31:19,727] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 55829) -> ('172.26.128.1', 55821)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-11 14:31:19,734] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 55830) -> ('172.26.128.1', 55821)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-11 14:31:19,774] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 55831) -> ('172.26.128.1', 55821)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-11 14:31:19,792] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 55832) -> ('172.26.128.1', 55821)) State.FROZEN -> State.IN_PROGRESS]
[2025-07-11 14:31:19,793] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 55829) -> ('10.118.7.130', 55823)) State.FROZEN -> State.FAILED]
[2025-07-11 14:31:19,793] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 55830) -> ('10.118.7.130', 55823)) State.FROZEN -> State.FAILED]
[2025-07-11 14:31:19,793] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 55831) -> ('10.118.7.130', 55823)) State.FROZEN -> State.FAILED]
[2025-07-11 14:31:19,793] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 55832) -> ('10.118.7.130', 55823)) State.FROZEN -> State.FAILED]
[2025-07-11 14:31:19,794] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 55829) -> ('169.254.43.7', 55825)) State.FROZEN -> State.FAILED]
[2025-07-11 14:31:19,794] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 55830) -> ('169.254.43.7', 55825)) State.FROZEN -> State.FAILED]
[2025-07-11 14:31:19,794] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 55831) -> ('169.254.43.7', 55825)) State.FROZEN -> State.FAILED]
[2025-07-11 14:31:19,795] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 55832) -> ('169.254.43.7', 55825)) State.FROZEN -> State.FAILED]
[2025-07-11 14:31:19,795] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 55829) -> ('169.254.208.220', 55827)) State.FROZEN -> State.FAILED]
[2025-07-11 14:31:19,795] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 55830) -> ('169.254.208.220', 55827)) State.FROZEN -> State.FAILED]
[2025-07-11 14:31:19,795] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 55831) -> ('169.254.208.220', 55827)) State.FROZEN -> State.FAILED]
[2025-07-11 14:31:19,796] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 55832) -> ('169.254.208.220', 55827)) State.FROZEN -> State.FAILED]
[2025-07-11 14:31:19,796] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 55829) -> ('58.248.180.197', 28193)) State.FROZEN -> State.FAILED]
[2025-07-11 14:31:19,796] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 55830) -> ('58.248.180.197', 28193)) State.FROZEN -> State.FAILED]
[2025-07-11 14:31:19,796] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 55831) -> ('58.248.180.197', 28193)) State.FROZEN -> State.FAILED]
[2025-07-11 14:31:19,797] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 55832) -> ('58.248.180.197', 28193)) State.FROZEN -> State.FAILED]
[2025-07-11 14:31:19,797] [ice.py[line:1114]] [INFO] [Connection(0) ICE completed]
[2025-07-11 14:31:19,798] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 55829) -> ('172.26.128.1', 55821)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-11 14:32:11,517] [ice.py[line:1114]] [INFO] [Connection(2) Could not bind to 169.254.123.208 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 14:32:11,518] [ice.py[line:1114]] [INFO] [Connection(2) Could not bind to 169.254.26.119 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 14:32:16,517] [web_log.py[line:211]] [INFO] [127.0.0.1 [11/Jul/2025:14:32:11 +0800] "POST /offer HTTP/1.1" 200 3511 "-" "Python/3.8 aiohttp/3.10.11"]
[2025-07-11 14:32:16,518] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 50492) -> ('2.2.2.106', 59472)) State.FROZEN -> State.WAITING]
[2025-07-11 14:32:16,518] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.26.128.1', 50493) -> ('2.2.2.106', 59472)) State.FROZEN -> State.WAITING]
[2025-07-11 14:32:16,518] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 50494) -> ('2.2.2.106', 59472)) State.FROZEN -> State.WAITING]
[2025-07-11 14:32:16,519] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 50495) -> ('2.2.2.106', 59472)) State.FROZEN -> State.WAITING]
[2025-07-11 14:32:16,519] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.208.220', 50496) -> ('2.2.2.106', 59472)) State.FROZEN -> State.WAITING]
[2025-07-11 14:32:16,519] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 50492) -> ('172.26.128.1', 59474)) State.FROZEN -> State.WAITING]
[2025-07-11 14:32:16,520] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 50492) -> ('2.2.2.106', 59472)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 14:32:16,522] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 50492) -> ('2.2.2.106', 59472)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-11 14:32:16,522] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 50492) -> ('10.118.7.130', 59476)) State.FROZEN -> State.WAITING]
[2025-07-11 14:32:16,522] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 50492) -> ('169.254.43.7', 59478)) State.FROZEN -> State.WAITING]
[2025-07-11 14:32:16,522] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 50492) -> ('169.254.208.220', 59480)) State.FROZEN -> State.WAITING]
[2025-07-11 14:32:16,522] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 50492) -> ('58.248.180.197', 28783)) State.FROZEN -> State.WAITING]
[2025-07-11 14:32:16,546] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.26.128.1', 50493) -> ('2.2.2.106', 59472)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 14:32:16,576] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 50494) -> ('2.2.2.106', 59472)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 14:32:16,608] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 50495) -> ('2.2.2.106', 59472)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 14:32:16,628] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.208.220', 50496) -> ('2.2.2.106', 59472)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 14:32:16,654] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 50492) -> ('172.26.128.1', 59474)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 14:32:16,666] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.26.128.1', 50493) -> ('172.26.128.1', 59474)) State.FROZEN -> State.FAILED]
[2025-07-11 14:32:16,666] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 50494) -> ('172.26.128.1', 59474)) State.FROZEN -> State.FAILED]
[2025-07-11 14:32:16,667] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 50495) -> ('172.26.128.1', 59474)) State.FROZEN -> State.FAILED]
[2025-07-11 14:32:16,667] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.208.220', 50496) -> ('172.26.128.1', 59474)) State.FROZEN -> State.FAILED]
[2025-07-11 14:32:16,667] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 50492) -> ('10.118.7.130', 59476)) State.WAITING -> State.FAILED]
[2025-07-11 14:32:16,667] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.26.128.1', 50493) -> ('10.118.7.130', 59476)) State.FROZEN -> State.FAILED]
[2025-07-11 14:32:16,668] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 50494) -> ('10.118.7.130', 59476)) State.FROZEN -> State.FAILED]
[2025-07-11 14:32:16,668] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 50495) -> ('10.118.7.130', 59476)) State.FROZEN -> State.FAILED]
[2025-07-11 14:32:16,668] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.208.220', 50496) -> ('10.118.7.130', 59476)) State.FROZEN -> State.FAILED]
[2025-07-11 14:32:16,668] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 50492) -> ('169.254.43.7', 59478)) State.WAITING -> State.FAILED]
[2025-07-11 14:32:16,668] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.26.128.1', 50493) -> ('169.254.43.7', 59478)) State.FROZEN -> State.FAILED]
[2025-07-11 14:32:16,669] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 50494) -> ('169.254.43.7', 59478)) State.FROZEN -> State.FAILED]
[2025-07-11 14:32:16,669] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 50495) -> ('169.254.43.7', 59478)) State.FROZEN -> State.FAILED]
[2025-07-11 14:32:16,669] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.208.220', 50496) -> ('169.254.43.7', 59478)) State.FROZEN -> State.FAILED]
[2025-07-11 14:32:16,669] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 50492) -> ('169.254.208.220', 59480)) State.WAITING -> State.FAILED]
[2025-07-11 14:32:16,669] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.26.128.1', 50493) -> ('169.254.208.220', 59480)) State.FROZEN -> State.FAILED]
[2025-07-11 14:32:16,669] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 50494) -> ('169.254.208.220', 59480)) State.FROZEN -> State.FAILED]
[2025-07-11 14:32:16,670] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 50495) -> ('169.254.208.220', 59480)) State.FROZEN -> State.FAILED]
[2025-07-11 14:32:16,670] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.208.220', 50496) -> ('169.254.208.220', 59480)) State.FROZEN -> State.FAILED]
[2025-07-11 14:32:16,670] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 50492) -> ('58.248.180.197', 28783)) State.WAITING -> State.FAILED]
[2025-07-11 14:32:16,670] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.26.128.1', 50493) -> ('58.248.180.197', 28783)) State.FROZEN -> State.FAILED]
[2025-07-11 14:32:16,671] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 50494) -> ('58.248.180.197', 28783)) State.FROZEN -> State.FAILED]
[2025-07-11 14:32:16,671] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 50495) -> ('58.248.180.197', 28783)) State.FROZEN -> State.FAILED]
[2025-07-11 14:32:16,671] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.208.220', 50496) -> ('58.248.180.197', 28783)) State.FROZEN -> State.FAILED]
[2025-07-11 14:32:16,671] [ice.py[line:1114]] [INFO] [Connection(2) ICE completed]
[2025-07-11 14:33:14,710] [run_local.py[line:216]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������: no close frame received or sent]
[2025-07-11 14:33:14,710] [run_local.py[line:223]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������.]
[2025-07-11 14:33:14,770] [run_local.py[line:225]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������.]
[2025-07-11 14:33:14,771] [server.py[line:264]] [INFO] [connection closed]
[2025-07-11 14:33:14,820] [run_local.py[line:265]] [INFO] [��start_websocket_server�����������б�ȡ����]
[2025-07-11 14:33:14,820] [run_local.py[line:268]] [INFO] [��start_websocket_server�����ڹرշ�����...]
[2025-07-11 14:33:14,821] [server.py[line:759]] [INFO] [server closing]
[2025-07-11 14:33:14,821] [server.py[line:793]] [INFO] [server closed]
[2025-07-11 14:33:14,821] [run_local.py[line:271]] [INFO] [��start_websocket_server���������ѳɹ��رա�]
[2025-07-11 14:33:14,822] [run_local.py[line:279]] [INFO] [
��⵽�û��жϣ����������ڹرա�]
[2025-07-11 14:35:24,785] [server.py[line:707]] [INFO] [server listening on 127.0.0.1:8765]
[2025-07-11 14:35:24,786] [run_local.py[line:248]] [INFO] [��start_websocket_server��WebSocket���������� ws://127.0.0.1:8765 ����]
[2025-07-11 14:35:24,786] [run_local.py[line:249]] [INFO] [��start_websocket_server���ȴ�������... (���300����û�����ӽ��Զ��ر�)]
[2025-07-11 14:35:25,063] [ice.py[line:1114]] [INFO] [Connection(0) Could not bind to 169.254.123.208 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 14:35:25,064] [ice.py[line:1114]] [INFO] [Connection(0) Could not bind to 169.254.26.119 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 14:35:30,083] [web_log.py[line:211]] [INFO] [127.0.0.1 [11/Jul/2025:14:35:25 +0800] "POST /offer HTTP/1.1" 200 3510 "-" "Python/3.8 aiohttp/3.10.11"]
[2025-07-11 14:35:30,084] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 62061) -> ('2.2.2.106', 51760)) State.FROZEN -> State.WAITING]
[2025-07-11 14:35:30,084] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 62062) -> ('2.2.2.106', 51760)) State.FROZEN -> State.WAITING]
[2025-07-11 14:35:30,084] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 62063) -> ('2.2.2.106', 51760)) State.FROZEN -> State.WAITING]
[2025-07-11 14:35:30,085] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 62064) -> ('2.2.2.106', 51760)) State.FROZEN -> State.WAITING]
[2025-07-11 14:35:30,085] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 62065) -> ('2.2.2.106', 51760)) State.FROZEN -> State.WAITING]
[2025-07-11 14:35:30,085] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 62061) -> ('172.26.128.1', 51762)) State.FROZEN -> State.WAITING]
[2025-07-11 14:35:30,086] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 62061) -> ('2.2.2.106', 51760)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 14:35:30,087] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 62061) -> ('2.2.2.106', 51760)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-11 14:35:30,087] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 62061) -> ('10.118.7.130', 51764)) State.FROZEN -> State.WAITING]
[2025-07-11 14:35:30,087] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 62061) -> ('169.254.43.7', 51766)) State.FROZEN -> State.WAITING]
[2025-07-11 14:35:30,088] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 62061) -> ('169.254.208.220', 51768)) State.FROZEN -> State.WAITING]
[2025-07-11 14:35:30,088] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 62061) -> ('58.248.180.197', 30727)) State.FROZEN -> State.WAITING]
[2025-07-11 14:35:30,110] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 62062) -> ('2.2.2.106', 51760)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 14:35:30,141] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 62063) -> ('2.2.2.106', 51760)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 14:35:30,172] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 62064) -> ('2.2.2.106', 51760)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 14:35:30,203] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 62065) -> ('2.2.2.106', 51760)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 14:35:30,210] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 62061) -> ('172.26.128.1', 51762)) State.WAITING -> State.FAILED]
[2025-07-11 14:35:30,210] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 62062) -> ('172.26.128.1', 51762)) State.FROZEN -> State.FAILED]
[2025-07-11 14:35:30,211] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 62063) -> ('172.26.128.1', 51762)) State.FROZEN -> State.FAILED]
[2025-07-11 14:35:30,211] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 62064) -> ('172.26.128.1', 51762)) State.FROZEN -> State.FAILED]
[2025-07-11 14:35:30,211] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 62065) -> ('172.26.128.1', 51762)) State.FROZEN -> State.FAILED]
[2025-07-11 14:35:30,211] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 62061) -> ('10.118.7.130', 51764)) State.WAITING -> State.FAILED]
[2025-07-11 14:35:30,212] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 62062) -> ('10.118.7.130', 51764)) State.FROZEN -> State.FAILED]
[2025-07-11 14:35:30,212] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 62063) -> ('10.118.7.130', 51764)) State.FROZEN -> State.FAILED]
[2025-07-11 14:35:30,212] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 62064) -> ('10.118.7.130', 51764)) State.FROZEN -> State.FAILED]
[2025-07-11 14:35:30,212] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 62065) -> ('10.118.7.130', 51764)) State.FROZEN -> State.FAILED]
[2025-07-11 14:35:30,212] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 62061) -> ('169.254.43.7', 51766)) State.WAITING -> State.FAILED]
[2025-07-11 14:35:30,212] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 62062) -> ('169.254.43.7', 51766)) State.FROZEN -> State.FAILED]
[2025-07-11 14:35:30,213] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 62063) -> ('169.254.43.7', 51766)) State.FROZEN -> State.FAILED]
[2025-07-11 14:35:30,213] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 62064) -> ('169.254.43.7', 51766)) State.FROZEN -> State.FAILED]
[2025-07-11 14:35:30,213] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 62065) -> ('169.254.43.7', 51766)) State.FROZEN -> State.FAILED]
[2025-07-11 14:35:30,213] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 62061) -> ('169.254.208.220', 51768)) State.WAITING -> State.FAILED]
[2025-07-11 14:35:30,213] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 62062) -> ('169.254.208.220', 51768)) State.FROZEN -> State.FAILED]
[2025-07-11 14:35:30,213] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 62063) -> ('169.254.208.220', 51768)) State.FROZEN -> State.FAILED]
[2025-07-11 14:35:30,213] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 62064) -> ('169.254.208.220', 51768)) State.FROZEN -> State.FAILED]
[2025-07-11 14:35:30,213] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 62065) -> ('169.254.208.220', 51768)) State.FROZEN -> State.FAILED]
[2025-07-11 14:35:30,214] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 62061) -> ('58.248.180.197', 30727)) State.WAITING -> State.FAILED]
[2025-07-11 14:35:30,214] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 62062) -> ('58.248.180.197', 30727)) State.FROZEN -> State.FAILED]
[2025-07-11 14:35:30,214] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 62063) -> ('58.248.180.197', 30727)) State.FROZEN -> State.FAILED]
[2025-07-11 14:35:30,214] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 62064) -> ('58.248.180.197', 30727)) State.FROZEN -> State.FAILED]
[2025-07-11 14:35:30,214] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 62065) -> ('58.248.180.197', 30727)) State.FROZEN -> State.FAILED]
[2025-07-11 14:35:30,214] [ice.py[line:1114]] [INFO] [Connection(0) ICE completed]
[2025-07-11 14:35:31,141] [server.py[line:642]] [INFO] [connection open]
[2025-07-11 14:35:31,142] [run_local.py[line:241]] [INFO] [��start_websocket_server�����յ���һ���ͻ������ӣ����������������С�]
[2025-07-11 14:35:31,142] [run_local.py[line:166]] [INFO] [��websocket_handler��WebSocket client ��ʼ��������: v3720p]
[2025-07-11 14:35:31,142] [run_local.py[line:178]] [INFO] [��websocket_handler���Ѿ�������ʽ����: v3720p]
[2025-07-11 14:35:31,143] [run_local.py[line:193]] [INFO] [��websocket_handler����ʽ���� fa3ba59424e711f091782228ff08ca3c ��ʼ��ʽ����~ ���~~.]
[2025-07-11 14:35:31,143] [run_local.py[line:257]] [INFO] [��start_websocket_server�����������ڽ����������С�]
[2025-07-11 14:36:22,439] [run_local.py[line:223]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������.]
[2025-07-11 14:39:09,390] [server.py[line:707]] [INFO] [server listening on 127.0.0.1:8765]
[2025-07-11 14:39:09,391] [run_local.py[line:248]] [INFO] [��start_websocket_server��WebSocket���������� ws://127.0.0.1:8765 ����]
[2025-07-11 14:39:09,391] [run_local.py[line:249]] [INFO] [��start_websocket_server���ȴ�������... (���300����û�����ӽ��Զ��ر�)]
[2025-07-11 14:39:13,574] [server.py[line:642]] [INFO] [connection open]
[2025-07-11 14:39:13,574] [run_local.py[line:241]] [INFO] [��start_websocket_server�����յ���һ���ͻ������ӣ����������������С�]
[2025-07-11 14:39:13,575] [run_local.py[line:166]] [INFO] [��websocket_handler��WebSocket client ��ʼ��������: v3720p]
[2025-07-11 14:39:13,575] [run_local.py[line:178]] [INFO] [��websocket_handler���Ѿ�������ʽ����: v3720p]
[2025-07-11 14:39:13,576] [run_local.py[line:193]] [INFO] [��websocket_handler����ʽ���� fa3ba59424e711f091782228ff08ca3c ��ʼ��ʽ����~ ���~~.]
[2025-07-11 14:39:13,577] [run_local.py[line:257]] [INFO] [��start_websocket_server�����������ڽ����������С�]
[2025-07-11 14:39:35,859] [ice.py[line:1114]] [INFO] [Connection(0) Could not bind to 169.254.123.208 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 14:39:35,860] [ice.py[line:1114]] [INFO] [Connection(0) Could not bind to 169.254.26.119 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 14:39:40,860] [web_log.py[line:211]] [INFO] [127.0.0.1 [11/Jul/2025:14:39:35 +0800] "POST /offer HTTP/1.1" 200 3508 "-" "Python/3.8 aiohttp/3.10.11"]
[2025-07-11 14:39:40,861] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 49263) -> ('2.2.2.106', 49250)) State.FROZEN -> State.WAITING]
[2025-07-11 14:39:40,861] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 49264) -> ('2.2.2.106', 49250)) State.FROZEN -> State.WAITING]
[2025-07-11 14:39:40,862] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 49265) -> ('2.2.2.106', 49250)) State.FROZEN -> State.WAITING]
[2025-07-11 14:39:40,862] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 49266) -> ('2.2.2.106', 49250)) State.FROZEN -> State.WAITING]
[2025-07-11 14:39:40,862] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 49267) -> ('2.2.2.106', 49250)) State.FROZEN -> State.WAITING]
[2025-07-11 14:39:40,862] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 49263) -> ('172.26.128.1', 49252)) State.FROZEN -> State.WAITING]
[2025-07-11 14:39:40,864] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 49263) -> ('2.2.2.106', 49250)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 14:39:40,865] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 49263) -> ('2.2.2.106', 49250)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-11 14:39:40,865] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 49263) -> ('10.118.7.130', 49254)) State.FROZEN -> State.WAITING]
[2025-07-11 14:39:40,865] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 49263) -> ('169.254.43.7', 49256)) State.FROZEN -> State.WAITING]
[2025-07-11 14:39:40,866] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 49263) -> ('169.254.208.220', 49258)) State.FROZEN -> State.WAITING]
[2025-07-11 14:39:40,866] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 49263) -> ('58.248.180.197', 33556)) State.FROZEN -> State.WAITING]
[2025-07-11 14:39:40,897] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 49264) -> ('2.2.2.106', 49250)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 14:39:40,927] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 49265) -> ('2.2.2.106', 49250)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 14:39:40,957] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 49266) -> ('2.2.2.106', 49250)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 14:39:40,989] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 49267) -> ('2.2.2.106', 49250)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 14:39:41,004] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 49263) -> ('172.26.128.1', 49252)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 14:39:41,016] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 49263) -> ('10.118.7.130', 49254)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 14:39:41,025] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 49264) -> ('172.26.128.1', 49252)) State.FROZEN -> State.FAILED]
[2025-07-11 14:39:41,025] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 49265) -> ('172.26.128.1', 49252)) State.FROZEN -> State.FAILED]
[2025-07-11 14:39:41,026] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 49266) -> ('172.26.128.1', 49252)) State.FROZEN -> State.FAILED]
[2025-07-11 14:39:41,026] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 49267) -> ('172.26.128.1', 49252)) State.FROZEN -> State.FAILED]
[2025-07-11 14:39:41,026] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 49264) -> ('10.118.7.130', 49254)) State.FROZEN -> State.FAILED]
[2025-07-11 14:39:41,026] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 49265) -> ('10.118.7.130', 49254)) State.FROZEN -> State.FAILED]
[2025-07-11 14:39:41,026] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 49266) -> ('10.118.7.130', 49254)) State.FROZEN -> State.FAILED]
[2025-07-11 14:39:41,026] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 49267) -> ('10.118.7.130', 49254)) State.FROZEN -> State.FAILED]
[2025-07-11 14:39:41,027] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 49263) -> ('169.254.43.7', 49256)) State.WAITING -> State.FAILED]
[2025-07-11 14:39:41,027] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 49264) -> ('169.254.43.7', 49256)) State.FROZEN -> State.FAILED]
[2025-07-11 14:39:41,027] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 49265) -> ('169.254.43.7', 49256)) State.FROZEN -> State.FAILED]
[2025-07-11 14:39:41,027] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 49266) -> ('169.254.43.7', 49256)) State.FROZEN -> State.FAILED]
[2025-07-11 14:39:41,027] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 49267) -> ('169.254.43.7', 49256)) State.FROZEN -> State.FAILED]
[2025-07-11 14:39:41,028] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 49263) -> ('169.254.208.220', 49258)) State.WAITING -> State.FAILED]
[2025-07-11 14:39:41,028] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 49264) -> ('169.254.208.220', 49258)) State.FROZEN -> State.FAILED]
[2025-07-11 14:39:41,028] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 49265) -> ('169.254.208.220', 49258)) State.FROZEN -> State.FAILED]
[2025-07-11 14:39:41,028] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 49266) -> ('169.254.208.220', 49258)) State.FROZEN -> State.FAILED]
[2025-07-11 14:39:41,029] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 49267) -> ('169.254.208.220', 49258)) State.FROZEN -> State.FAILED]
[2025-07-11 14:39:41,029] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 49263) -> ('58.248.180.197', 33556)) State.WAITING -> State.FAILED]
[2025-07-11 14:39:41,029] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 49264) -> ('58.248.180.197', 33556)) State.FROZEN -> State.FAILED]
[2025-07-11 14:39:41,029] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 49265) -> ('58.248.180.197', 33556)) State.FROZEN -> State.FAILED]
[2025-07-11 14:39:41,029] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 49266) -> ('58.248.180.197', 33556)) State.FROZEN -> State.FAILED]
[2025-07-11 14:39:41,029] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 49267) -> ('58.248.180.197', 33556)) State.FROZEN -> State.FAILED]
[2025-07-11 14:39:41,030] [ice.py[line:1114]] [INFO] [Connection(0) ICE completed]
[2025-07-11 14:40:22,810] [ice.py[line:1114]] [INFO] [Connection(2) Could not bind to 169.254.123.208 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 14:40:22,811] [ice.py[line:1114]] [INFO] [Connection(2) Could not bind to 169.254.26.119 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 14:40:27,827] [web_log.py[line:211]] [INFO] [127.0.0.1 [11/Jul/2025:14:40:22 +0800] "POST /offer HTTP/1.1" 200 3513 "-" "Python/3.8 aiohttp/3.10.11"]
[2025-07-11 14:40:27,828] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 61115) -> ('2.2.2.106', 61105)) State.FROZEN -> State.WAITING]
[2025-07-11 14:40:27,828] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.26.128.1', 61116) -> ('2.2.2.106', 61105)) State.FROZEN -> State.WAITING]
[2025-07-11 14:40:27,828] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 61117) -> ('2.2.2.106', 61105)) State.FROZEN -> State.WAITING]
[2025-07-11 14:40:27,829] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 61118) -> ('2.2.2.106', 61105)) State.FROZEN -> State.WAITING]
[2025-07-11 14:40:27,829] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.208.220', 61119) -> ('2.2.2.106', 61105)) State.FROZEN -> State.WAITING]
[2025-07-11 14:40:27,829] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 61115) -> ('172.26.128.1', 61107)) State.FROZEN -> State.WAITING]
[2025-07-11 14:40:27,831] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 61115) -> ('2.2.2.106', 61105)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 14:40:27,833] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 61115) -> ('2.2.2.106', 61105)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-11 14:40:27,833] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 61115) -> ('10.118.7.130', 61109)) State.FROZEN -> State.WAITING]
[2025-07-11 14:40:27,833] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 61115) -> ('169.254.43.7', 61111)) State.FROZEN -> State.WAITING]
[2025-07-11 14:40:27,833] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 61115) -> ('169.254.208.220', 61113)) State.FROZEN -> State.WAITING]
[2025-07-11 14:40:27,833] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 61115) -> ('58.248.180.197', 34159)) State.FROZEN -> State.WAITING]
[2025-07-11 14:40:27,856] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.26.128.1', 61116) -> ('2.2.2.106', 61105)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 14:40:27,857] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 61117) -> ('2.2.2.106', 61105)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 14:40:27,886] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 61118) -> ('2.2.2.106', 61105)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 14:40:27,892] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.208.220', 61119) -> ('2.2.2.106', 61105)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 14:40:27,906] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 61115) -> ('172.26.128.1', 61107)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 14:40:27,932] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 61115) -> ('10.118.7.130', 61109)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 14:40:27,963] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 61115) -> ('169.254.43.7', 61111)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 14:40:27,995] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 61115) -> ('169.254.208.220', 61113)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 14:40:27,996] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.26.128.1', 61116) -> ('172.26.128.1', 61107)) State.FROZEN -> State.FAILED]
[2025-07-11 14:40:27,996] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 61117) -> ('172.26.128.1', 61107)) State.FROZEN -> State.FAILED]
[2025-07-11 14:40:27,996] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 61118) -> ('172.26.128.1', 61107)) State.FROZEN -> State.FAILED]
[2025-07-11 14:40:27,996] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.208.220', 61119) -> ('172.26.128.1', 61107)) State.FROZEN -> State.FAILED]
[2025-07-11 14:40:27,997] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.26.128.1', 61116) -> ('10.118.7.130', 61109)) State.FROZEN -> State.FAILED]
[2025-07-11 14:40:27,997] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 61117) -> ('10.118.7.130', 61109)) State.FROZEN -> State.FAILED]
[2025-07-11 14:40:27,997] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 61118) -> ('10.118.7.130', 61109)) State.FROZEN -> State.FAILED]
[2025-07-11 14:40:27,997] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.208.220', 61119) -> ('10.118.7.130', 61109)) State.FROZEN -> State.FAILED]
[2025-07-11 14:40:27,997] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.26.128.1', 61116) -> ('169.254.43.7', 61111)) State.FROZEN -> State.FAILED]
[2025-07-11 14:40:27,997] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 61117) -> ('169.254.43.7', 61111)) State.FROZEN -> State.FAILED]
[2025-07-11 14:40:27,998] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 61118) -> ('169.254.43.7', 61111)) State.FROZEN -> State.FAILED]
[2025-07-11 14:40:27,998] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.208.220', 61119) -> ('169.254.43.7', 61111)) State.FROZEN -> State.FAILED]
[2025-07-11 14:40:27,998] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.26.128.1', 61116) -> ('169.254.208.220', 61113)) State.FROZEN -> State.FAILED]
[2025-07-11 14:40:27,998] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 61117) -> ('169.254.208.220', 61113)) State.FROZEN -> State.FAILED]
[2025-07-11 14:40:27,998] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 61118) -> ('169.254.208.220', 61113)) State.FROZEN -> State.FAILED]
[2025-07-11 14:40:27,998] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.208.220', 61119) -> ('169.254.208.220', 61113)) State.FROZEN -> State.FAILED]
[2025-07-11 14:40:27,998] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 61115) -> ('58.248.180.197', 34159)) State.WAITING -> State.FAILED]
[2025-07-11 14:40:27,998] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.26.128.1', 61116) -> ('58.248.180.197', 34159)) State.FROZEN -> State.FAILED]
[2025-07-11 14:40:27,999] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 61117) -> ('58.248.180.197', 34159)) State.FROZEN -> State.FAILED]
[2025-07-11 14:40:27,999] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 61118) -> ('58.248.180.197', 34159)) State.FROZEN -> State.FAILED]
[2025-07-11 14:40:27,999] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.208.220', 61119) -> ('58.248.180.197', 34159)) State.FROZEN -> State.FAILED]
[2025-07-11 14:40:27,999] [ice.py[line:1114]] [INFO] [Connection(2) ICE completed]
[2025-07-11 14:41:04,391] [run_local.py[line:223]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������.]
[2025-07-11 14:41:05,290] [server.py[line:264]] [INFO] [connection closed]
[2025-07-11 14:41:05,290] [run_local.py[line:265]] [INFO] [��start_websocket_server�����������б�ȡ����]
[2025-07-11 14:41:05,291] [run_local.py[line:268]] [INFO] [��start_websocket_server�����ڹرշ�����...]
[2025-07-11 14:41:05,291] [server.py[line:759]] [INFO] [server closing]
[2025-07-11 14:41:05,291] [server.py[line:793]] [INFO] [server closed]
[2025-07-11 14:41:05,291] [run_local.py[line:271]] [INFO] [��start_websocket_server���������ѳɹ��رա�]
[2025-07-11 14:41:15,307] [run_local.py[line:279]] [INFO] [
��⵽�û��жϣ����������ڹرա�]
[2025-07-11 14:43:20,790] [server.py[line:707]] [INFO] [server listening on 127.0.0.1:8765]
[2025-07-11 14:43:20,791] [run_local.py[line:248]] [INFO] [��start_websocket_server��WebSocket���������� ws://127.0.0.1:8765 ����]
[2025-07-11 14:43:20,791] [run_local.py[line:249]] [INFO] [��start_websocket_server���ȴ�������... (���300����û�����ӽ��Զ��ر�)]
[2025-07-11 14:43:27,430] [server.py[line:642]] [INFO] [connection open]
[2025-07-11 14:43:27,431] [run_local.py[line:241]] [INFO] [��start_websocket_server�����յ���һ���ͻ������ӣ����������������С�]
[2025-07-11 14:43:27,431] [run_local.py[line:166]] [INFO] [��websocket_handler��WebSocket client ��ʼ��������: v3720p]
[2025-07-11 14:43:27,432] [run_local.py[line:178]] [INFO] [��websocket_handler���Ѿ�������ʽ����: v3720p]
[2025-07-11 14:43:27,432] [run_local.py[line:193]] [INFO] [��websocket_handler����ʽ���� fa3ba59424e711f091782228ff08ca3c ��ʼ��ʽ����~ ���~~.]
[2025-07-11 14:43:27,433] [run_local.py[line:257]] [INFO] [��start_websocket_server�����������ڽ����������С�]
[2025-07-11 14:43:43,851] [ice.py[line:1114]] [INFO] [Connection(0) Could not bind to 169.254.123.208 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 14:43:43,852] [ice.py[line:1114]] [INFO] [Connection(0) Could not bind to 169.254.26.119 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 14:43:48,872] [web_log.py[line:211]] [INFO] [127.0.0.1 [11/Jul/2025:14:43:43 +0800] "POST /offer HTTP/1.1" 200 3513 "-" "Python/3.8 aiohttp/3.10.11"]
[2025-07-11 14:43:48,873] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 54609) -> ('2.2.2.106', 63211)) State.FROZEN -> State.WAITING]
[2025-07-11 14:43:48,873] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 54610) -> ('2.2.2.106', 63211)) State.FROZEN -> State.WAITING]
[2025-07-11 14:43:48,874] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 54611) -> ('2.2.2.106', 63211)) State.FROZEN -> State.WAITING]
[2025-07-11 14:43:48,874] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 54612) -> ('2.2.2.106', 63211)) State.FROZEN -> State.WAITING]
[2025-07-11 14:43:48,874] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 54613) -> ('2.2.2.106', 63211)) State.FROZEN -> State.WAITING]
[2025-07-11 14:43:48,874] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 54609) -> ('172.26.128.1', 63213)) State.FROZEN -> State.WAITING]
[2025-07-11 14:43:48,875] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 54609) -> ('2.2.2.106', 63211)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 14:43:48,877] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 54609) -> ('2.2.2.106', 63211)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-11 14:43:48,877] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 54609) -> ('10.118.7.130', 63215)) State.FROZEN -> State.WAITING]
[2025-07-11 14:43:48,877] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 54609) -> ('169.254.43.7', 63217)) State.FROZEN -> State.WAITING]
[2025-07-11 14:43:48,878] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 54609) -> ('169.254.208.220', 63219)) State.FROZEN -> State.WAITING]
[2025-07-11 14:43:48,878] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 54609) -> ('58.248.180.197', 36066)) State.FROZEN -> State.WAITING]
[2025-07-11 14:43:48,880] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 54610) -> ('2.2.2.106', 63211)) State.WAITING -> State.FAILED]
[2025-07-11 14:43:48,880] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 54611) -> ('2.2.2.106', 63211)) State.WAITING -> State.FAILED]
[2025-07-11 14:43:48,880] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 54612) -> ('2.2.2.106', 63211)) State.WAITING -> State.FAILED]
[2025-07-11 14:43:48,880] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 54613) -> ('2.2.2.106', 63211)) State.WAITING -> State.FAILED]
[2025-07-11 14:43:48,880] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 54609) -> ('172.26.128.1', 63213)) State.WAITING -> State.FAILED]
[2025-07-11 14:43:48,882] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 54610) -> ('172.26.128.1', 63213)) State.FROZEN -> State.FAILED]
[2025-07-11 14:43:48,882] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 54611) -> ('172.26.128.1', 63213)) State.FROZEN -> State.FAILED]
[2025-07-11 14:43:48,882] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 54612) -> ('172.26.128.1', 63213)) State.FROZEN -> State.FAILED]
[2025-07-11 14:43:48,882] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 54613) -> ('172.26.128.1', 63213)) State.FROZEN -> State.FAILED]
[2025-07-11 14:43:48,882] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 54609) -> ('10.118.7.130', 63215)) State.WAITING -> State.FAILED]
[2025-07-11 14:43:48,883] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 54610) -> ('10.118.7.130', 63215)) State.FROZEN -> State.FAILED]
[2025-07-11 14:43:48,883] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 54611) -> ('10.118.7.130', 63215)) State.FROZEN -> State.FAILED]
[2025-07-11 14:43:48,883] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 54612) -> ('10.118.7.130', 63215)) State.FROZEN -> State.FAILED]
[2025-07-11 14:43:48,883] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 54613) -> ('10.118.7.130', 63215)) State.FROZEN -> State.FAILED]
[2025-07-11 14:43:48,883] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 54609) -> ('169.254.43.7', 63217)) State.WAITING -> State.FAILED]
[2025-07-11 14:43:48,883] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 54610) -> ('169.254.43.7', 63217)) State.FROZEN -> State.FAILED]
[2025-07-11 14:43:48,884] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 54611) -> ('169.254.43.7', 63217)) State.FROZEN -> State.FAILED]
[2025-07-11 14:43:48,884] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 54612) -> ('169.254.43.7', 63217)) State.FROZEN -> State.FAILED]
[2025-07-11 14:43:48,884] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 54613) -> ('169.254.43.7', 63217)) State.FROZEN -> State.FAILED]
[2025-07-11 14:43:48,884] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 54609) -> ('169.254.208.220', 63219)) State.WAITING -> State.FAILED]
[2025-07-11 14:43:48,884] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 54610) -> ('169.254.208.220', 63219)) State.FROZEN -> State.FAILED]
[2025-07-11 14:43:48,884] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 54611) -> ('169.254.208.220', 63219)) State.FROZEN -> State.FAILED]
[2025-07-11 14:43:48,885] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 54612) -> ('169.254.208.220', 63219)) State.FROZEN -> State.FAILED]
[2025-07-11 14:43:48,885] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 54613) -> ('169.254.208.220', 63219)) State.FROZEN -> State.FAILED]
[2025-07-11 14:43:48,885] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 54609) -> ('58.248.180.197', 36066)) State.WAITING -> State.FAILED]
[2025-07-11 14:43:48,885] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 54610) -> ('58.248.180.197', 36066)) State.FROZEN -> State.FAILED]
[2025-07-11 14:43:48,885] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 54611) -> ('58.248.180.197', 36066)) State.FROZEN -> State.FAILED]
[2025-07-11 14:43:48,885] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 54612) -> ('58.248.180.197', 36066)) State.FROZEN -> State.FAILED]
[2025-07-11 14:43:48,886] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 54613) -> ('58.248.180.197', 36066)) State.FROZEN -> State.FAILED]
[2025-07-11 14:43:48,886] [ice.py[line:1114]] [INFO] [Connection(0) ICE completed]
[2025-07-11 14:44:14,336] [ice.py[line:1114]] [INFO] [Connection(2) Could not bind to 169.254.123.208 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 14:44:14,337] [ice.py[line:1114]] [INFO] [Connection(2) Could not bind to 169.254.26.119 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 14:44:19,348] [web_log.py[line:211]] [INFO] [127.0.0.1 [11/Jul/2025:14:44:14 +0800] "POST /offer HTTP/1.1" 200 3513 "-" "Python/3.8 aiohttp/3.10.11"]
[2025-07-11 14:44:19,349] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 49677) -> ('2.2.2.106', 53017)) State.FROZEN -> State.WAITING]
[2025-07-11 14:44:19,350] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.26.128.1', 49678) -> ('2.2.2.106', 53017)) State.FROZEN -> State.WAITING]
[2025-07-11 14:44:19,350] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 49679) -> ('2.2.2.106', 53017)) State.FROZEN -> State.WAITING]
[2025-07-11 14:44:19,350] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 49680) -> ('2.2.2.106', 53017)) State.FROZEN -> State.WAITING]
[2025-07-11 14:44:19,350] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.208.220', 49681) -> ('2.2.2.106', 53017)) State.FROZEN -> State.WAITING]
[2025-07-11 14:44:19,350] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 49677) -> ('172.26.128.1', 53019)) State.FROZEN -> State.WAITING]
[2025-07-11 14:44:19,351] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 49677) -> ('2.2.2.106', 53017)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 14:44:19,352] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 49677) -> ('2.2.2.106', 53017)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-11 14:44:19,352] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 49677) -> ('10.118.7.130', 53021)) State.FROZEN -> State.WAITING]
[2025-07-11 14:44:19,352] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 49677) -> ('169.254.43.7', 53023)) State.FROZEN -> State.WAITING]
[2025-07-11 14:44:19,353] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 49677) -> ('169.254.208.220', 53025)) State.FROZEN -> State.WAITING]
[2025-07-11 14:44:19,353] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 49677) -> ('58.248.180.197', 36294)) State.FROZEN -> State.WAITING]
[2025-07-11 14:44:19,355] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.26.128.1', 49678) -> ('2.2.2.106', 53017)) State.WAITING -> State.FAILED]
[2025-07-11 14:44:19,356] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 49679) -> ('2.2.2.106', 53017)) State.WAITING -> State.FAILED]
[2025-07-11 14:44:19,356] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 49680) -> ('2.2.2.106', 53017)) State.WAITING -> State.FAILED]
[2025-07-11 14:44:19,356] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.208.220', 49681) -> ('2.2.2.106', 53017)) State.WAITING -> State.FAILED]
[2025-07-11 14:44:19,356] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 49677) -> ('172.26.128.1', 53019)) State.WAITING -> State.FAILED]
[2025-07-11 14:44:19,356] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.26.128.1', 49678) -> ('172.26.128.1', 53019)) State.FROZEN -> State.FAILED]
[2025-07-11 14:44:19,356] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 49679) -> ('172.26.128.1', 53019)) State.FROZEN -> State.FAILED]
[2025-07-11 14:44:19,357] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 49680) -> ('172.26.128.1', 53019)) State.FROZEN -> State.FAILED]
[2025-07-11 14:44:19,357] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.208.220', 49681) -> ('172.26.128.1', 53019)) State.FROZEN -> State.FAILED]
[2025-07-11 14:44:19,357] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 49677) -> ('10.118.7.130', 53021)) State.WAITING -> State.FAILED]
[2025-07-11 14:44:19,357] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.26.128.1', 49678) -> ('10.118.7.130', 53021)) State.FROZEN -> State.FAILED]
[2025-07-11 14:44:19,357] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 49679) -> ('10.118.7.130', 53021)) State.FROZEN -> State.FAILED]
[2025-07-11 14:44:19,357] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 49680) -> ('10.118.7.130', 53021)) State.FROZEN -> State.FAILED]
[2025-07-11 14:44:19,358] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.208.220', 49681) -> ('10.118.7.130', 53021)) State.FROZEN -> State.FAILED]
[2025-07-11 14:44:19,358] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 49677) -> ('169.254.43.7', 53023)) State.WAITING -> State.FAILED]
[2025-07-11 14:44:19,358] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.26.128.1', 49678) -> ('169.254.43.7', 53023)) State.FROZEN -> State.FAILED]
[2025-07-11 14:44:19,358] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 49679) -> ('169.254.43.7', 53023)) State.FROZEN -> State.FAILED]
[2025-07-11 14:44:19,358] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 49680) -> ('169.254.43.7', 53023)) State.FROZEN -> State.FAILED]
[2025-07-11 14:44:19,358] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.208.220', 49681) -> ('169.254.43.7', 53023)) State.FROZEN -> State.FAILED]
[2025-07-11 14:44:19,358] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 49677) -> ('169.254.208.220', 53025)) State.WAITING -> State.FAILED]
[2025-07-11 14:44:19,358] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.26.128.1', 49678) -> ('169.254.208.220', 53025)) State.FROZEN -> State.FAILED]
[2025-07-11 14:44:19,359] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 49679) -> ('169.254.208.220', 53025)) State.FROZEN -> State.FAILED]
[2025-07-11 14:44:19,359] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 49680) -> ('169.254.208.220', 53025)) State.FROZEN -> State.FAILED]
[2025-07-11 14:44:19,359] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.208.220', 49681) -> ('169.254.208.220', 53025)) State.FROZEN -> State.FAILED]
[2025-07-11 14:44:19,359] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 49677) -> ('58.248.180.197', 36294)) State.WAITING -> State.FAILED]
[2025-07-11 14:44:19,359] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.26.128.1', 49678) -> ('58.248.180.197', 36294)) State.FROZEN -> State.FAILED]
[2025-07-11 14:44:19,359] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 49679) -> ('58.248.180.197', 36294)) State.FROZEN -> State.FAILED]
[2025-07-11 14:44:19,360] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 49680) -> ('58.248.180.197', 36294)) State.FROZEN -> State.FAILED]
[2025-07-11 14:44:19,360] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.208.220', 49681) -> ('58.248.180.197', 36294)) State.FROZEN -> State.FAILED]
[2025-07-11 14:44:19,360] [ice.py[line:1114]] [INFO] [Connection(2) ICE completed]
[2025-07-11 14:44:53,053] [run_local.py[line:223]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������.]
[2025-07-11 15:14:42,932] [server.py[line:707]] [INFO] [server listening on 127.0.0.1:8765]
[2025-07-11 15:14:42,933] [run_local.py[line:248]] [INFO] [��start_websocket_server��WebSocket���������� ws://127.0.0.1:8765 ����]
[2025-07-11 15:14:42,933] [run_local.py[line:249]] [INFO] [��start_websocket_server���ȴ�������... (���300����û�����ӽ��Զ��ر�)]
[2025-07-11 15:14:43,593] [ice.py[line:1114]] [INFO] [Connection(0) Could not bind to 169.254.123.208 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 15:14:43,594] [ice.py[line:1114]] [INFO] [Connection(0) Could not bind to 169.254.26.119 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 15:14:46,434] [server.py[line:642]] [INFO] [connection open]
[2025-07-11 15:14:46,435] [run_local.py[line:241]] [INFO] [��start_websocket_server�����յ���һ���ͻ������ӣ����������������С�]
[2025-07-11 15:14:46,435] [run_local.py[line:166]] [INFO] [��websocket_handler��WebSocket client ��ʼ��������: v3720p]
[2025-07-11 15:14:46,436] [run_local.py[line:178]] [INFO] [��websocket_handler���Ѿ�������ʽ����: v3720p]
[2025-07-11 15:14:46,437] [run_local.py[line:193]] [INFO] [��websocket_handler����ʽ���� fa3ba59424e711f091782228ff08ca3c ��ʼ��ʽ����~ ���~~.]
[2025-07-11 15:14:46,437] [run_local.py[line:257]] [INFO] [��start_websocket_server�����������ڽ����������С�]
[2025-07-11 15:14:48,617] [web_log.py[line:211]] [INFO] [127.0.0.1 [11/Jul/2025:15:14:43 +0800] "POST /offer HTTP/1.1" 200 3513 "-" "Python/3.10 aiohttp/3.9.5"]
[2025-07-11 15:14:48,618] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 50162) -> ('2.2.2.106', 52030)) State.FROZEN -> State.WAITING]
[2025-07-11 15:14:48,618] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 50163) -> ('2.2.2.106', 52030)) State.FROZEN -> State.WAITING]
[2025-07-11 15:14:48,618] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 50164) -> ('2.2.2.106', 52030)) State.FROZEN -> State.WAITING]
[2025-07-11 15:14:48,618] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 50165) -> ('2.2.2.106', 52030)) State.FROZEN -> State.WAITING]
[2025-07-11 15:14:48,619] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 50166) -> ('2.2.2.106', 52030)) State.FROZEN -> State.WAITING]
[2025-07-11 15:14:48,619] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 50162) -> ('172.26.128.1', 52032)) State.FROZEN -> State.WAITING]
[2025-07-11 15:14:48,620] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 50162) -> ('2.2.2.106', 52030)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 15:14:48,621] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 50162) -> ('2.2.2.106', 52030)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-11 15:14:48,621] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 50162) -> ('10.118.7.130', 52034)) State.FROZEN -> State.WAITING]
[2025-07-11 15:14:48,621] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 50162) -> ('169.254.43.7', 52036)) State.FROZEN -> State.WAITING]
[2025-07-11 15:14:48,622] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 50162) -> ('169.254.208.220', 52038)) State.FROZEN -> State.WAITING]
[2025-07-11 15:14:48,622] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 50162) -> ('58.248.180.197', 32114)) State.FROZEN -> State.WAITING]
[2025-07-11 15:14:48,626] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 50163) -> ('2.2.2.106', 52030)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 15:14:48,626] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 50164) -> ('2.2.2.106', 52030)) State.WAITING -> State.FAILED]
[2025-07-11 15:14:48,627] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 50165) -> ('2.2.2.106', 52030)) State.WAITING -> State.FAILED]
[2025-07-11 15:14:48,627] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 50166) -> ('2.2.2.106', 52030)) State.WAITING -> State.FAILED]
[2025-07-11 15:14:48,627] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 50162) -> ('172.26.128.1', 52032)) State.WAITING -> State.FAILED]
[2025-07-11 15:14:48,627] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 50163) -> ('172.26.128.1', 52032)) State.FROZEN -> State.FAILED]
[2025-07-11 15:14:48,628] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 50164) -> ('172.26.128.1', 52032)) State.FROZEN -> State.FAILED]
[2025-07-11 15:14:48,628] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 50165) -> ('172.26.128.1', 52032)) State.FROZEN -> State.FAILED]
[2025-07-11 15:14:48,628] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 50166) -> ('172.26.128.1', 52032)) State.FROZEN -> State.FAILED]
[2025-07-11 15:14:48,628] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 50162) -> ('10.118.7.130', 52034)) State.WAITING -> State.FAILED]
[2025-07-11 15:14:48,628] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 50163) -> ('10.118.7.130', 52034)) State.FROZEN -> State.FAILED]
[2025-07-11 15:14:48,629] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 50164) -> ('10.118.7.130', 52034)) State.FROZEN -> State.FAILED]
[2025-07-11 15:14:48,629] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 50165) -> ('10.118.7.130', 52034)) State.FROZEN -> State.FAILED]
[2025-07-11 15:14:48,629] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 50166) -> ('10.118.7.130', 52034)) State.FROZEN -> State.FAILED]
[2025-07-11 15:14:48,629] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 50162) -> ('169.254.43.7', 52036)) State.WAITING -> State.FAILED]
[2025-07-11 15:14:48,629] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 50163) -> ('169.254.43.7', 52036)) State.FROZEN -> State.FAILED]
[2025-07-11 15:14:48,629] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 50164) -> ('169.254.43.7', 52036)) State.FROZEN -> State.FAILED]
[2025-07-11 15:14:48,630] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 50165) -> ('169.254.43.7', 52036)) State.FROZEN -> State.FAILED]
[2025-07-11 15:14:48,630] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 50166) -> ('169.254.43.7', 52036)) State.FROZEN -> State.FAILED]
[2025-07-11 15:14:48,630] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 50162) -> ('169.254.208.220', 52038)) State.WAITING -> State.FAILED]
[2025-07-11 15:14:48,630] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 50163) -> ('169.254.208.220', 52038)) State.FROZEN -> State.FAILED]
[2025-07-11 15:14:48,631] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 50164) -> ('169.254.208.220', 52038)) State.FROZEN -> State.FAILED]
[2025-07-11 15:14:48,631] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 50165) -> ('169.254.208.220', 52038)) State.FROZEN -> State.FAILED]
[2025-07-11 15:14:48,631] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 50166) -> ('169.254.208.220', 52038)) State.FROZEN -> State.FAILED]
[2025-07-11 15:14:48,631] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 50162) -> ('58.248.180.197', 32114)) State.WAITING -> State.FAILED]
[2025-07-11 15:14:48,631] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 50163) -> ('58.248.180.197', 32114)) State.FROZEN -> State.FAILED]
[2025-07-11 15:14:48,632] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 50164) -> ('58.248.180.197', 32114)) State.FROZEN -> State.FAILED]
[2025-07-11 15:14:48,632] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 50165) -> ('58.248.180.197', 32114)) State.FROZEN -> State.FAILED]
[2025-07-11 15:14:48,632] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 50166) -> ('58.248.180.197', 32114)) State.FROZEN -> State.FAILED]
[2025-07-11 15:14:48,632] [ice.py[line:1114]] [INFO] [Connection(0) ICE completed]
[2025-07-11 15:15:12,627] [ice.py[line:1114]] [INFO] [Connection(2) Could not bind to 169.254.123.208 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 15:15:12,628] [ice.py[line:1114]] [INFO] [Connection(2) Could not bind to 169.254.26.119 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 15:15:17,611] [web_log.py[line:211]] [INFO] [127.0.0.1 [11/Jul/2025:15:15:12 +0800] "POST /offer HTTP/1.1" 200 3511 "-" "Python/3.10 aiohttp/3.9.5"]
[2025-07-11 15:15:17,612] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 50761) -> ('2.2.2.106', 55440)) State.FROZEN -> State.WAITING]
[2025-07-11 15:15:17,612] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.26.128.1', 50762) -> ('2.2.2.106', 55440)) State.FROZEN -> State.WAITING]
[2025-07-11 15:15:17,613] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 50763) -> ('2.2.2.106', 55440)) State.FROZEN -> State.WAITING]
[2025-07-11 15:15:17,613] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 50764) -> ('2.2.2.106', 55440)) State.FROZEN -> State.WAITING]
[2025-07-11 15:15:17,613] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.208.220', 50765) -> ('2.2.2.106', 55440)) State.FROZEN -> State.WAITING]
[2025-07-11 15:15:17,613] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 50761) -> ('172.26.128.1', 55442)) State.FROZEN -> State.WAITING]
[2025-07-11 15:15:17,615] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 50761) -> ('2.2.2.106', 55440)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 15:15:17,617] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 50761) -> ('2.2.2.106', 55440)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-11 15:15:17,617] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 50761) -> ('10.118.7.130', 55444)) State.FROZEN -> State.WAITING]
[2025-07-11 15:15:17,617] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 50761) -> ('169.254.43.7', 55446)) State.FROZEN -> State.WAITING]
[2025-07-11 15:15:17,618] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 50761) -> ('169.254.208.220', 55448)) State.FROZEN -> State.WAITING]
[2025-07-11 15:15:17,618] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 50761) -> ('58.248.180.197', 32444)) State.FROZEN -> State.WAITING]
[2025-07-11 15:15:17,622] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.26.128.1', 50762) -> ('2.2.2.106', 55440)) State.WAITING -> State.FAILED]
[2025-07-11 15:15:17,622] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 50763) -> ('2.2.2.106', 55440)) State.WAITING -> State.FAILED]
[2025-07-11 15:15:17,623] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 50764) -> ('2.2.2.106', 55440)) State.WAITING -> State.FAILED]
[2025-07-11 15:15:17,623] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.208.220', 50765) -> ('2.2.2.106', 55440)) State.WAITING -> State.FAILED]
[2025-07-11 15:15:17,623] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 50761) -> ('172.26.128.1', 55442)) State.WAITING -> State.FAILED]
[2025-07-11 15:15:17,623] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.26.128.1', 50762) -> ('172.26.128.1', 55442)) State.FROZEN -> State.FAILED]
[2025-07-11 15:15:17,623] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 50763) -> ('172.26.128.1', 55442)) State.FROZEN -> State.FAILED]
[2025-07-11 15:15:17,624] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 50764) -> ('172.26.128.1', 55442)) State.FROZEN -> State.FAILED]
[2025-07-11 15:15:17,624] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.208.220', 50765) -> ('172.26.128.1', 55442)) State.FROZEN -> State.FAILED]
[2025-07-11 15:15:17,624] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 50761) -> ('10.118.7.130', 55444)) State.WAITING -> State.FAILED]
[2025-07-11 15:15:17,624] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.26.128.1', 50762) -> ('10.118.7.130', 55444)) State.FROZEN -> State.FAILED]
[2025-07-11 15:15:17,624] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 50763) -> ('10.118.7.130', 55444)) State.FROZEN -> State.FAILED]
[2025-07-11 15:15:17,625] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 50764) -> ('10.118.7.130', 55444)) State.FROZEN -> State.FAILED]
[2025-07-11 15:15:17,625] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.208.220', 50765) -> ('10.118.7.130', 55444)) State.FROZEN -> State.FAILED]
[2025-07-11 15:15:17,625] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 50761) -> ('169.254.43.7', 55446)) State.WAITING -> State.FAILED]
[2025-07-11 15:15:17,625] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.26.128.1', 50762) -> ('169.254.43.7', 55446)) State.FROZEN -> State.FAILED]
[2025-07-11 15:15:17,625] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 50763) -> ('169.254.43.7', 55446)) State.FROZEN -> State.FAILED]
[2025-07-11 15:15:17,626] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 50764) -> ('169.254.43.7', 55446)) State.FROZEN -> State.FAILED]
[2025-07-11 15:15:17,626] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.208.220', 50765) -> ('169.254.43.7', 55446)) State.FROZEN -> State.FAILED]
[2025-07-11 15:15:17,626] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 50761) -> ('169.254.208.220', 55448)) State.WAITING -> State.FAILED]
[2025-07-11 15:15:17,626] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.26.128.1', 50762) -> ('169.254.208.220', 55448)) State.FROZEN -> State.FAILED]
[2025-07-11 15:15:17,627] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 50763) -> ('169.254.208.220', 55448)) State.FROZEN -> State.FAILED]
[2025-07-11 15:15:17,627] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 50764) -> ('169.254.208.220', 55448)) State.FROZEN -> State.FAILED]
[2025-07-11 15:15:17,627] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.208.220', 50765) -> ('169.254.208.220', 55448)) State.FROZEN -> State.FAILED]
[2025-07-11 15:15:17,627] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 50761) -> ('58.248.180.197', 32444)) State.WAITING -> State.FAILED]
[2025-07-11 15:15:17,627] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.26.128.1', 50762) -> ('58.248.180.197', 32444)) State.FROZEN -> State.FAILED]
[2025-07-11 15:15:17,627] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 50763) -> ('58.248.180.197', 32444)) State.FROZEN -> State.FAILED]
[2025-07-11 15:15:17,627] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 50764) -> ('58.248.180.197', 32444)) State.FROZEN -> State.FAILED]
[2025-07-11 15:15:17,627] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.208.220', 50765) -> ('58.248.180.197', 32444)) State.FROZEN -> State.FAILED]
[2025-07-11 15:15:17,628] [ice.py[line:1114]] [INFO] [Connection(2) ICE completed]
[2025-07-11 15:15:51,719] [ice.py[line:1114]] [INFO] [Connection(4) Could not bind to 169.254.123.208 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 15:15:51,720] [ice.py[line:1114]] [INFO] [Connection(4) Could not bind to 169.254.26.119 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 15:15:56,723] [web_log.py[line:211]] [INFO] [127.0.0.1 [11/Jul/2025:15:15:51 +0800] "POST /offer HTTP/1.1" 200 3513 "-" "Python/3.10 aiohttp/3.9.5"]
[2025-07-11 15:15:56,724] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('2.2.2.106', 58516) -> ('2.2.2.106', 58505)) State.FROZEN -> State.WAITING]
[2025-07-11 15:15:56,725] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('172.26.128.1', 58517) -> ('2.2.2.106', 58505)) State.FROZEN -> State.WAITING]
[2025-07-11 15:15:56,725] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('10.118.7.130', 58518) -> ('2.2.2.106', 58505)) State.FROZEN -> State.WAITING]
[2025-07-11 15:15:56,726] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('169.254.43.7', 58519) -> ('2.2.2.106', 58505)) State.FROZEN -> State.WAITING]
[2025-07-11 15:15:56,726] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('169.254.208.220', 58520) -> ('2.2.2.106', 58505)) State.FROZEN -> State.WAITING]
[2025-07-11 15:15:56,727] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('2.2.2.106', 58516) -> ('172.26.128.1', 58507)) State.FROZEN -> State.WAITING]
[2025-07-11 15:15:56,728] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('2.2.2.106', 58516) -> ('2.2.2.106', 58505)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 15:15:56,735] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('2.2.2.106', 58516) -> ('2.2.2.106', 58505)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-11 15:15:56,737] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('2.2.2.106', 58516) -> ('10.118.7.130', 58509)) State.FROZEN -> State.WAITING]
[2025-07-11 15:15:56,737] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('2.2.2.106', 58516) -> ('169.254.43.7', 58511)) State.FROZEN -> State.WAITING]
[2025-07-11 15:15:56,738] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('2.2.2.106', 58516) -> ('169.254.208.220', 58513)) State.FROZEN -> State.WAITING]
[2025-07-11 15:15:56,738] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('2.2.2.106', 58516) -> ('58.248.180.197', 32752)) State.FROZEN -> State.WAITING]
[2025-07-11 15:15:56,739] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('172.26.128.1', 58517) -> ('2.2.2.106', 58505)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 15:15:56,740] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('10.118.7.130', 58518) -> ('2.2.2.106', 58505)) State.WAITING -> State.FAILED]
[2025-07-11 15:15:56,740] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('169.254.43.7', 58519) -> ('2.2.2.106', 58505)) State.WAITING -> State.FAILED]
[2025-07-11 15:15:56,740] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('169.254.208.220', 58520) -> ('2.2.2.106', 58505)) State.WAITING -> State.FAILED]
[2025-07-11 15:15:56,741] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('2.2.2.106', 58516) -> ('172.26.128.1', 58507)) State.WAITING -> State.FAILED]
[2025-07-11 15:15:56,741] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('172.26.128.1', 58517) -> ('172.26.128.1', 58507)) State.FROZEN -> State.FAILED]
[2025-07-11 15:15:56,741] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('10.118.7.130', 58518) -> ('172.26.128.1', 58507)) State.FROZEN -> State.FAILED]
[2025-07-11 15:15:56,741] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('169.254.43.7', 58519) -> ('172.26.128.1', 58507)) State.FROZEN -> State.FAILED]
[2025-07-11 15:15:56,741] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('169.254.208.220', 58520) -> ('172.26.128.1', 58507)) State.FROZEN -> State.FAILED]
[2025-07-11 15:15:56,742] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('2.2.2.106', 58516) -> ('10.118.7.130', 58509)) State.WAITING -> State.FAILED]
[2025-07-11 15:15:56,742] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('172.26.128.1', 58517) -> ('10.118.7.130', 58509)) State.FROZEN -> State.FAILED]
[2025-07-11 15:15:56,742] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('10.118.7.130', 58518) -> ('10.118.7.130', 58509)) State.FROZEN -> State.FAILED]
[2025-07-11 15:15:56,742] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('169.254.43.7', 58519) -> ('10.118.7.130', 58509)) State.FROZEN -> State.FAILED]
[2025-07-11 15:15:56,743] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('169.254.208.220', 58520) -> ('10.118.7.130', 58509)) State.FROZEN -> State.FAILED]
[2025-07-11 15:15:56,743] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('2.2.2.106', 58516) -> ('169.254.43.7', 58511)) State.WAITING -> State.FAILED]
[2025-07-11 15:15:56,743] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('172.26.128.1', 58517) -> ('169.254.43.7', 58511)) State.FROZEN -> State.FAILED]
[2025-07-11 15:15:56,743] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('10.118.7.130', 58518) -> ('169.254.43.7', 58511)) State.FROZEN -> State.FAILED]
[2025-07-11 15:15:56,744] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('169.254.43.7', 58519) -> ('169.254.43.7', 58511)) State.FROZEN -> State.FAILED]
[2025-07-11 15:15:56,744] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('169.254.208.220', 58520) -> ('169.254.43.7', 58511)) State.FROZEN -> State.FAILED]
[2025-07-11 15:15:56,744] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('2.2.2.106', 58516) -> ('169.254.208.220', 58513)) State.WAITING -> State.FAILED]
[2025-07-11 15:15:56,744] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('172.26.128.1', 58517) -> ('169.254.208.220', 58513)) State.FROZEN -> State.FAILED]
[2025-07-11 15:15:56,745] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('10.118.7.130', 58518) -> ('169.254.208.220', 58513)) State.FROZEN -> State.FAILED]
[2025-07-11 15:15:56,745] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('169.254.43.7', 58519) -> ('169.254.208.220', 58513)) State.FROZEN -> State.FAILED]
[2025-07-11 15:15:56,745] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('169.254.208.220', 58520) -> ('169.254.208.220', 58513)) State.FROZEN -> State.FAILED]
[2025-07-11 15:15:56,745] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('2.2.2.106', 58516) -> ('58.248.180.197', 32752)) State.WAITING -> State.FAILED]
[2025-07-11 15:15:56,746] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('172.26.128.1', 58517) -> ('58.248.180.197', 32752)) State.FROZEN -> State.FAILED]
[2025-07-11 15:15:56,746] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('10.118.7.130', 58518) -> ('58.248.180.197', 32752)) State.FROZEN -> State.FAILED]
[2025-07-11 15:15:56,746] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('169.254.43.7', 58519) -> ('58.248.180.197', 32752)) State.FROZEN -> State.FAILED]
[2025-07-11 15:15:56,746] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('169.254.208.220', 58520) -> ('58.248.180.197', 32752)) State.FROZEN -> State.FAILED]
[2025-07-11 15:15:56,746] [ice.py[line:1114]] [INFO] [Connection(4) ICE completed]
[2025-07-11 15:16:46,089] [ice.py[line:1114]] [INFO] [Connection(4) Consent to send expired]
[2025-07-11 15:16:47,430] [ice.py[line:1114]] [INFO] [Connection(6) Could not bind to 169.254.123.208 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 15:16:47,431] [ice.py[line:1114]] [INFO] [Connection(6) Could not bind to 169.254.26.119 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 15:16:52,438] [web_log.py[line:211]] [INFO] [127.0.0.1 [11/Jul/2025:15:16:47 +0800] "POST /offer HTTP/1.1" 200 3513 "-" "Python/3.10 aiohttp/3.9.5"]
[2025-07-11 15:16:52,439] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('2.2.2.106', 60563) -> ('2.2.2.106', 63307)) State.FROZEN -> State.WAITING]
[2025-07-11 15:16:52,439] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('172.26.128.1', 60564) -> ('2.2.2.106', 63307)) State.FROZEN -> State.WAITING]
[2025-07-11 15:16:52,439] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('10.118.7.130', 60565) -> ('2.2.2.106', 63307)) State.FROZEN -> State.WAITING]
[2025-07-11 15:16:52,439] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('169.254.43.7', 60566) -> ('2.2.2.106', 63307)) State.FROZEN -> State.WAITING]
[2025-07-11 15:16:52,439] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('169.254.208.220', 60567) -> ('2.2.2.106', 63307)) State.FROZEN -> State.WAITING]
[2025-07-11 15:16:52,440] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('2.2.2.106', 60563) -> ('172.26.128.1', 63309)) State.FROZEN -> State.WAITING]
[2025-07-11 15:16:52,440] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('2.2.2.106', 60563) -> ('2.2.2.106', 63307)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 15:16:52,442] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('2.2.2.106', 60563) -> ('2.2.2.106', 63307)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-11 15:16:52,442] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('2.2.2.106', 60563) -> ('10.118.7.130', 63311)) State.FROZEN -> State.WAITING]
[2025-07-11 15:16:52,443] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('2.2.2.106', 60563) -> ('169.254.43.7', 63313)) State.FROZEN -> State.WAITING]
[2025-07-11 15:16:52,443] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('2.2.2.106', 60563) -> ('169.254.208.220', 63315)) State.FROZEN -> State.WAITING]
[2025-07-11 15:16:52,443] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('2.2.2.106', 60563) -> ('58.248.180.197', 33280)) State.FROZEN -> State.WAITING]
[2025-07-11 15:16:52,447] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('172.26.128.1', 60564) -> ('2.2.2.106', 63307)) State.WAITING -> State.FAILED]
[2025-07-11 15:16:52,447] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('10.118.7.130', 60565) -> ('2.2.2.106', 63307)) State.WAITING -> State.FAILED]
[2025-07-11 15:16:52,448] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('169.254.43.7', 60566) -> ('2.2.2.106', 63307)) State.WAITING -> State.FAILED]
[2025-07-11 15:16:52,448] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('169.254.208.220', 60567) -> ('2.2.2.106', 63307)) State.WAITING -> State.FAILED]
[2025-07-11 15:16:52,448] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('2.2.2.106', 60563) -> ('172.26.128.1', 63309)) State.WAITING -> State.FAILED]
[2025-07-11 15:16:52,448] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('172.26.128.1', 60564) -> ('172.26.128.1', 63309)) State.FROZEN -> State.FAILED]
[2025-07-11 15:16:52,448] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('10.118.7.130', 60565) -> ('172.26.128.1', 63309)) State.FROZEN -> State.FAILED]
[2025-07-11 15:16:52,449] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('169.254.43.7', 60566) -> ('172.26.128.1', 63309)) State.FROZEN -> State.FAILED]
[2025-07-11 15:16:52,449] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('169.254.208.220', 60567) -> ('172.26.128.1', 63309)) State.FROZEN -> State.FAILED]
[2025-07-11 15:16:52,449] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('2.2.2.106', 60563) -> ('10.118.7.130', 63311)) State.WAITING -> State.FAILED]
[2025-07-11 15:16:52,449] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('172.26.128.1', 60564) -> ('10.118.7.130', 63311)) State.FROZEN -> State.FAILED]
[2025-07-11 15:16:52,449] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('10.118.7.130', 60565) -> ('10.118.7.130', 63311)) State.FROZEN -> State.FAILED]
[2025-07-11 15:16:52,450] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('169.254.43.7', 60566) -> ('10.118.7.130', 63311)) State.FROZEN -> State.FAILED]
[2025-07-11 15:16:52,450] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('169.254.208.220', 60567) -> ('10.118.7.130', 63311)) State.FROZEN -> State.FAILED]
[2025-07-11 15:16:52,450] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('2.2.2.106', 60563) -> ('169.254.43.7', 63313)) State.WAITING -> State.FAILED]
[2025-07-11 15:16:52,450] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('172.26.128.1', 60564) -> ('169.254.43.7', 63313)) State.FROZEN -> State.FAILED]
[2025-07-11 15:16:52,451] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('10.118.7.130', 60565) -> ('169.254.43.7', 63313)) State.FROZEN -> State.FAILED]
[2025-07-11 15:16:52,451] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('169.254.43.7', 60566) -> ('169.254.43.7', 63313)) State.FROZEN -> State.FAILED]
[2025-07-11 15:16:52,451] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('169.254.208.220', 60567) -> ('169.254.43.7', 63313)) State.FROZEN -> State.FAILED]
[2025-07-11 15:16:52,451] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('2.2.2.106', 60563) -> ('169.254.208.220', 63315)) State.WAITING -> State.FAILED]
[2025-07-11 15:16:52,451] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('172.26.128.1', 60564) -> ('169.254.208.220', 63315)) State.FROZEN -> State.FAILED]
[2025-07-11 15:16:52,451] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('10.118.7.130', 60565) -> ('169.254.208.220', 63315)) State.FROZEN -> State.FAILED]
[2025-07-11 15:16:52,452] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('169.254.43.7', 60566) -> ('169.254.208.220', 63315)) State.FROZEN -> State.FAILED]
[2025-07-11 15:16:52,452] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('169.254.208.220', 60567) -> ('169.254.208.220', 63315)) State.FROZEN -> State.FAILED]
[2025-07-11 15:16:52,452] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('2.2.2.106', 60563) -> ('58.248.180.197', 33280)) State.WAITING -> State.FAILED]
[2025-07-11 15:16:52,452] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('172.26.128.1', 60564) -> ('58.248.180.197', 33280)) State.FROZEN -> State.FAILED]
[2025-07-11 15:16:52,452] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('10.118.7.130', 60565) -> ('58.248.180.197', 33280)) State.FROZEN -> State.FAILED]
[2025-07-11 15:16:52,452] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('169.254.43.7', 60566) -> ('58.248.180.197', 33280)) State.FROZEN -> State.FAILED]
[2025-07-11 15:16:52,453] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('169.254.208.220', 60567) -> ('58.248.180.197', 33280)) State.FROZEN -> State.FAILED]
[2025-07-11 15:16:52,453] [ice.py[line:1114]] [INFO] [Connection(6) ICE completed]
[2025-07-11 15:17:24,816] [ice.py[line:1114]] [INFO] [Connection(8) Could not bind to 169.254.123.208 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 15:17:24,816] [ice.py[line:1114]] [INFO] [Connection(8) Could not bind to 169.254.26.119 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 15:17:29,830] [web_log.py[line:211]] [INFO] [127.0.0.1 [11/Jul/2025:15:17:24 +0800] "POST /offer HTTP/1.1" 200 3513 "-" "Python/3.10 aiohttp/3.9.5"]
[2025-07-11 15:17:29,831] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('2.2.2.106', 61299) -> ('2.2.2.106', 54553)) State.FROZEN -> State.WAITING]
[2025-07-11 15:17:29,831] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('172.26.128.1', 61300) -> ('2.2.2.106', 54553)) State.FROZEN -> State.WAITING]
[2025-07-11 15:17:29,832] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('10.118.7.130', 61301) -> ('2.2.2.106', 54553)) State.FROZEN -> State.WAITING]
[2025-07-11 15:17:29,832] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('169.254.43.7', 61302) -> ('2.2.2.106', 54553)) State.FROZEN -> State.WAITING]
[2025-07-11 15:17:29,832] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('169.254.208.220', 61303) -> ('2.2.2.106', 54553)) State.FROZEN -> State.WAITING]
[2025-07-11 15:17:29,833] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('2.2.2.106', 61299) -> ('172.26.128.1', 54555)) State.FROZEN -> State.WAITING]
[2025-07-11 15:17:29,834] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('2.2.2.106', 61299) -> ('2.2.2.106', 54553)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 15:17:29,837] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('2.2.2.106', 61299) -> ('2.2.2.106', 54553)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-11 15:17:29,837] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('2.2.2.106', 61299) -> ('10.118.7.130', 54557)) State.FROZEN -> State.WAITING]
[2025-07-11 15:17:29,837] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('2.2.2.106', 61299) -> ('169.254.43.7', 54559)) State.FROZEN -> State.WAITING]
[2025-07-11 15:17:29,837] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('2.2.2.106', 61299) -> ('169.254.208.220', 54561)) State.FROZEN -> State.WAITING]
[2025-07-11 15:17:29,838] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('2.2.2.106', 61299) -> ('58.248.180.197', 33740)) State.FROZEN -> State.WAITING]
[2025-07-11 15:17:29,839] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('172.26.128.1', 61300) -> ('2.2.2.106', 54553)) State.WAITING -> State.FAILED]
[2025-07-11 15:17:29,839] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('10.118.7.130', 61301) -> ('2.2.2.106', 54553)) State.WAITING -> State.FAILED]
[2025-07-11 15:17:29,839] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('169.254.43.7', 61302) -> ('2.2.2.106', 54553)) State.WAITING -> State.FAILED]
[2025-07-11 15:17:29,840] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('169.254.208.220', 61303) -> ('2.2.2.106', 54553)) State.WAITING -> State.FAILED]
[2025-07-11 15:17:29,840] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('2.2.2.106', 61299) -> ('172.26.128.1', 54555)) State.WAITING -> State.FAILED]
[2025-07-11 15:17:29,840] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('172.26.128.1', 61300) -> ('172.26.128.1', 54555)) State.FROZEN -> State.FAILED]
[2025-07-11 15:17:29,840] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('10.118.7.130', 61301) -> ('172.26.128.1', 54555)) State.FROZEN -> State.FAILED]
[2025-07-11 15:17:29,841] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('169.254.43.7', 61302) -> ('172.26.128.1', 54555)) State.FROZEN -> State.FAILED]
[2025-07-11 15:17:29,841] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('169.254.208.220', 61303) -> ('172.26.128.1', 54555)) State.FROZEN -> State.FAILED]
[2025-07-11 15:17:29,841] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('2.2.2.106', 61299) -> ('10.118.7.130', 54557)) State.WAITING -> State.FAILED]
[2025-07-11 15:17:29,841] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('172.26.128.1', 61300) -> ('10.118.7.130', 54557)) State.FROZEN -> State.FAILED]
[2025-07-11 15:17:29,842] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('10.118.7.130', 61301) -> ('10.118.7.130', 54557)) State.FROZEN -> State.FAILED]
[2025-07-11 15:17:29,842] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('169.254.43.7', 61302) -> ('10.118.7.130', 54557)) State.FROZEN -> State.FAILED]
[2025-07-11 15:17:29,842] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('169.254.208.220', 61303) -> ('10.118.7.130', 54557)) State.FROZEN -> State.FAILED]
[2025-07-11 15:17:29,842] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('2.2.2.106', 61299) -> ('169.254.43.7', 54559)) State.WAITING -> State.FAILED]
[2025-07-11 15:17:29,842] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('172.26.128.1', 61300) -> ('169.254.43.7', 54559)) State.FROZEN -> State.FAILED]
[2025-07-11 15:17:29,842] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('10.118.7.130', 61301) -> ('169.254.43.7', 54559)) State.FROZEN -> State.FAILED]
[2025-07-11 15:17:29,842] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('169.254.43.7', 61302) -> ('169.254.43.7', 54559)) State.FROZEN -> State.FAILED]
[2025-07-11 15:17:29,843] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('169.254.208.220', 61303) -> ('169.254.43.7', 54559)) State.FROZEN -> State.FAILED]
[2025-07-11 15:17:29,843] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('2.2.2.106', 61299) -> ('169.254.208.220', 54561)) State.WAITING -> State.FAILED]
[2025-07-11 15:17:29,844] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('172.26.128.1', 61300) -> ('169.254.208.220', 54561)) State.FROZEN -> State.FAILED]
[2025-07-11 15:17:29,844] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('10.118.7.130', 61301) -> ('169.254.208.220', 54561)) State.FROZEN -> State.FAILED]
[2025-07-11 15:17:29,844] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('169.254.43.7', 61302) -> ('169.254.208.220', 54561)) State.FROZEN -> State.FAILED]
[2025-07-11 15:17:29,844] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('169.254.208.220', 61303) -> ('169.254.208.220', 54561)) State.FROZEN -> State.FAILED]
[2025-07-11 15:17:29,844] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('2.2.2.106', 61299) -> ('58.248.180.197', 33740)) State.WAITING -> State.FAILED]
[2025-07-11 15:17:29,844] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('172.26.128.1', 61300) -> ('58.248.180.197', 33740)) State.FROZEN -> State.FAILED]
[2025-07-11 15:17:29,845] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('10.118.7.130', 61301) -> ('58.248.180.197', 33740)) State.FROZEN -> State.FAILED]
[2025-07-11 15:17:29,845] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('169.254.43.7', 61302) -> ('58.248.180.197', 33740)) State.FROZEN -> State.FAILED]
[2025-07-11 15:17:29,845] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('169.254.208.220', 61303) -> ('58.248.180.197', 33740)) State.FROZEN -> State.FAILED]
[2025-07-11 15:17:29,845] [ice.py[line:1114]] [INFO] [Connection(8) ICE completed]
[2025-07-11 15:18:06,733] [run_local.py[line:216]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������: received 1011 (internal error) keepalive ping timeout; then sent 1011 (internal error) keepalive ping timeout]
[2025-07-11 15:18:06,734] [run_local.py[line:223]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������.]
[2025-07-11 15:18:06,792] [run_local.py[line:225]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������.]
[2025-07-11 15:18:06,793] [server.py[line:264]] [INFO] [connection closed]
[2025-07-11 15:18:06,968] [run_local.py[line:265]] [INFO] [��start_websocket_server�����������б�ȡ����]
[2025-07-11 15:18:06,968] [run_local.py[line:268]] [INFO] [��start_websocket_server�����ڹرշ�����...]
[2025-07-11 15:18:06,968] [server.py[line:759]] [INFO] [server closing]
[2025-07-11 15:18:06,968] [server.py[line:793]] [INFO] [server closed]
[2025-07-11 15:18:06,969] [run_local.py[line:271]] [INFO] [��start_websocket_server���������ѳɹ��رա�]
[2025-07-11 15:18:06,970] [run_local.py[line:279]] [INFO] [
��⵽�û��жϣ����������ڹرա�]
[2025-07-11 16:11:25,956] [server.py[line:707]] [INFO] [server listening on 127.0.0.1:8765]
[2025-07-11 16:11:25,956] [run_local.py[line:248]] [INFO] [��start_websocket_server��WebSocket���������� ws://127.0.0.1:8765 ����]
[2025-07-11 16:11:25,956] [run_local.py[line:249]] [INFO] [��start_websocket_server���ȴ�������... (���300����û�����ӽ��Զ��ر�)]
[2025-07-11 16:11:26,235] [server.py[line:642]] [INFO] [connection open]
[2025-07-11 16:11:26,236] [run_local.py[line:241]] [INFO] [��start_websocket_server�����յ���һ���ͻ������ӣ����������������С�]
[2025-07-11 16:11:26,236] [run_local.py[line:166]] [INFO] [��websocket_handler��WebSocket client ��ʼ��������: v3720p]
[2025-07-11 16:11:26,236] [run_local.py[line:178]] [INFO] [��websocket_handler���Ѿ�������ʽ����: v3720p]
[2025-07-11 16:11:26,237] [run_local.py[line:193]] [INFO] [��websocket_handler����ʽ���� fa3ba59424e711f091782228ff08ca3c ��ʼ��ʽ����~ ���~~.]
[2025-07-11 16:11:26,237] [run_local.py[line:257]] [INFO] [��start_websocket_server�����������ڽ����������С�]
[2025-07-11 16:11:42,380] [ice.py[line:1114]] [INFO] [Connection(0) Could not bind to 169.254.123.208 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 16:11:42,381] [ice.py[line:1114]] [INFO] [Connection(0) Could not bind to 169.254.26.119 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 16:11:47,362] [web_log.py[line:211]] [INFO] [127.0.0.1 [11/Jul/2025:16:11:42 +0800] "POST /offer HTTP/1.1" 200 3513 "-" "Python/3.10 aiohttp/3.9.5"]
[2025-07-11 16:11:47,364] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 50171) -> ('2.2.2.106', 50162)) State.FROZEN -> State.WAITING]
[2025-07-11 16:11:47,364] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 50172) -> ('2.2.2.106', 50162)) State.FROZEN -> State.WAITING]
[2025-07-11 16:11:47,364] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 50173) -> ('2.2.2.106', 50162)) State.FROZEN -> State.WAITING]
[2025-07-11 16:11:47,364] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 50174) -> ('2.2.2.106', 50162)) State.FROZEN -> State.WAITING]
[2025-07-11 16:11:47,365] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 50175) -> ('2.2.2.106', 50162)) State.FROZEN -> State.WAITING]
[2025-07-11 16:11:47,365] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 50171) -> ('172.26.128.1', 50164)) State.FROZEN -> State.WAITING]
[2025-07-11 16:11:47,366] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 50171) -> ('2.2.2.106', 50162)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 16:11:47,368] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 50171) -> ('2.2.2.106', 50162)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-11 16:11:47,369] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 50171) -> ('10.118.7.130', 50166)) State.FROZEN -> State.WAITING]
[2025-07-11 16:11:47,369] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 50171) -> ('169.254.43.7', 50168)) State.FROZEN -> State.WAITING]
[2025-07-11 16:11:47,369] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 50171) -> ('169.254.208.220', 50170)) State.FROZEN -> State.WAITING]
[2025-07-11 16:11:47,370] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 50171) -> ('58.248.180.197', 27154)) State.FROZEN -> State.WAITING]
[2025-07-11 16:11:47,371] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 50172) -> ('2.2.2.106', 50162)) State.WAITING -> State.FAILED]
[2025-07-11 16:11:47,372] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 50173) -> ('2.2.2.106', 50162)) State.WAITING -> State.FAILED]
[2025-07-11 16:11:47,372] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 50174) -> ('2.2.2.106', 50162)) State.WAITING -> State.FAILED]
[2025-07-11 16:11:47,372] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 50175) -> ('2.2.2.106', 50162)) State.WAITING -> State.FAILED]
[2025-07-11 16:11:47,372] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 50171) -> ('172.26.128.1', 50164)) State.WAITING -> State.FAILED]
[2025-07-11 16:11:47,373] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 50172) -> ('172.26.128.1', 50164)) State.FROZEN -> State.FAILED]
[2025-07-11 16:11:47,373] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 50173) -> ('172.26.128.1', 50164)) State.FROZEN -> State.FAILED]
[2025-07-11 16:11:47,373] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 50174) -> ('172.26.128.1', 50164)) State.FROZEN -> State.FAILED]
[2025-07-11 16:11:47,373] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 50175) -> ('172.26.128.1', 50164)) State.FROZEN -> State.FAILED]
[2025-07-11 16:11:47,374] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 50171) -> ('10.118.7.130', 50166)) State.WAITING -> State.FAILED]
[2025-07-11 16:11:47,374] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 50172) -> ('10.118.7.130', 50166)) State.FROZEN -> State.FAILED]
[2025-07-11 16:11:47,374] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 50173) -> ('10.118.7.130', 50166)) State.FROZEN -> State.FAILED]
[2025-07-11 16:11:47,374] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 50174) -> ('10.118.7.130', 50166)) State.FROZEN -> State.FAILED]
[2025-07-11 16:11:47,375] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 50175) -> ('10.118.7.130', 50166)) State.FROZEN -> State.FAILED]
[2025-07-11 16:11:47,375] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 50171) -> ('169.254.43.7', 50168)) State.WAITING -> State.FAILED]
[2025-07-11 16:11:47,375] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 50172) -> ('169.254.43.7', 50168)) State.FROZEN -> State.FAILED]
[2025-07-11 16:11:47,375] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 50173) -> ('169.254.43.7', 50168)) State.FROZEN -> State.FAILED]
[2025-07-11 16:11:47,376] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 50174) -> ('169.254.43.7', 50168)) State.FROZEN -> State.FAILED]
[2025-07-11 16:11:47,376] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 50175) -> ('169.254.43.7', 50168)) State.FROZEN -> State.FAILED]
[2025-07-11 16:11:47,376] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 50171) -> ('169.254.208.220', 50170)) State.WAITING -> State.FAILED]
[2025-07-11 16:11:47,376] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 50172) -> ('169.254.208.220', 50170)) State.FROZEN -> State.FAILED]
[2025-07-11 16:11:47,376] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 50173) -> ('169.254.208.220', 50170)) State.FROZEN -> State.FAILED]
[2025-07-11 16:11:47,377] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 50174) -> ('169.254.208.220', 50170)) State.FROZEN -> State.FAILED]
[2025-07-11 16:11:47,377] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 50175) -> ('169.254.208.220', 50170)) State.FROZEN -> State.FAILED]
[2025-07-11 16:11:47,377] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 50171) -> ('58.248.180.197', 27154)) State.WAITING -> State.FAILED]
[2025-07-11 16:11:47,378] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 50172) -> ('58.248.180.197', 27154)) State.FROZEN -> State.FAILED]
[2025-07-11 16:11:47,378] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 50173) -> ('58.248.180.197', 27154)) State.FROZEN -> State.FAILED]
[2025-07-11 16:11:47,378] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 50174) -> ('58.248.180.197', 27154)) State.FROZEN -> State.FAILED]
[2025-07-11 16:11:47,378] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 50175) -> ('58.248.180.197', 27154)) State.FROZEN -> State.FAILED]
[2025-07-11 16:11:47,379] [ice.py[line:1114]] [INFO] [Connection(0) ICE completed]
[2025-07-11 16:12:22,264] [ice.py[line:1114]] [INFO] [Connection(0) Consent to send expired]
[2025-07-11 16:12:35,498] [ice.py[line:1114]] [INFO] [Connection(2) Could not bind to 169.254.123.208 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 16:12:35,499] [ice.py[line:1114]] [INFO] [Connection(2) Could not bind to 169.254.26.119 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 16:12:40,495] [web_log.py[line:211]] [INFO] [127.0.0.1 [11/Jul/2025:16:12:35 +0800] "POST /offer HTTP/1.1" 200 3511 "-" "Python/3.10 aiohttp/3.9.5"]
[2025-07-11 16:12:40,496] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 54855) -> ('2.2.2.106', 55880)) State.FROZEN -> State.WAITING]
[2025-07-11 16:12:40,496] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.26.128.1', 54856) -> ('2.2.2.106', 55880)) State.FROZEN -> State.WAITING]
[2025-07-11 16:12:40,497] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 54857) -> ('2.2.2.106', 55880)) State.FROZEN -> State.WAITING]
[2025-07-11 16:12:40,497] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 54858) -> ('2.2.2.106', 55880)) State.FROZEN -> State.WAITING]
[2025-07-11 16:12:40,497] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.208.220', 54859) -> ('2.2.2.106', 55880)) State.FROZEN -> State.WAITING]
[2025-07-11 16:12:40,497] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 54855) -> ('172.26.128.1', 55882)) State.FROZEN -> State.WAITING]
[2025-07-11 16:12:40,499] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 54855) -> ('2.2.2.106', 55880)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 16:12:40,504] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 54855) -> ('2.2.2.106', 55880)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-11 16:12:40,504] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 54855) -> ('10.118.7.130', 55884)) State.FROZEN -> State.WAITING]
[2025-07-11 16:12:40,505] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 54855) -> ('169.254.43.7', 55886)) State.FROZEN -> State.WAITING]
[2025-07-11 16:12:40,505] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 54855) -> ('169.254.208.220', 55888)) State.FROZEN -> State.WAITING]
[2025-07-11 16:12:40,505] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 54855) -> ('58.248.180.197', 27636)) State.FROZEN -> State.WAITING]
[2025-07-11 16:12:40,506] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.26.128.1', 54856) -> ('2.2.2.106', 55880)) State.WAITING -> State.FAILED]
[2025-07-11 16:12:40,507] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 54857) -> ('2.2.2.106', 55880)) State.WAITING -> State.FAILED]
[2025-07-11 16:12:40,507] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 54858) -> ('2.2.2.106', 55880)) State.WAITING -> State.FAILED]
[2025-07-11 16:12:40,507] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.208.220', 54859) -> ('2.2.2.106', 55880)) State.WAITING -> State.FAILED]
[2025-07-11 16:12:40,508] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 54855) -> ('172.26.128.1', 55882)) State.WAITING -> State.FAILED]
[2025-07-11 16:12:40,508] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.26.128.1', 54856) -> ('172.26.128.1', 55882)) State.FROZEN -> State.FAILED]
[2025-07-11 16:12:40,508] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 54857) -> ('172.26.128.1', 55882)) State.FROZEN -> State.FAILED]
[2025-07-11 16:12:40,508] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 54858) -> ('172.26.128.1', 55882)) State.FROZEN -> State.FAILED]
[2025-07-11 16:12:40,508] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.208.220', 54859) -> ('172.26.128.1', 55882)) State.FROZEN -> State.FAILED]
[2025-07-11 16:12:40,509] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 54855) -> ('10.118.7.130', 55884)) State.WAITING -> State.FAILED]
[2025-07-11 16:12:40,509] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.26.128.1', 54856) -> ('10.118.7.130', 55884)) State.FROZEN -> State.FAILED]
[2025-07-11 16:12:40,509] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 54857) -> ('10.118.7.130', 55884)) State.FROZEN -> State.FAILED]
[2025-07-11 16:12:40,510] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 54858) -> ('10.118.7.130', 55884)) State.FROZEN -> State.FAILED]
[2025-07-11 16:12:40,510] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.208.220', 54859) -> ('10.118.7.130', 55884)) State.FROZEN -> State.FAILED]
[2025-07-11 16:12:40,510] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 54855) -> ('169.254.43.7', 55886)) State.WAITING -> State.FAILED]
[2025-07-11 16:12:40,510] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.26.128.1', 54856) -> ('169.254.43.7', 55886)) State.FROZEN -> State.FAILED]
[2025-07-11 16:12:40,511] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 54857) -> ('169.254.43.7', 55886)) State.FROZEN -> State.FAILED]
[2025-07-11 16:12:40,511] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 54858) -> ('169.254.43.7', 55886)) State.FROZEN -> State.FAILED]
[2025-07-11 16:12:40,511] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.208.220', 54859) -> ('169.254.43.7', 55886)) State.FROZEN -> State.FAILED]
[2025-07-11 16:12:40,512] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 54855) -> ('169.254.208.220', 55888)) State.WAITING -> State.FAILED]
[2025-07-11 16:12:40,512] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.26.128.1', 54856) -> ('169.254.208.220', 55888)) State.FROZEN -> State.FAILED]
[2025-07-11 16:12:40,512] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 54857) -> ('169.254.208.220', 55888)) State.FROZEN -> State.FAILED]
[2025-07-11 16:12:40,512] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 54858) -> ('169.254.208.220', 55888)) State.FROZEN -> State.FAILED]
[2025-07-11 16:12:40,512] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.208.220', 54859) -> ('169.254.208.220', 55888)) State.FROZEN -> State.FAILED]
[2025-07-11 16:12:40,512] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 54855) -> ('58.248.180.197', 27636)) State.WAITING -> State.FAILED]
[2025-07-11 16:12:40,513] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.26.128.1', 54856) -> ('58.248.180.197', 27636)) State.FROZEN -> State.FAILED]
[2025-07-11 16:12:40,513] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 54857) -> ('58.248.180.197', 27636)) State.FROZEN -> State.FAILED]
[2025-07-11 16:12:40,513] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 54858) -> ('58.248.180.197', 27636)) State.FROZEN -> State.FAILED]
[2025-07-11 16:12:40,513] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.208.220', 54859) -> ('58.248.180.197', 27636)) State.FROZEN -> State.FAILED]
[2025-07-11 16:12:40,513] [ice.py[line:1114]] [INFO] [Connection(2) ICE completed]
[2025-07-11 16:12:55,255] [server.py[line:642]] [INFO] [connection open]
[2025-07-11 16:12:55,255] [run_local.py[line:166]] [INFO] [��websocket_handler��WebSocket client ��ʼ��������: v3720p]
[2025-07-11 16:12:55,255] [run_local.py[line:178]] [INFO] [��websocket_handler���Ѿ�������ʽ����: v3720p]
[2025-07-11 16:12:55,257] [run_local.py[line:193]] [INFO] [��websocket_handler����ʽ���� fa3ba59424e711f091782228ff08ca3c ��ʼ��ʽ����~ ���~~.]
[2025-07-11 16:13:00,255] [run_local.py[line:216]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������: sent 1011 (internal error) keepalive ping timeout; no close frame received]
[2025-07-11 16:13:00,256] [run_local.py[line:223]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������.]
[2025-07-11 16:13:00,256] [run_local.py[line:225]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������.]
[2025-07-11 16:13:00,256] [server.py[line:264]] [INFO] [connection closed]
[2025-07-11 16:13:49,848] [ice.py[line:1114]] [INFO] [Connection(4) Could not bind to 169.254.123.208 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 16:13:49,849] [ice.py[line:1114]] [INFO] [Connection(4) Could not bind to 169.254.26.119 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 16:13:54,862] [web_log.py[line:211]] [INFO] [127.0.0.1 [11/Jul/2025:16:13:49 +0800] "POST /offer HTTP/1.1" 200 3513 "-" "Python/3.10 aiohttp/3.9.5"]
[2025-07-11 16:13:54,862] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('2.2.2.106', 62726) -> ('2.2.2.106', 62716)) State.FROZEN -> State.WAITING]
[2025-07-11 16:13:54,863] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('172.26.128.1', 62727) -> ('2.2.2.106', 62716)) State.FROZEN -> State.WAITING]
[2025-07-11 16:13:54,863] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('10.118.7.130', 62728) -> ('2.2.2.106', 62716)) State.FROZEN -> State.WAITING]
[2025-07-11 16:13:54,863] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('169.254.43.7', 62729) -> ('2.2.2.106', 62716)) State.FROZEN -> State.WAITING]
[2025-07-11 16:13:54,863] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('169.254.208.220', 62730) -> ('2.2.2.106', 62716)) State.FROZEN -> State.WAITING]
[2025-07-11 16:13:54,864] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('2.2.2.106', 62726) -> ('172.26.128.1', 62718)) State.FROZEN -> State.WAITING]
[2025-07-11 16:13:54,865] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('2.2.2.106', 62726) -> ('2.2.2.106', 62716)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 16:13:54,867] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('2.2.2.106', 62726) -> ('2.2.2.106', 62716)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-11 16:13:54,867] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('2.2.2.106', 62726) -> ('10.118.7.130', 62720)) State.FROZEN -> State.WAITING]
[2025-07-11 16:13:54,867] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('2.2.2.106', 62726) -> ('169.254.43.7', 62722)) State.FROZEN -> State.WAITING]
[2025-07-11 16:13:54,868] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('2.2.2.106', 62726) -> ('169.254.208.220', 62724)) State.FROZEN -> State.WAITING]
[2025-07-11 16:13:54,868] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('2.2.2.106', 62726) -> ('58.248.180.197', 28446)) State.FROZEN -> State.WAITING]
[2025-07-11 16:13:54,871] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('172.26.128.1', 62727) -> ('2.2.2.106', 62716)) State.WAITING -> State.FAILED]
[2025-07-11 16:13:54,871] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('10.118.7.130', 62728) -> ('2.2.2.106', 62716)) State.WAITING -> State.FAILED]
[2025-07-11 16:13:54,872] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('169.254.43.7', 62729) -> ('2.2.2.106', 62716)) State.WAITING -> State.FAILED]
[2025-07-11 16:13:54,872] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('169.254.208.220', 62730) -> ('2.2.2.106', 62716)) State.WAITING -> State.FAILED]
[2025-07-11 16:13:54,872] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('2.2.2.106', 62726) -> ('172.26.128.1', 62718)) State.WAITING -> State.FAILED]
[2025-07-11 16:13:54,873] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('172.26.128.1', 62727) -> ('172.26.128.1', 62718)) State.FROZEN -> State.FAILED]
[2025-07-11 16:13:54,873] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('10.118.7.130', 62728) -> ('172.26.128.1', 62718)) State.FROZEN -> State.FAILED]
[2025-07-11 16:13:54,873] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('169.254.43.7', 62729) -> ('172.26.128.1', 62718)) State.FROZEN -> State.FAILED]
[2025-07-11 16:13:54,873] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('169.254.208.220', 62730) -> ('172.26.128.1', 62718)) State.FROZEN -> State.FAILED]
[2025-07-11 16:13:54,873] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('2.2.2.106', 62726) -> ('10.118.7.130', 62720)) State.WAITING -> State.FAILED]
[2025-07-11 16:13:54,874] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('172.26.128.1', 62727) -> ('10.118.7.130', 62720)) State.FROZEN -> State.FAILED]
[2025-07-11 16:13:54,874] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('10.118.7.130', 62728) -> ('10.118.7.130', 62720)) State.FROZEN -> State.FAILED]
[2025-07-11 16:13:54,874] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('169.254.43.7', 62729) -> ('10.118.7.130', 62720)) State.FROZEN -> State.FAILED]
[2025-07-11 16:13:54,874] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('169.254.208.220', 62730) -> ('10.118.7.130', 62720)) State.FROZEN -> State.FAILED]
[2025-07-11 16:13:54,875] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('2.2.2.106', 62726) -> ('169.254.43.7', 62722)) State.WAITING -> State.FAILED]
[2025-07-11 16:13:54,875] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('172.26.128.1', 62727) -> ('169.254.43.7', 62722)) State.FROZEN -> State.FAILED]
[2025-07-11 16:13:54,875] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('10.118.7.130', 62728) -> ('169.254.43.7', 62722)) State.FROZEN -> State.FAILED]
[2025-07-11 16:13:54,875] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('169.254.43.7', 62729) -> ('169.254.43.7', 62722)) State.FROZEN -> State.FAILED]
[2025-07-11 16:13:54,875] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('169.254.208.220', 62730) -> ('169.254.43.7', 62722)) State.FROZEN -> State.FAILED]
[2025-07-11 16:13:54,876] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('2.2.2.106', 62726) -> ('169.254.208.220', 62724)) State.WAITING -> State.FAILED]
[2025-07-11 16:13:54,876] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('172.26.128.1', 62727) -> ('169.254.208.220', 62724)) State.FROZEN -> State.FAILED]
[2025-07-11 16:13:54,876] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('10.118.7.130', 62728) -> ('169.254.208.220', 62724)) State.FROZEN -> State.FAILED]
[2025-07-11 16:13:54,876] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('169.254.43.7', 62729) -> ('169.254.208.220', 62724)) State.FROZEN -> State.FAILED]
[2025-07-11 16:13:54,877] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('169.254.208.220', 62730) -> ('169.254.208.220', 62724)) State.FROZEN -> State.FAILED]
[2025-07-11 16:13:54,877] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('2.2.2.106', 62726) -> ('58.248.180.197', 28446)) State.WAITING -> State.FAILED]
[2025-07-11 16:13:54,877] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('172.26.128.1', 62727) -> ('58.248.180.197', 28446)) State.FROZEN -> State.FAILED]
[2025-07-11 16:13:54,877] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('10.118.7.130', 62728) -> ('58.248.180.197', 28446)) State.FROZEN -> State.FAILED]
[2025-07-11 16:13:54,877] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('169.254.43.7', 62729) -> ('58.248.180.197', 28446)) State.FROZEN -> State.FAILED]
[2025-07-11 16:13:54,878] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('169.254.208.220', 62730) -> ('58.248.180.197', 28446)) State.FROZEN -> State.FAILED]
[2025-07-11 16:13:54,878] [ice.py[line:1114]] [INFO] [Connection(4) ICE completed]
[2025-07-11 16:14:08,793] [ice.py[line:1114]] [INFO] [Connection(2) Consent to send expired]
[2025-07-11 16:14:35,525] [run_local.py[line:223]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������.]
[2025-07-11 16:22:07,956] [server.py[line:264]] [INFO] [connection closed]
[2025-07-11 16:22:07,956] [run_local.py[line:265]] [INFO] [��start_websocket_server�����������б�ȡ����]
[2025-07-11 16:22:07,957] [run_local.py[line:268]] [INFO] [��start_websocket_server�����ڹرշ�����...]
[2025-07-11 16:22:07,957] [server.py[line:759]] [INFO] [server closing]
[2025-07-11 16:22:07,958] [server.py[line:793]] [INFO] [server closed]
[2025-07-11 16:22:07,958] [run_local.py[line:271]] [INFO] [��start_websocket_server���������ѳɹ��رա�]
[2025-07-11 16:22:09,162] [run_local.py[line:279]] [INFO] [
��⵽�û��жϣ����������ڹرա�]
[2025-07-11 19:18:57,019] [server.py[line:707]] [INFO] [server listening on 127.0.0.1:8765]
[2025-07-11 19:18:57,020] [run_local.py[line:248]] [INFO] [��start_websocket_server��WebSocket���������� ws://127.0.0.1:8765 ����]
[2025-07-11 19:18:57,020] [run_local.py[line:249]] [INFO] [��start_websocket_server���ȴ�������... (���300����û�����ӽ��Զ��ر�)]
[2025-07-11 19:18:57,577] [ice.py[line:1114]] [INFO] [Connection(0) Could not bind to 169.254.123.208 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 19:18:57,578] [ice.py[line:1114]] [INFO] [Connection(0) Could not bind to 169.254.26.119 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 19:18:58,136] [server.py[line:642]] [INFO] [connection open]
[2025-07-11 19:18:58,136] [run_local.py[line:241]] [INFO] [��start_websocket_server�����յ���һ���ͻ������ӣ����������������С�]
[2025-07-11 19:18:58,136] [run_local.py[line:166]] [INFO] [��websocket_handler��WebSocket client ��ʼ��������: v3720p]
[2025-07-11 19:18:58,136] [run_local.py[line:178]] [INFO] [��websocket_handler���Ѿ�������ʽ����: v3720p]
[2025-07-11 19:18:58,137] [run_local.py[line:193]] [INFO] [��websocket_handler����ʽ���� fa3ba59424e711f091782228ff08ca3c ��ʼ��ʽ����~ ���~~.]
[2025-07-11 19:18:58,137] [run_local.py[line:257]] [INFO] [��start_websocket_server�����������ڽ����������С�]
[2025-07-11 19:19:02,600] [web_log.py[line:211]] [INFO] [127.0.0.1 [11/Jul/2025:19:18:57 +0800] "POST /offer HTTP/1.1" 200 3511 "-" "Python/3.10 aiohttp/3.9.5"]
[2025-07-11 19:19:02,600] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 53293) -> ('2.2.2.106', 53284)) State.FROZEN -> State.WAITING]
[2025-07-11 19:19:02,601] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 53294) -> ('2.2.2.106', 53284)) State.FROZEN -> State.WAITING]
[2025-07-11 19:19:02,601] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 53295) -> ('2.2.2.106', 53284)) State.FROZEN -> State.WAITING]
[2025-07-11 19:19:02,601] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 53296) -> ('2.2.2.106', 53284)) State.FROZEN -> State.WAITING]
[2025-07-11 19:19:02,601] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 53297) -> ('2.2.2.106', 53284)) State.FROZEN -> State.WAITING]
[2025-07-11 19:19:02,601] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 53293) -> ('172.26.128.1', 53286)) State.FROZEN -> State.WAITING]
[2025-07-11 19:19:02,602] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 53293) -> ('2.2.2.106', 53284)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 19:19:02,604] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 53293) -> ('2.2.2.106', 53284)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-11 19:19:02,604] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 53293) -> ('10.118.7.130', 53288)) State.FROZEN -> State.WAITING]
[2025-07-11 19:19:02,604] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 53293) -> ('169.254.43.7', 53290)) State.FROZEN -> State.WAITING]
[2025-07-11 19:19:02,604] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 53293) -> ('169.254.208.220', 53292)) State.FROZEN -> State.WAITING]
[2025-07-11 19:19:02,605] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 53293) -> ('58.248.180.197', 24598)) State.FROZEN -> State.WAITING]
[2025-07-11 19:19:02,607] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 53294) -> ('2.2.2.106', 53284)) State.WAITING -> State.FAILED]
[2025-07-11 19:19:02,608] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 53295) -> ('2.2.2.106', 53284)) State.WAITING -> State.FAILED]
[2025-07-11 19:19:02,608] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 53296) -> ('2.2.2.106', 53284)) State.WAITING -> State.FAILED]
[2025-07-11 19:19:02,608] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 53297) -> ('2.2.2.106', 53284)) State.WAITING -> State.FAILED]
[2025-07-11 19:19:02,608] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 53293) -> ('172.26.128.1', 53286)) State.WAITING -> State.FAILED]
[2025-07-11 19:19:02,608] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 53294) -> ('172.26.128.1', 53286)) State.FROZEN -> State.FAILED]
[2025-07-11 19:19:02,609] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 53295) -> ('172.26.128.1', 53286)) State.FROZEN -> State.FAILED]
[2025-07-11 19:19:02,609] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 53296) -> ('172.26.128.1', 53286)) State.FROZEN -> State.FAILED]
[2025-07-11 19:19:02,609] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 53297) -> ('172.26.128.1', 53286)) State.FROZEN -> State.FAILED]
[2025-07-11 19:19:02,609] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 53293) -> ('10.118.7.130', 53288)) State.WAITING -> State.FAILED]
[2025-07-11 19:19:02,609] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 53294) -> ('10.118.7.130', 53288)) State.FROZEN -> State.FAILED]
[2025-07-11 19:19:02,609] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 53295) -> ('10.118.7.130', 53288)) State.FROZEN -> State.FAILED]
[2025-07-11 19:19:02,609] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 53296) -> ('10.118.7.130', 53288)) State.FROZEN -> State.FAILED]
[2025-07-11 19:19:02,609] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 53297) -> ('10.118.7.130', 53288)) State.FROZEN -> State.FAILED]
[2025-07-11 19:19:02,609] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 53293) -> ('169.254.43.7', 53290)) State.WAITING -> State.FAILED]
[2025-07-11 19:19:02,611] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 53294) -> ('169.254.43.7', 53290)) State.FROZEN -> State.FAILED]
[2025-07-11 19:19:02,611] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 53295) -> ('169.254.43.7', 53290)) State.FROZEN -> State.FAILED]
[2025-07-11 19:19:02,611] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 53296) -> ('169.254.43.7', 53290)) State.FROZEN -> State.FAILED]
[2025-07-11 19:19:02,611] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 53297) -> ('169.254.43.7', 53290)) State.FROZEN -> State.FAILED]
[2025-07-11 19:19:02,612] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 53293) -> ('169.254.208.220', 53292)) State.WAITING -> State.FAILED]
[2025-07-11 19:19:02,612] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 53294) -> ('169.254.208.220', 53292)) State.FROZEN -> State.FAILED]
[2025-07-11 19:19:02,612] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 53295) -> ('169.254.208.220', 53292)) State.FROZEN -> State.FAILED]
[2025-07-11 19:19:02,612] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 53296) -> ('169.254.208.220', 53292)) State.FROZEN -> State.FAILED]
[2025-07-11 19:19:02,612] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 53297) -> ('169.254.208.220', 53292)) State.FROZEN -> State.FAILED]
[2025-07-11 19:19:02,612] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.106', 53293) -> ('58.248.180.197', 24598)) State.WAITING -> State.FAILED]
[2025-07-11 19:19:02,613] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.26.128.1', 53294) -> ('58.248.180.197', 24598)) State.FROZEN -> State.FAILED]
[2025-07-11 19:19:02,613] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 53295) -> ('58.248.180.197', 24598)) State.FROZEN -> State.FAILED]
[2025-07-11 19:19:02,613] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 53296) -> ('58.248.180.197', 24598)) State.FROZEN -> State.FAILED]
[2025-07-11 19:19:02,613] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 53297) -> ('58.248.180.197', 24598)) State.FROZEN -> State.FAILED]
[2025-07-11 19:19:02,614] [ice.py[line:1114]] [INFO] [Connection(0) ICE completed]
[2025-07-11 19:19:58,678] [ice.py[line:1114]] [INFO] [Connection(2) Could not bind to 169.254.123.208 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 19:19:58,679] [ice.py[line:1114]] [INFO] [Connection(2) Could not bind to 169.254.26.119 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 19:20:03,688] [web_log.py[line:211]] [INFO] [127.0.0.1 [11/Jul/2025:19:19:58 +0800] "POST /offer HTTP/1.1" 200 3510 "-" "Python/3.10 aiohttp/3.9.5"]
[2025-07-11 19:20:03,688] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 51139) -> ('2.2.2.106', 51129)) State.FROZEN -> State.WAITING]
[2025-07-11 19:20:03,689] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.26.128.1', 51140) -> ('2.2.2.106', 51129)) State.FROZEN -> State.WAITING]
[2025-07-11 19:20:03,689] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 51141) -> ('2.2.2.106', 51129)) State.FROZEN -> State.WAITING]
[2025-07-11 19:20:03,689] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 51142) -> ('2.2.2.106', 51129)) State.FROZEN -> State.WAITING]
[2025-07-11 19:20:03,690] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.208.220', 51143) -> ('2.2.2.106', 51129)) State.FROZEN -> State.WAITING]
[2025-07-11 19:20:03,690] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 51139) -> ('172.26.128.1', 51131)) State.FROZEN -> State.WAITING]
[2025-07-11 19:20:03,691] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 51139) -> ('2.2.2.106', 51129)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 19:20:03,693] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 51139) -> ('2.2.2.106', 51129)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-11 19:20:03,693] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 51139) -> ('10.118.7.130', 51133)) State.FROZEN -> State.WAITING]
[2025-07-11 19:20:03,693] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 51139) -> ('169.254.43.7', 51135)) State.FROZEN -> State.WAITING]
[2025-07-11 19:20:03,693] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 51139) -> ('169.254.208.220', 51137)) State.FROZEN -> State.WAITING]
[2025-07-11 19:20:03,693] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 51139) -> ('58.248.180.197', 24832)) State.FROZEN -> State.WAITING]
[2025-07-11 19:20:03,695] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.26.128.1', 51140) -> ('2.2.2.106', 51129)) State.WAITING -> State.FAILED]
[2025-07-11 19:20:03,697] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 51141) -> ('2.2.2.106', 51129)) State.WAITING -> State.FAILED]
[2025-07-11 19:20:03,697] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 51142) -> ('2.2.2.106', 51129)) State.WAITING -> State.FAILED]
[2025-07-11 19:20:03,697] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.208.220', 51143) -> ('2.2.2.106', 51129)) State.WAITING -> State.FAILED]
[2025-07-11 19:20:03,697] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 51139) -> ('172.26.128.1', 51131)) State.WAITING -> State.FAILED]
[2025-07-11 19:20:03,697] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.26.128.1', 51140) -> ('172.26.128.1', 51131)) State.FROZEN -> State.FAILED]
[2025-07-11 19:20:03,697] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 51141) -> ('172.26.128.1', 51131)) State.FROZEN -> State.FAILED]
[2025-07-11 19:20:03,697] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 51142) -> ('172.26.128.1', 51131)) State.FROZEN -> State.FAILED]
[2025-07-11 19:20:03,698] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.208.220', 51143) -> ('172.26.128.1', 51131)) State.FROZEN -> State.FAILED]
[2025-07-11 19:20:03,698] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 51139) -> ('10.118.7.130', 51133)) State.WAITING -> State.FAILED]
[2025-07-11 19:20:03,698] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.26.128.1', 51140) -> ('10.118.7.130', 51133)) State.FROZEN -> State.FAILED]
[2025-07-11 19:20:03,698] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 51141) -> ('10.118.7.130', 51133)) State.FROZEN -> State.FAILED]
[2025-07-11 19:20:03,698] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 51142) -> ('10.118.7.130', 51133)) State.FROZEN -> State.FAILED]
[2025-07-11 19:20:03,698] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.208.220', 51143) -> ('10.118.7.130', 51133)) State.FROZEN -> State.FAILED]
[2025-07-11 19:20:03,698] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 51139) -> ('169.254.43.7', 51135)) State.WAITING -> State.FAILED]
[2025-07-11 19:20:03,699] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.26.128.1', 51140) -> ('169.254.43.7', 51135)) State.FROZEN -> State.FAILED]
[2025-07-11 19:20:03,699] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 51141) -> ('169.254.43.7', 51135)) State.FROZEN -> State.FAILED]
[2025-07-11 19:20:03,699] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 51142) -> ('169.254.43.7', 51135)) State.FROZEN -> State.FAILED]
[2025-07-11 19:20:03,699] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.208.220', 51143) -> ('169.254.43.7', 51135)) State.FROZEN -> State.FAILED]
[2025-07-11 19:20:03,699] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 51139) -> ('169.254.208.220', 51137)) State.WAITING -> State.FAILED]
[2025-07-11 19:20:03,699] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.26.128.1', 51140) -> ('169.254.208.220', 51137)) State.FROZEN -> State.FAILED]
[2025-07-11 19:20:03,699] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 51141) -> ('169.254.208.220', 51137)) State.FROZEN -> State.FAILED]
[2025-07-11 19:20:03,700] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 51142) -> ('169.254.208.220', 51137)) State.FROZEN -> State.FAILED]
[2025-07-11 19:20:03,700] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.208.220', 51143) -> ('169.254.208.220', 51137)) State.FROZEN -> State.FAILED]
[2025-07-11 19:20:03,700] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('2.2.2.106', 51139) -> ('58.248.180.197', 24832)) State.WAITING -> State.FAILED]
[2025-07-11 19:20:03,700] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('172.26.128.1', 51140) -> ('58.248.180.197', 24832)) State.FROZEN -> State.FAILED]
[2025-07-11 19:20:03,700] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('10.118.7.130', 51141) -> ('58.248.180.197', 24832)) State.FROZEN -> State.FAILED]
[2025-07-11 19:20:03,700] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.43.7', 51142) -> ('58.248.180.197', 24832)) State.FROZEN -> State.FAILED]
[2025-07-11 19:20:03,700] [ice.py[line:1114]] [INFO] [Connection(2) Check CandidatePair(('169.254.208.220', 51143) -> ('58.248.180.197', 24832)) State.FROZEN -> State.FAILED]
[2025-07-11 19:20:03,701] [ice.py[line:1114]] [INFO] [Connection(2) ICE completed]
[2025-07-11 19:21:09,386] [ice.py[line:1114]] [INFO] [Connection(4) Could not bind to 169.254.123.208 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 19:21:09,387] [ice.py[line:1114]] [INFO] [Connection(4) Could not bind to 169.254.26.119 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 19:21:14,390] [web_log.py[line:211]] [INFO] [127.0.0.1 [11/Jul/2025:19:21:09 +0800] "POST /offer HTTP/1.1" 200 3512 "-" "Python/3.10 aiohttp/3.9.5"]
[2025-07-11 19:21:14,391] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('2.2.2.106', 53893) -> ('2.2.2.106', 64229)) State.FROZEN -> State.WAITING]
[2025-07-11 19:21:14,391] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('172.26.128.1', 53894) -> ('2.2.2.106', 64229)) State.FROZEN -> State.WAITING]
[2025-07-11 19:21:14,391] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('10.118.7.130', 53895) -> ('2.2.2.106', 64229)) State.FROZEN -> State.WAITING]
[2025-07-11 19:21:14,392] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('169.254.43.7', 53896) -> ('2.2.2.106', 64229)) State.FROZEN -> State.WAITING]
[2025-07-11 19:21:14,392] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('169.254.208.220', 53897) -> ('2.2.2.106', 64229)) State.FROZEN -> State.WAITING]
[2025-07-11 19:21:14,392] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('2.2.2.106', 53893) -> ('172.26.128.1', 64231)) State.FROZEN -> State.WAITING]
[2025-07-11 19:21:14,393] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('2.2.2.106', 53893) -> ('2.2.2.106', 64229)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 19:21:14,395] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('2.2.2.106', 53893) -> ('2.2.2.106', 64229)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-11 19:21:14,395] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('2.2.2.106', 53893) -> ('10.118.7.130', 64233)) State.FROZEN -> State.WAITING]
[2025-07-11 19:21:14,395] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('2.2.2.106', 53893) -> ('169.254.43.7', 64235)) State.FROZEN -> State.WAITING]
[2025-07-11 19:21:14,395] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('2.2.2.106', 53893) -> ('169.254.208.220', 64237)) State.FROZEN -> State.WAITING]
[2025-07-11 19:21:14,395] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('2.2.2.106', 53893) -> ('58.248.180.197', 25134)) State.FROZEN -> State.WAITING]
[2025-07-11 19:21:14,399] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('172.26.128.1', 53894) -> ('2.2.2.106', 64229)) State.WAITING -> State.FAILED]
[2025-07-11 19:21:14,399] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('10.118.7.130', 53895) -> ('2.2.2.106', 64229)) State.WAITING -> State.FAILED]
[2025-07-11 19:21:14,399] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('169.254.43.7', 53896) -> ('2.2.2.106', 64229)) State.WAITING -> State.FAILED]
[2025-07-11 19:21:14,399] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('169.254.208.220', 53897) -> ('2.2.2.106', 64229)) State.WAITING -> State.FAILED]
[2025-07-11 19:21:14,399] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('2.2.2.106', 53893) -> ('172.26.128.1', 64231)) State.WAITING -> State.FAILED]
[2025-07-11 19:21:14,400] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('172.26.128.1', 53894) -> ('172.26.128.1', 64231)) State.FROZEN -> State.FAILED]
[2025-07-11 19:21:14,400] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('10.118.7.130', 53895) -> ('172.26.128.1', 64231)) State.FROZEN -> State.FAILED]
[2025-07-11 19:21:14,400] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('169.254.43.7', 53896) -> ('172.26.128.1', 64231)) State.FROZEN -> State.FAILED]
[2025-07-11 19:21:14,400] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('169.254.208.220', 53897) -> ('172.26.128.1', 64231)) State.FROZEN -> State.FAILED]
[2025-07-11 19:21:14,400] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('2.2.2.106', 53893) -> ('10.118.7.130', 64233)) State.WAITING -> State.FAILED]
[2025-07-11 19:21:14,400] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('172.26.128.1', 53894) -> ('10.118.7.130', 64233)) State.FROZEN -> State.FAILED]
[2025-07-11 19:21:14,400] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('10.118.7.130', 53895) -> ('10.118.7.130', 64233)) State.FROZEN -> State.FAILED]
[2025-07-11 19:21:14,401] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('169.254.43.7', 53896) -> ('10.118.7.130', 64233)) State.FROZEN -> State.FAILED]
[2025-07-11 19:21:14,401] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('169.254.208.220', 53897) -> ('10.118.7.130', 64233)) State.FROZEN -> State.FAILED]
[2025-07-11 19:21:14,401] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('2.2.2.106', 53893) -> ('169.254.43.7', 64235)) State.WAITING -> State.FAILED]
[2025-07-11 19:21:14,401] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('172.26.128.1', 53894) -> ('169.254.43.7', 64235)) State.FROZEN -> State.FAILED]
[2025-07-11 19:21:14,401] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('10.118.7.130', 53895) -> ('169.254.43.7', 64235)) State.FROZEN -> State.FAILED]
[2025-07-11 19:21:14,403] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('169.254.43.7', 53896) -> ('169.254.43.7', 64235)) State.FROZEN -> State.FAILED]
[2025-07-11 19:21:14,403] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('169.254.208.220', 53897) -> ('169.254.43.7', 64235)) State.FROZEN -> State.FAILED]
[2025-07-11 19:21:14,403] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('2.2.2.106', 53893) -> ('169.254.208.220', 64237)) State.WAITING -> State.FAILED]
[2025-07-11 19:21:14,403] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('172.26.128.1', 53894) -> ('169.254.208.220', 64237)) State.FROZEN -> State.FAILED]
[2025-07-11 19:21:14,403] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('10.118.7.130', 53895) -> ('169.254.208.220', 64237)) State.FROZEN -> State.FAILED]
[2025-07-11 19:21:14,403] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('169.254.43.7', 53896) -> ('169.254.208.220', 64237)) State.FROZEN -> State.FAILED]
[2025-07-11 19:21:14,404] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('169.254.208.220', 53897) -> ('169.254.208.220', 64237)) State.FROZEN -> State.FAILED]
[2025-07-11 19:21:14,404] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('2.2.2.106', 53893) -> ('58.248.180.197', 25134)) State.WAITING -> State.FAILED]
[2025-07-11 19:21:14,404] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('172.26.128.1', 53894) -> ('58.248.180.197', 25134)) State.FROZEN -> State.FAILED]
[2025-07-11 19:21:14,404] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('10.118.7.130', 53895) -> ('58.248.180.197', 25134)) State.FROZEN -> State.FAILED]
[2025-07-11 19:21:14,404] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('169.254.43.7', 53896) -> ('58.248.180.197', 25134)) State.FROZEN -> State.FAILED]
[2025-07-11 19:21:14,405] [ice.py[line:1114]] [INFO] [Connection(4) Check CandidatePair(('169.254.208.220', 53897) -> ('58.248.180.197', 25134)) State.FROZEN -> State.FAILED]
[2025-07-11 19:21:14,405] [ice.py[line:1114]] [INFO] [Connection(4) ICE completed]
[2025-07-11 19:21:54,018] [ice.py[line:1114]] [INFO] [Connection(6) Could not bind to 169.254.123.208 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 19:21:54,019] [ice.py[line:1114]] [INFO] [Connection(6) Could not bind to 169.254.26.119 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 19:21:59,033] [web_log.py[line:211]] [INFO] [127.0.0.1 [11/Jul/2025:19:21:54 +0800] "POST /offer HTTP/1.1" 200 3513 "-" "Python/3.8 aiohttp/3.10.11"]
[2025-07-11 19:21:59,034] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('2.2.2.106', 49862) -> ('2.2.2.106', 49852)) State.FROZEN -> State.WAITING]
[2025-07-11 19:21:59,034] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('172.26.128.1', 49863) -> ('2.2.2.106', 49852)) State.FROZEN -> State.WAITING]
[2025-07-11 19:21:59,034] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('10.118.7.130', 49864) -> ('2.2.2.106', 49852)) State.FROZEN -> State.WAITING]
[2025-07-11 19:21:59,035] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('169.254.43.7', 49865) -> ('2.2.2.106', 49852)) State.FROZEN -> State.WAITING]
[2025-07-11 19:21:59,035] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('169.254.208.220', 49866) -> ('2.2.2.106', 49852)) State.FROZEN -> State.WAITING]
[2025-07-11 19:21:59,035] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('2.2.2.106', 49862) -> ('172.26.128.1', 49854)) State.FROZEN -> State.WAITING]
[2025-07-11 19:21:59,036] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('2.2.2.106', 49862) -> ('2.2.2.106', 49852)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 19:21:59,038] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('2.2.2.106', 49862) -> ('2.2.2.106', 49852)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-11 19:21:59,039] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('2.2.2.106', 49862) -> ('10.118.7.130', 49856)) State.FROZEN -> State.WAITING]
[2025-07-11 19:21:59,039] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('2.2.2.106', 49862) -> ('169.254.43.7', 49858)) State.FROZEN -> State.WAITING]
[2025-07-11 19:21:59,039] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('2.2.2.106', 49862) -> ('169.254.208.220', 49860)) State.FROZEN -> State.WAITING]
[2025-07-11 19:21:59,039] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('2.2.2.106', 49862) -> ('58.248.180.197', 25303)) State.FROZEN -> State.WAITING]
[2025-07-11 19:21:59,042] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('172.26.128.1', 49863) -> ('2.2.2.106', 49852)) State.WAITING -> State.FAILED]
[2025-07-11 19:21:59,042] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('10.118.7.130', 49864) -> ('2.2.2.106', 49852)) State.WAITING -> State.FAILED]
[2025-07-11 19:21:59,042] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('169.254.43.7', 49865) -> ('2.2.2.106', 49852)) State.WAITING -> State.FAILED]
[2025-07-11 19:21:59,042] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('169.254.208.220', 49866) -> ('2.2.2.106', 49852)) State.WAITING -> State.FAILED]
[2025-07-11 19:21:59,044] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('2.2.2.106', 49862) -> ('172.26.128.1', 49854)) State.WAITING -> State.FAILED]
[2025-07-11 19:21:59,044] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('172.26.128.1', 49863) -> ('172.26.128.1', 49854)) State.FROZEN -> State.FAILED]
[2025-07-11 19:21:59,044] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('10.118.7.130', 49864) -> ('172.26.128.1', 49854)) State.FROZEN -> State.FAILED]
[2025-07-11 19:21:59,044] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('169.254.43.7', 49865) -> ('172.26.128.1', 49854)) State.FROZEN -> State.FAILED]
[2025-07-11 19:21:59,044] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('169.254.208.220', 49866) -> ('172.26.128.1', 49854)) State.FROZEN -> State.FAILED]
[2025-07-11 19:21:59,045] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('2.2.2.106', 49862) -> ('10.118.7.130', 49856)) State.WAITING -> State.FAILED]
[2025-07-11 19:21:59,045] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('172.26.128.1', 49863) -> ('10.118.7.130', 49856)) State.FROZEN -> State.FAILED]
[2025-07-11 19:21:59,045] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('10.118.7.130', 49864) -> ('10.118.7.130', 49856)) State.FROZEN -> State.FAILED]
[2025-07-11 19:21:59,045] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('169.254.43.7', 49865) -> ('10.118.7.130', 49856)) State.FROZEN -> State.FAILED]
[2025-07-11 19:21:59,045] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('169.254.208.220', 49866) -> ('10.118.7.130', 49856)) State.FROZEN -> State.FAILED]
[2025-07-11 19:21:59,046] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('2.2.2.106', 49862) -> ('169.254.43.7', 49858)) State.WAITING -> State.FAILED]
[2025-07-11 19:21:59,046] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('172.26.128.1', 49863) -> ('169.254.43.7', 49858)) State.FROZEN -> State.FAILED]
[2025-07-11 19:21:59,046] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('10.118.7.130', 49864) -> ('169.254.43.7', 49858)) State.FROZEN -> State.FAILED]
[2025-07-11 19:21:59,046] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('169.254.43.7', 49865) -> ('169.254.43.7', 49858)) State.FROZEN -> State.FAILED]
[2025-07-11 19:21:59,046] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('169.254.208.220', 49866) -> ('169.254.43.7', 49858)) State.FROZEN -> State.FAILED]
[2025-07-11 19:21:59,046] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('2.2.2.106', 49862) -> ('169.254.208.220', 49860)) State.WAITING -> State.FAILED]
[2025-07-11 19:21:59,046] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('172.26.128.1', 49863) -> ('169.254.208.220', 49860)) State.FROZEN -> State.FAILED]
[2025-07-11 19:21:59,047] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('10.118.7.130', 49864) -> ('169.254.208.220', 49860)) State.FROZEN -> State.FAILED]
[2025-07-11 19:21:59,047] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('169.254.43.7', 49865) -> ('169.254.208.220', 49860)) State.FROZEN -> State.FAILED]
[2025-07-11 19:21:59,047] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('169.254.208.220', 49866) -> ('169.254.208.220', 49860)) State.FROZEN -> State.FAILED]
[2025-07-11 19:21:59,047] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('2.2.2.106', 49862) -> ('58.248.180.197', 25303)) State.WAITING -> State.FAILED]
[2025-07-11 19:21:59,047] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('172.26.128.1', 49863) -> ('58.248.180.197', 25303)) State.FROZEN -> State.FAILED]
[2025-07-11 19:21:59,049] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('10.118.7.130', 49864) -> ('58.248.180.197', 25303)) State.FROZEN -> State.FAILED]
[2025-07-11 19:21:59,049] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('169.254.43.7', 49865) -> ('58.248.180.197', 25303)) State.FROZEN -> State.FAILED]
[2025-07-11 19:21:59,049] [ice.py[line:1114]] [INFO] [Connection(6) Check CandidatePair(('169.254.208.220', 49866) -> ('58.248.180.197', 25303)) State.FROZEN -> State.FAILED]
[2025-07-11 19:21:59,049] [ice.py[line:1114]] [INFO] [Connection(6) ICE completed]
[2025-07-11 19:22:32,853] [ice.py[line:1114]] [INFO] [Connection(6) Consent to send expired]
[2025-07-11 19:23:06,787] [ice.py[line:1114]] [INFO] [Connection(8) Could not bind to 169.254.123.208 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 19:23:06,789] [ice.py[line:1114]] [INFO] [Connection(8) Could not bind to 169.254.26.119 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-11 19:23:11,785] [web_log.py[line:211]] [INFO] [127.0.0.1 [11/Jul/2025:19:23:06 +0800] "POST /offer HTTP/1.1" 200 3511 "-" "Python/3.8 aiohttp/3.10.11"]
[2025-07-11 19:23:11,786] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('2.2.2.106', 55130) -> ('2.2.2.106', 56005)) State.FROZEN -> State.WAITING]
[2025-07-11 19:23:11,786] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('172.26.128.1', 55131) -> ('2.2.2.106', 56005)) State.FROZEN -> State.WAITING]
[2025-07-11 19:23:11,786] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('10.118.7.130', 55132) -> ('2.2.2.106', 56005)) State.FROZEN -> State.WAITING]
[2025-07-11 19:23:11,786] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('169.254.43.7', 55133) -> ('2.2.2.106', 56005)) State.FROZEN -> State.WAITING]
[2025-07-11 19:23:11,786] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('169.254.208.220', 55134) -> ('2.2.2.106', 56005)) State.FROZEN -> State.WAITING]
[2025-07-11 19:23:11,786] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('2.2.2.106', 55130) -> ('172.26.128.1', 56007)) State.FROZEN -> State.WAITING]
[2025-07-11 19:23:11,788] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('2.2.2.106', 55130) -> ('2.2.2.106', 56005)) State.WAITING -> State.IN_PROGRESS]
[2025-07-11 19:23:11,789] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('2.2.2.106', 55130) -> ('2.2.2.106', 56005)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-11 19:23:11,789] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('2.2.2.106', 55130) -> ('10.118.7.130', 56009)) State.FROZEN -> State.WAITING]
[2025-07-11 19:23:11,789] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('2.2.2.106', 55130) -> ('169.254.43.7', 56011)) State.FROZEN -> State.WAITING]
[2025-07-11 19:23:11,790] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('2.2.2.106', 55130) -> ('169.254.208.220', 56013)) State.FROZEN -> State.WAITING]
[2025-07-11 19:23:11,790] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('2.2.2.106', 55130) -> ('58.248.180.197', 25619)) State.FROZEN -> State.WAITING]
[2025-07-11 19:23:11,793] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('172.26.128.1', 55131) -> ('2.2.2.106', 56005)) State.WAITING -> State.FAILED]
[2025-07-11 19:23:11,793] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('10.118.7.130', 55132) -> ('2.2.2.106', 56005)) State.WAITING -> State.FAILED]
[2025-07-11 19:23:11,793] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('169.254.43.7', 55133) -> ('2.2.2.106', 56005)) State.WAITING -> State.FAILED]
[2025-07-11 19:23:11,793] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('169.254.208.220', 55134) -> ('2.2.2.106', 56005)) State.WAITING -> State.FAILED]
[2025-07-11 19:23:11,793] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('2.2.2.106', 55130) -> ('172.26.128.1', 56007)) State.WAITING -> State.FAILED]
[2025-07-11 19:23:11,793] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('172.26.128.1', 55131) -> ('172.26.128.1', 56007)) State.FROZEN -> State.FAILED]
[2025-07-11 19:23:11,793] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('10.118.7.130', 55132) -> ('172.26.128.1', 56007)) State.FROZEN -> State.FAILED]
[2025-07-11 19:23:11,795] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('169.254.43.7', 55133) -> ('172.26.128.1', 56007)) State.FROZEN -> State.FAILED]
[2025-07-11 19:23:11,795] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('169.254.208.220', 55134) -> ('172.26.128.1', 56007)) State.FROZEN -> State.FAILED]
[2025-07-11 19:23:11,795] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('2.2.2.106', 55130) -> ('10.118.7.130', 56009)) State.WAITING -> State.FAILED]
[2025-07-11 19:23:11,795] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('172.26.128.1', 55131) -> ('10.118.7.130', 56009)) State.FROZEN -> State.FAILED]
[2025-07-11 19:23:11,795] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('10.118.7.130', 55132) -> ('10.118.7.130', 56009)) State.FROZEN -> State.FAILED]
[2025-07-11 19:23:11,795] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('169.254.43.7', 55133) -> ('10.118.7.130', 56009)) State.FROZEN -> State.FAILED]
[2025-07-11 19:23:11,795] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('169.254.208.220', 55134) -> ('10.118.7.130', 56009)) State.FROZEN -> State.FAILED]
[2025-07-11 19:23:11,796] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('2.2.2.106', 55130) -> ('169.254.43.7', 56011)) State.WAITING -> State.FAILED]
[2025-07-11 19:23:11,796] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('172.26.128.1', 55131) -> ('169.254.43.7', 56011)) State.FROZEN -> State.FAILED]
[2025-07-11 19:23:11,796] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('10.118.7.130', 55132) -> ('169.254.43.7', 56011)) State.FROZEN -> State.FAILED]
[2025-07-11 19:23:11,796] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('169.254.43.7', 55133) -> ('169.254.43.7', 56011)) State.FROZEN -> State.FAILED]
[2025-07-11 19:23:11,796] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('169.254.208.220', 55134) -> ('169.254.43.7', 56011)) State.FROZEN -> State.FAILED]
[2025-07-11 19:23:11,796] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('2.2.2.106', 55130) -> ('169.254.208.220', 56013)) State.WAITING -> State.FAILED]
[2025-07-11 19:23:11,796] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('172.26.128.1', 55131) -> ('169.254.208.220', 56013)) State.FROZEN -> State.FAILED]
[2025-07-11 19:23:11,796] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('10.118.7.130', 55132) -> ('169.254.208.220', 56013)) State.FROZEN -> State.FAILED]
[2025-07-11 19:23:11,796] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('169.254.43.7', 55133) -> ('169.254.208.220', 56013)) State.FROZEN -> State.FAILED]
[2025-07-11 19:23:11,796] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('169.254.208.220', 55134) -> ('169.254.208.220', 56013)) State.FROZEN -> State.FAILED]
[2025-07-11 19:23:11,796] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('2.2.2.106', 55130) -> ('58.248.180.197', 25619)) State.WAITING -> State.FAILED]
[2025-07-11 19:23:11,797] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('172.26.128.1', 55131) -> ('58.248.180.197', 25619)) State.FROZEN -> State.FAILED]
[2025-07-11 19:23:11,797] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('10.118.7.130', 55132) -> ('58.248.180.197', 25619)) State.FROZEN -> State.FAILED]
[2025-07-11 19:23:11,797] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('169.254.43.7', 55133) -> ('58.248.180.197', 25619)) State.FROZEN -> State.FAILED]
[2025-07-11 19:23:11,797] [ice.py[line:1114]] [INFO] [Connection(8) Check CandidatePair(('169.254.208.220', 55134) -> ('58.248.180.197', 25619)) State.FROZEN -> State.FAILED]
[2025-07-11 19:23:11,797] [ice.py[line:1114]] [INFO] [Connection(8) ICE completed]
[2025-07-11 19:23:38,499] [run_local.py[line:223]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������.]
[2025-07-11 19:23:41,180] [server.py[line:264]] [INFO] [connection closed]
[2025-07-11 19:23:41,181] [run_local.py[line:265]] [INFO] [��start_websocket_server�����������б�ȡ����]
[2025-07-11 19:23:41,181] [run_local.py[line:268]] [INFO] [��start_websocket_server�����ڹرշ�����...]
[2025-07-11 19:23:41,182] [server.py[line:759]] [INFO] [server closing]
[2025-07-11 19:23:41,182] [server.py[line:793]] [INFO] [server closed]
[2025-07-11 19:23:41,182] [run_local.py[line:271]] [INFO] [��start_websocket_server���������ѳɹ��رա�]
[2025-07-11 19:23:43,102] [run_local.py[line:279]] [INFO] [
��⵽�û��жϣ����������ڹرա�]
[2025-07-14 10:38:59,709] [ice.py[line:1114]] [INFO] [Connection(0) Could not bind to 169.254.123.208 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-14 10:38:59,711] [ice.py[line:1114]] [INFO] [Connection(0) Could not bind to 169.254.26.119 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-14 10:39:04,716] [web_log.py[line:211]] [INFO] [127.0.0.1 [14/Jul/2025:10:38:59 +0800] "POST /offer HTTP/1.1" 200 3693 "-" "Python/3.10 aiohttp/3.9.5"]
[2025-07-14 10:39:04,717] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.73', 49237) -> ('2.2.2.73', 60854)) State.FROZEN -> State.WAITING]
[2025-07-14 10:39:04,718] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 49238) -> ('2.2.2.73', 60854)) State.FROZEN -> State.WAITING]
[2025-07-14 10:39:04,718] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 49239) -> ('2.2.2.73', 60854)) State.FROZEN -> State.WAITING]
[2025-07-14 10:39:04,718] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 49240) -> ('2.2.2.73', 60854)) State.FROZEN -> State.WAITING]
[2025-07-14 10:39:04,719] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.17.160.1', 49241) -> ('2.2.2.73', 60854)) State.FROZEN -> State.WAITING]
[2025-07-14 10:39:04,719] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.17.240.1', 49242) -> ('2.2.2.73', 60854)) State.FROZEN -> State.WAITING]
[2025-07-14 10:39:04,719] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.73', 49237) -> ('10.118.7.130', 60856)) State.FROZEN -> State.WAITING]
[2025-07-14 10:39:04,721] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.73', 49237) -> ('2.2.2.73', 60854)) State.WAITING -> State.IN_PROGRESS]
[2025-07-14 10:39:04,724] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.73', 49237) -> ('2.2.2.73', 60854)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-14 10:39:04,725] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.73', 49237) -> ('169.254.208.220', 60858)) State.FROZEN -> State.WAITING]
[2025-07-14 10:39:04,725] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.73', 49237) -> ('169.254.43.7', 60860)) State.FROZEN -> State.WAITING]
[2025-07-14 10:39:04,726] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.73', 49237) -> ('172.17.160.1', 60862)) State.FROZEN -> State.WAITING]
[2025-07-14 10:39:04,726] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.73', 49237) -> ('172.17.240.1', 60864)) State.FROZEN -> State.WAITING]
[2025-07-14 10:39:04,726] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.73', 49237) -> ('58.248.180.197', 24313)) State.FROZEN -> State.WAITING]
[2025-07-14 10:39:04,730] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 49238) -> ('2.2.2.73', 60854)) State.WAITING -> State.FAILED]
[2025-07-14 10:39:04,731] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 49239) -> ('2.2.2.73', 60854)) State.WAITING -> State.FAILED]
[2025-07-14 10:39:04,732] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 49240) -> ('2.2.2.73', 60854)) State.WAITING -> State.FAILED]
[2025-07-14 10:39:04,732] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.17.160.1', 49241) -> ('2.2.2.73', 60854)) State.WAITING -> State.FAILED]
[2025-07-14 10:39:04,732] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.17.240.1', 49242) -> ('2.2.2.73', 60854)) State.WAITING -> State.FAILED]
[2025-07-14 10:39:04,732] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.73', 49237) -> ('10.118.7.130', 60856)) State.WAITING -> State.FAILED]
[2025-07-14 10:39:04,733] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 49238) -> ('10.118.7.130', 60856)) State.FROZEN -> State.FAILED]
[2025-07-14 10:39:04,733] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 49239) -> ('10.118.7.130', 60856)) State.FROZEN -> State.FAILED]
[2025-07-14 10:39:04,733] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 49240) -> ('10.118.7.130', 60856)) State.FROZEN -> State.FAILED]
[2025-07-14 10:39:04,733] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.17.160.1', 49241) -> ('10.118.7.130', 60856)) State.FROZEN -> State.FAILED]
[2025-07-14 10:39:04,733] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.17.240.1', 49242) -> ('10.118.7.130', 60856)) State.FROZEN -> State.FAILED]
[2025-07-14 10:39:04,734] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.73', 49237) -> ('169.254.208.220', 60858)) State.WAITING -> State.FAILED]
[2025-07-14 10:39:04,734] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 49238) -> ('169.254.208.220', 60858)) State.FROZEN -> State.FAILED]
[2025-07-14 10:39:04,734] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 49239) -> ('169.254.208.220', 60858)) State.FROZEN -> State.FAILED]
[2025-07-14 10:39:04,734] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 49240) -> ('169.254.208.220', 60858)) State.FROZEN -> State.FAILED]
[2025-07-14 10:39:04,734] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.17.160.1', 49241) -> ('169.254.208.220', 60858)) State.FROZEN -> State.FAILED]
[2025-07-14 10:39:04,734] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.17.240.1', 49242) -> ('169.254.208.220', 60858)) State.FROZEN -> State.FAILED]
[2025-07-14 10:39:04,735] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.73', 49237) -> ('169.254.43.7', 60860)) State.WAITING -> State.FAILED]
[2025-07-14 10:39:04,735] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 49238) -> ('169.254.43.7', 60860)) State.FROZEN -> State.FAILED]
[2025-07-14 10:39:04,735] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 49239) -> ('169.254.43.7', 60860)) State.FROZEN -> State.FAILED]
[2025-07-14 10:39:04,735] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 49240) -> ('169.254.43.7', 60860)) State.FROZEN -> State.FAILED]
[2025-07-14 10:39:04,736] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.17.160.1', 49241) -> ('169.254.43.7', 60860)) State.FROZEN -> State.FAILED]
[2025-07-14 10:39:04,736] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.17.240.1', 49242) -> ('169.254.43.7', 60860)) State.FROZEN -> State.FAILED]
[2025-07-14 10:39:04,736] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.73', 49237) -> ('172.17.160.1', 60862)) State.WAITING -> State.FAILED]
[2025-07-14 10:39:04,736] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 49238) -> ('172.17.160.1', 60862)) State.FROZEN -> State.FAILED]
[2025-07-14 10:39:04,736] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 49239) -> ('172.17.160.1', 60862)) State.FROZEN -> State.FAILED]
[2025-07-14 10:39:04,737] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 49240) -> ('172.17.160.1', 60862)) State.FROZEN -> State.FAILED]
[2025-07-14 10:39:04,737] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.17.160.1', 49241) -> ('172.17.160.1', 60862)) State.FROZEN -> State.FAILED]
[2025-07-14 10:39:04,737] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.17.240.1', 49242) -> ('172.17.160.1', 60862)) State.FROZEN -> State.FAILED]
[2025-07-14 10:39:04,737] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.73', 49237) -> ('172.17.240.1', 60864)) State.WAITING -> State.FAILED]
[2025-07-14 10:39:04,737] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 49238) -> ('172.17.240.1', 60864)) State.FROZEN -> State.FAILED]
[2025-07-14 10:39:04,738] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 49239) -> ('172.17.240.1', 60864)) State.FROZEN -> State.FAILED]
[2025-07-14 10:39:04,738] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 49240) -> ('172.17.240.1', 60864)) State.FROZEN -> State.FAILED]
[2025-07-14 10:39:04,738] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.17.160.1', 49241) -> ('172.17.240.1', 60864)) State.FROZEN -> State.FAILED]
[2025-07-14 10:39:04,738] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.17.240.1', 49242) -> ('172.17.240.1', 60864)) State.FROZEN -> State.FAILED]
[2025-07-14 10:39:04,738] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.73', 49237) -> ('58.248.180.197', 24313)) State.WAITING -> State.FAILED]
[2025-07-14 10:39:04,738] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 49238) -> ('58.248.180.197', 24313)) State.FROZEN -> State.FAILED]
[2025-07-14 10:39:04,738] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 49239) -> ('58.248.180.197', 24313)) State.FROZEN -> State.FAILED]
[2025-07-14 10:39:04,739] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 49240) -> ('58.248.180.197', 24313)) State.FROZEN -> State.FAILED]
[2025-07-14 10:39:04,739] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.17.160.1', 49241) -> ('58.248.180.197', 24313)) State.FROZEN -> State.FAILED]
[2025-07-14 10:39:04,739] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.17.240.1', 49242) -> ('58.248.180.197', 24313)) State.FROZEN -> State.FAILED]
[2025-07-14 10:39:04,739] [ice.py[line:1114]] [INFO] [Connection(0) ICE completed]
[2025-07-14 10:39:38,960] [ice.py[line:1114]] [INFO] [Connection(0) Consent to send expired]
[2025-07-14 11:48:52,147] [server.py[line:707]] [INFO] [server listening on 127.0.0.1:8766]
[2025-07-14 11:48:52,147] [run_local.py[line:248]] [INFO] [��start_websocket_server��WebSocket���������� ws://127.0.0.1:8766 ����]
[2025-07-14 11:48:52,147] [run_local.py[line:249]] [INFO] [��start_websocket_server���ȴ�������... (���300����û�����ӽ��Զ��ر�)]
[2025-07-14 11:48:54,819] [server.py[line:642]] [INFO] [connection open]
[2025-07-14 11:48:54,819] [run_local.py[line:241]] [INFO] [��start_websocket_server�����յ���һ���ͻ������ӣ����������������С�]
[2025-07-14 11:48:54,819] [run_local.py[line:166]] [INFO] [��websocket_handler��WebSocket client ��ʼ��������: v3720p]
[2025-07-14 11:48:54,821] [run_local.py[line:178]] [INFO] [��websocket_handler���Ѿ�������ʽ����: v3720p]
[2025-07-14 11:48:54,821] [run_local.py[line:193]] [INFO] [��websocket_handler����ʽ���� fa3ba59424e711f091782228ff08ca3c ��ʼ��ʽ����~ ���~~.]
[2025-07-14 11:48:54,822] [run_local.py[line:257]] [INFO] [��start_websocket_server�����������ڽ����������С�]
[2025-07-14 11:50:21,595] [server.py[line:229]] [INFO] [connection rejected (400 Bad Request)]
[2025-07-14 11:50:21,595] [server.py[line:264]] [INFO] [connection closed]
[2025-07-14 11:50:28,469] [ice.py[line:1114]] [INFO] [Connection(0) Could not bind to 169.254.123.208 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-14 11:50:28,470] [ice.py[line:1114]] [INFO] [Connection(0) Could not bind to 169.254.26.119 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-14 11:50:33,477] [web_log.py[line:211]] [INFO] [127.0.0.1 [14/Jul/2025:11:50:28 +0800] "POST /offer HTTP/1.1" 200 3511 "-" "Python/3.10 aiohttp/3.9.5"]
[2025-07-14 11:50:33,477] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.194', 54817) -> ('2.2.2.194', 62814)) State.FROZEN -> State.WAITING]
[2025-07-14 11:50:33,477] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 54818) -> ('2.2.2.194', 62814)) State.FROZEN -> State.WAITING]
[2025-07-14 11:50:33,477] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 54819) -> ('2.2.2.194', 62814)) State.FROZEN -> State.WAITING]
[2025-07-14 11:50:33,477] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 54820) -> ('2.2.2.194', 62814)) State.FROZEN -> State.WAITING]
[2025-07-14 11:50:33,477] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.20.176.1', 54821) -> ('2.2.2.194', 62814)) State.FROZEN -> State.WAITING]
[2025-07-14 11:50:33,477] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.194', 54817) -> ('10.118.7.130', 62816)) State.FROZEN -> State.WAITING]
[2025-07-14 11:50:33,477] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.194', 54817) -> ('2.2.2.194', 62814)) State.WAITING -> State.IN_PROGRESS]
[2025-07-14 11:50:33,477] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.194', 54817) -> ('2.2.2.194', 62814)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-14 11:50:33,477] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.194', 54817) -> ('169.254.208.220', 62818)) State.FROZEN -> State.WAITING]
[2025-07-14 11:50:33,477] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.194', 54817) -> ('169.254.43.7', 62820)) State.FROZEN -> State.WAITING]
[2025-07-14 11:50:33,485] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.194', 54817) -> ('172.20.176.1', 62822)) State.FROZEN -> State.WAITING]
[2025-07-14 11:50:33,485] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.194', 54817) -> ('58.248.180.197', 37319)) State.FROZEN -> State.WAITING]
[2025-07-14 11:50:33,486] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 54818) -> ('2.2.2.194', 62814)) State.WAITING -> State.IN_PROGRESS]
[2025-07-14 11:50:33,487] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 54819) -> ('2.2.2.194', 62814)) State.WAITING -> State.FAILED]
[2025-07-14 11:50:33,487] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 54820) -> ('2.2.2.194', 62814)) State.WAITING -> State.FAILED]
[2025-07-14 11:50:33,488] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.20.176.1', 54821) -> ('2.2.2.194', 62814)) State.WAITING -> State.FAILED]
[2025-07-14 11:50:33,488] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.194', 54817) -> ('10.118.7.130', 62816)) State.WAITING -> State.FAILED]
[2025-07-14 11:50:33,488] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 54818) -> ('10.118.7.130', 62816)) State.FROZEN -> State.FAILED]
[2025-07-14 11:50:33,488] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 54819) -> ('10.118.7.130', 62816)) State.FROZEN -> State.FAILED]
[2025-07-14 11:50:33,489] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 54820) -> ('10.118.7.130', 62816)) State.FROZEN -> State.FAILED]
[2025-07-14 11:50:33,489] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.20.176.1', 54821) -> ('10.118.7.130', 62816)) State.FROZEN -> State.FAILED]
[2025-07-14 11:50:33,489] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.194', 54817) -> ('169.254.208.220', 62818)) State.WAITING -> State.FAILED]
[2025-07-14 11:50:33,489] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 54818) -> ('169.254.208.220', 62818)) State.FROZEN -> State.FAILED]
[2025-07-14 11:50:33,489] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 54819) -> ('169.254.208.220', 62818)) State.FROZEN -> State.FAILED]
[2025-07-14 11:50:33,490] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 54820) -> ('169.254.208.220', 62818)) State.FROZEN -> State.FAILED]
[2025-07-14 11:50:33,490] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.20.176.1', 54821) -> ('169.254.208.220', 62818)) State.FROZEN -> State.FAILED]
[2025-07-14 11:50:33,490] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.194', 54817) -> ('169.254.43.7', 62820)) State.WAITING -> State.FAILED]
[2025-07-14 11:50:33,490] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 54818) -> ('169.254.43.7', 62820)) State.FROZEN -> State.FAILED]
[2025-07-14 11:50:33,490] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 54819) -> ('169.254.43.7', 62820)) State.FROZEN -> State.FAILED]
[2025-07-14 11:50:33,491] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 54820) -> ('169.254.43.7', 62820)) State.FROZEN -> State.FAILED]
[2025-07-14 11:50:33,491] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.20.176.1', 54821) -> ('169.254.43.7', 62820)) State.FROZEN -> State.FAILED]
[2025-07-14 11:50:33,491] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.194', 54817) -> ('172.20.176.1', 62822)) State.WAITING -> State.FAILED]
[2025-07-14 11:50:33,491] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 54818) -> ('172.20.176.1', 62822)) State.FROZEN -> State.FAILED]
[2025-07-14 11:50:33,492] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 54819) -> ('172.20.176.1', 62822)) State.FROZEN -> State.FAILED]
[2025-07-14 11:50:33,492] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 54820) -> ('172.20.176.1', 62822)) State.FROZEN -> State.FAILED]
[2025-07-14 11:50:33,492] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.20.176.1', 54821) -> ('172.20.176.1', 62822)) State.FROZEN -> State.FAILED]
[2025-07-14 11:50:33,492] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.194', 54817) -> ('58.248.180.197', 37319)) State.WAITING -> State.FAILED]
[2025-07-14 11:50:33,492] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 54818) -> ('58.248.180.197', 37319)) State.FROZEN -> State.FAILED]
[2025-07-14 11:50:33,492] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 54819) -> ('58.248.180.197', 37319)) State.FROZEN -> State.FAILED]
[2025-07-14 11:50:33,493] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 54820) -> ('58.248.180.197', 37319)) State.FROZEN -> State.FAILED]
[2025-07-14 11:50:33,493] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.20.176.1', 54821) -> ('58.248.180.197', 37319)) State.FROZEN -> State.FAILED]
[2025-07-14 11:50:33,493] [ice.py[line:1114]] [INFO] [Connection(0) ICE completed]
[2025-07-14 11:50:53,757] [run_local.py[line:223]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������.]
[2025-07-14 11:50:54,536] [server.py[line:264]] [INFO] [connection closed]
[2025-07-14 11:50:54,536] [run_local.py[line:265]] [INFO] [��start_websocket_server�����������б�ȡ����]
[2025-07-14 11:50:54,536] [run_local.py[line:268]] [INFO] [��start_websocket_server�����ڹرշ�����...]
[2025-07-14 11:50:54,536] [server.py[line:759]] [INFO] [server closing]
[2025-07-14 11:50:54,536] [server.py[line:793]] [INFO] [server closed]
[2025-07-14 11:50:54,536] [run_local.py[line:271]] [INFO] [��start_websocket_server���������ѳɹ��رա�]
[2025-07-14 11:51:04,551] [run_local.py[line:279]] [INFO] [
��⵽�û��жϣ����������ڹرա�]
[2025-07-14 11:55:37,862] [server.py[line:707]] [INFO] [server listening on 127.0.0.1:8766]
[2025-07-14 11:55:37,863] [run_local.py[line:248]] [INFO] [��start_websocket_server��WebSocket���������� ws://127.0.0.1:8766 ����]
[2025-07-14 11:55:37,863] [run_local.py[line:249]] [INFO] [��start_websocket_server���ȴ�������... (���300����û�����ӽ��Զ��ر�)]
[2025-07-14 11:55:42,334] [server.py[line:642]] [INFO] [connection open]
[2025-07-14 11:55:42,334] [run_local.py[line:241]] [INFO] [��start_websocket_server�����յ���һ���ͻ������ӣ����������������С�]
[2025-07-14 11:55:42,334] [run_local.py[line:166]] [INFO] [��websocket_handler��WebSocket client ��ʼ��������: v3720p]
[2025-07-14 11:55:42,335] [run_local.py[line:178]] [INFO] [��websocket_handler���Ѿ�������ʽ����: v3720p]
[2025-07-14 11:55:42,336] [run_local.py[line:193]] [INFO] [��websocket_handler����ʽ���� fa3ba59424e711f091782228ff08ca3c ��ʼ��ʽ����~ ���~~.]
[2025-07-14 11:55:42,337] [run_local.py[line:257]] [INFO] [��start_websocket_server�����������ڽ����������С�]
[2025-07-14 11:57:03,540] [run_local.py[line:223]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������.]
[2025-07-15 15:04:47,621] [server.py[line:707]] [INFO] [server listening on 127.0.0.1:8766]
[2025-07-15 15:04:47,622] [run_local.py[line:248]] [INFO] [��start_websocket_server��WebSocket���������� ws://127.0.0.1:8766 ����]
[2025-07-15 15:04:47,622] [run_local.py[line:249]] [INFO] [��start_websocket_server���ȴ�������... (���300����û�����ӽ��Զ��ر�)]
[2025-07-15 15:04:47,781] [server.py[line:642]] [INFO] [connection open]
[2025-07-15 15:04:47,781] [run_local.py[line:241]] [INFO] [��start_websocket_server�����յ���һ���ͻ������ӣ����������������С�]
[2025-07-15 15:04:47,781] [run_local.py[line:166]] [INFO] [��websocket_handler��WebSocket client ��ʼ��������: v3720p]
[2025-07-15 15:04:47,782] [run_local.py[line:178]] [INFO] [��websocket_handler���Ѿ�������ʽ����: v3720p]
[2025-07-15 15:04:47,782] [run_local.py[line:193]] [INFO] [��websocket_handler����ʽ���� fa3ba59424e711f091782228ff08ca3c ��ʼ��ʽ����~ ���~~.]
[2025-07-15 15:04:47,783] [run_local.py[line:257]] [INFO] [��start_websocket_server�����������ڽ����������С�]
[2025-07-15 15:05:12,576] [ice.py[line:1114]] [INFO] [Connection(0) Could not bind to 169.254.123.208 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-15 15:05:12,576] [ice.py[line:1114]] [INFO] [Connection(0) Could not bind to 169.254.26.119 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-15 15:05:17,596] [web_log.py[line:211]] [INFO] [127.0.0.1 [15/Jul/2025:15:05:12 +0800] "POST /offer HTTP/1.1" 200 3695 "-" "Python/3.10 aiohttp/3.9.5"]
[2025-07-15 15:05:17,597] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.94', 54687) -> ('2.2.2.94', 54688)) State.FROZEN -> State.WAITING]
[2025-07-15 15:05:17,597] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 54700) -> ('2.2.2.94', 54688)) State.FROZEN -> State.WAITING]
[2025-07-15 15:05:17,597] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 54701) -> ('2.2.2.94', 54688)) State.FROZEN -> State.WAITING]
[2025-07-15 15:05:17,598] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 54702) -> ('2.2.2.94', 54688)) State.FROZEN -> State.WAITING]
[2025-07-15 15:05:17,598] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.20.176.1', 54703) -> ('2.2.2.94', 54688)) State.FROZEN -> State.WAITING]
[2025-07-15 15:05:17,598] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.17.240.1', 54704) -> ('2.2.2.94', 54688)) State.FROZEN -> State.WAITING]
[2025-07-15 15:05:17,598] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.94', 54687) -> ('10.118.7.130', 54690)) State.FROZEN -> State.WAITING]
[2025-07-15 15:05:17,599] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.94', 54687) -> ('2.2.2.94', 54688)) State.WAITING -> State.IN_PROGRESS]
[2025-07-15 15:05:17,601] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.94', 54687) -> ('2.2.2.94', 54688)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-15 15:05:17,601] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.94', 54687) -> ('169.254.208.220', 54692)) State.FROZEN -> State.WAITING]
[2025-07-15 15:05:17,601] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.94', 54687) -> ('169.254.43.7', 54694)) State.FROZEN -> State.WAITING]
[2025-07-15 15:05:17,601] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.94', 54687) -> ('172.20.176.1', 54696)) State.FROZEN -> State.WAITING]
[2025-07-15 15:05:17,602] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.94', 54687) -> ('172.17.240.1', 54698)) State.FROZEN -> State.WAITING]
[2025-07-15 15:05:17,602] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.94', 54687) -> ('58.248.180.197', 43943)) State.FROZEN -> State.WAITING]
[2025-07-15 15:05:17,605] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 54700) -> ('2.2.2.94', 54688)) State.WAITING -> State.FAILED]
[2025-07-15 15:05:17,605] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 54701) -> ('2.2.2.94', 54688)) State.WAITING -> State.FAILED]
[2025-07-15 15:05:17,605] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 54702) -> ('2.2.2.94', 54688)) State.WAITING -> State.FAILED]
[2025-07-15 15:05:17,605] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.20.176.1', 54703) -> ('2.2.2.94', 54688)) State.WAITING -> State.FAILED]
[2025-07-15 15:05:17,605] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.17.240.1', 54704) -> ('2.2.2.94', 54688)) State.WAITING -> State.FAILED]
[2025-07-15 15:05:17,605] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.94', 54687) -> ('10.118.7.130', 54690)) State.WAITING -> State.FAILED]
[2025-07-15 15:05:17,605] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 54700) -> ('10.118.7.130', 54690)) State.FROZEN -> State.FAILED]
[2025-07-15 15:05:17,605] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 54701) -> ('10.118.7.130', 54690)) State.FROZEN -> State.FAILED]
[2025-07-15 15:05:17,606] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 54702) -> ('10.118.7.130', 54690)) State.FROZEN -> State.FAILED]
[2025-07-15 15:05:17,606] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.20.176.1', 54703) -> ('10.118.7.130', 54690)) State.FROZEN -> State.FAILED]
[2025-07-15 15:05:17,606] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.17.240.1', 54704) -> ('10.118.7.130', 54690)) State.FROZEN -> State.FAILED]
[2025-07-15 15:05:17,606] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.94', 54687) -> ('169.254.208.220', 54692)) State.WAITING -> State.FAILED]
[2025-07-15 15:05:17,606] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 54700) -> ('169.254.208.220', 54692)) State.FROZEN -> State.FAILED]
[2025-07-15 15:05:17,606] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 54701) -> ('169.254.208.220', 54692)) State.FROZEN -> State.FAILED]
[2025-07-15 15:05:17,606] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 54702) -> ('169.254.208.220', 54692)) State.FROZEN -> State.FAILED]
[2025-07-15 15:05:17,606] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.20.176.1', 54703) -> ('169.254.208.220', 54692)) State.FROZEN -> State.FAILED]
[2025-07-15 15:05:17,606] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.17.240.1', 54704) -> ('169.254.208.220', 54692)) State.FROZEN -> State.FAILED]
[2025-07-15 15:05:17,606] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.94', 54687) -> ('169.254.43.7', 54694)) State.WAITING -> State.FAILED]
[2025-07-15 15:05:17,607] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 54700) -> ('169.254.43.7', 54694)) State.FROZEN -> State.FAILED]
[2025-07-15 15:05:17,607] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 54701) -> ('169.254.43.7', 54694)) State.FROZEN -> State.FAILED]
[2025-07-15 15:05:17,607] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 54702) -> ('169.254.43.7', 54694)) State.FROZEN -> State.FAILED]
[2025-07-15 15:05:17,607] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.20.176.1', 54703) -> ('169.254.43.7', 54694)) State.FROZEN -> State.FAILED]
[2025-07-15 15:05:17,607] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.17.240.1', 54704) -> ('169.254.43.7', 54694)) State.FROZEN -> State.FAILED]
[2025-07-15 15:05:17,607] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.94', 54687) -> ('172.20.176.1', 54696)) State.WAITING -> State.FAILED]
[2025-07-15 15:05:17,607] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 54700) -> ('172.20.176.1', 54696)) State.FROZEN -> State.FAILED]
[2025-07-15 15:05:17,607] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 54701) -> ('172.20.176.1', 54696)) State.FROZEN -> State.FAILED]
[2025-07-15 15:05:17,608] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 54702) -> ('172.20.176.1', 54696)) State.FROZEN -> State.FAILED]
[2025-07-15 15:05:17,608] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.20.176.1', 54703) -> ('172.20.176.1', 54696)) State.FROZEN -> State.FAILED]
[2025-07-15 15:05:17,608] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.17.240.1', 54704) -> ('172.20.176.1', 54696)) State.FROZEN -> State.FAILED]
[2025-07-15 15:05:17,608] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.94', 54687) -> ('172.17.240.1', 54698)) State.WAITING -> State.FAILED]
[2025-07-15 15:05:17,608] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 54700) -> ('172.17.240.1', 54698)) State.FROZEN -> State.FAILED]
[2025-07-15 15:05:17,608] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 54701) -> ('172.17.240.1', 54698)) State.FROZEN -> State.FAILED]
[2025-07-15 15:05:17,608] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 54702) -> ('172.17.240.1', 54698)) State.FROZEN -> State.FAILED]
[2025-07-15 15:05:17,608] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.20.176.1', 54703) -> ('172.17.240.1', 54698)) State.FROZEN -> State.FAILED]
[2025-07-15 15:05:17,608] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.17.240.1', 54704) -> ('172.17.240.1', 54698)) State.FROZEN -> State.FAILED]
[2025-07-15 15:05:17,609] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.94', 54687) -> ('58.248.180.197', 43943)) State.WAITING -> State.FAILED]
[2025-07-15 15:05:17,609] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 54700) -> ('58.248.180.197', 43943)) State.FROZEN -> State.FAILED]
[2025-07-15 15:05:17,609] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 54701) -> ('58.248.180.197', 43943)) State.FROZEN -> State.FAILED]
[2025-07-15 15:05:17,609] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 54702) -> ('58.248.180.197', 43943)) State.FROZEN -> State.FAILED]
[2025-07-15 15:05:17,609] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.20.176.1', 54703) -> ('58.248.180.197', 43943)) State.FROZEN -> State.FAILED]
[2025-07-15 15:05:17,609] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.17.240.1', 54704) -> ('58.248.180.197', 43943)) State.FROZEN -> State.FAILED]
[2025-07-15 15:05:17,609] [ice.py[line:1114]] [INFO] [Connection(0) ICE completed]
[2025-07-15 15:29:59,489] [run_local.py[line:223]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������.]
[2025-07-15 15:36:51,079] [server.py[line:707]] [INFO] [server listening on 127.0.0.1:8766]
[2025-07-15 15:36:51,079] [run_local.py[line:248]] [INFO] [��start_websocket_server��WebSocket���������� ws://127.0.0.1:8766 ����]
[2025-07-15 15:36:51,080] [run_local.py[line:249]] [INFO] [��start_websocket_server���ȴ�������... (���300����û�����ӽ��Զ��ر�)]
[2025-07-15 15:36:56,856] [server.py[line:642]] [INFO] [connection open]
[2025-07-15 15:36:56,856] [run_local.py[line:241]] [INFO] [��start_websocket_server�����յ���һ���ͻ������ӣ����������������С�]
[2025-07-15 15:36:56,856] [run_local.py[line:166]] [INFO] [��websocket_handler��WebSocket client ��ʼ��������: v3720p]
[2025-07-15 15:36:56,856] [run_local.py[line:178]] [INFO] [��websocket_handler���Ѿ�������ʽ����: v3720p]
[2025-07-15 15:36:56,857] [run_local.py[line:193]] [INFO] [��websocket_handler����ʽ���� fa3ba59424e711f091782228ff08ca3c ��ʼ��ʽ����~ ���~~.]
[2025-07-15 15:36:56,857] [run_local.py[line:257]] [INFO] [��start_websocket_server�����������ڽ����������С�]
[2025-07-15 15:37:01,872] [ice.py[line:1114]] [INFO] [Connection(0) Could not bind to 169.254.123.208 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-15 15:37:01,873] [ice.py[line:1114]] [INFO] [Connection(0) Could not bind to 169.254.26.119 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-15 15:37:06,884] [web_log.py[line:211]] [INFO] [127.0.0.1 [15/Jul/2025:15:37:01 +0800] "POST /offer HTTP/1.1" 200 3513 "-" "Python/3.10 aiohttp/3.9.5"]
[2025-07-15 15:37:06,885] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.193', 59345) -> ('2.2.2.193', 59335)) State.FROZEN -> State.WAITING]
[2025-07-15 15:37:06,885] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.23.176.1', 59346) -> ('2.2.2.193', 59335)) State.FROZEN -> State.WAITING]
[2025-07-15 15:37:06,885] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 59347) -> ('2.2.2.193', 59335)) State.FROZEN -> State.WAITING]
[2025-07-15 15:37:06,885] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 59348) -> ('2.2.2.193', 59335)) State.FROZEN -> State.WAITING]
[2025-07-15 15:37:06,885] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 59349) -> ('2.2.2.193', 59335)) State.FROZEN -> State.WAITING]
[2025-07-15 15:37:06,885] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.193', 59345) -> ('172.23.176.1', 59337)) State.FROZEN -> State.WAITING]
[2025-07-15 15:37:06,886] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.193', 59345) -> ('2.2.2.193', 59335)) State.WAITING -> State.IN_PROGRESS]
[2025-07-15 15:37:06,887] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.193', 59345) -> ('2.2.2.193', 59335)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-15 15:37:06,888] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.193', 59345) -> ('10.118.7.130', 59339)) State.FROZEN -> State.WAITING]
[2025-07-15 15:37:06,888] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.193', 59345) -> ('169.254.43.7', 59341)) State.FROZEN -> State.WAITING]
[2025-07-15 15:37:06,888] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.193', 59345) -> ('169.254.208.220', 59343)) State.FROZEN -> State.WAITING]
[2025-07-15 15:37:06,888] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.193', 59345) -> ('58.248.180.197', 36403)) State.FROZEN -> State.WAITING]
[2025-07-15 15:37:06,889] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.23.176.1', 59346) -> ('2.2.2.193', 59335)) State.WAITING -> State.IN_PROGRESS]
[2025-07-15 15:37:06,890] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 59347) -> ('2.2.2.193', 59335)) State.WAITING -> State.FAILED]
[2025-07-15 15:37:06,890] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 59348) -> ('2.2.2.193', 59335)) State.WAITING -> State.FAILED]
[2025-07-15 15:37:06,890] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 59349) -> ('2.2.2.193', 59335)) State.WAITING -> State.FAILED]
[2025-07-15 15:37:06,891] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.193', 59345) -> ('172.23.176.1', 59337)) State.WAITING -> State.FAILED]
[2025-07-15 15:37:06,891] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.23.176.1', 59346) -> ('172.23.176.1', 59337)) State.FROZEN -> State.FAILED]
[2025-07-15 15:37:06,891] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 59347) -> ('172.23.176.1', 59337)) State.FROZEN -> State.FAILED]
[2025-07-15 15:37:06,891] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 59348) -> ('172.23.176.1', 59337)) State.FROZEN -> State.FAILED]
[2025-07-15 15:37:06,891] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 59349) -> ('172.23.176.1', 59337)) State.FROZEN -> State.FAILED]
[2025-07-15 15:37:06,891] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.193', 59345) -> ('10.118.7.130', 59339)) State.WAITING -> State.FAILED]
[2025-07-15 15:37:06,891] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.23.176.1', 59346) -> ('10.118.7.130', 59339)) State.FROZEN -> State.FAILED]
[2025-07-15 15:37:06,891] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 59347) -> ('10.118.7.130', 59339)) State.FROZEN -> State.FAILED]
[2025-07-15 15:37:06,891] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 59348) -> ('10.118.7.130', 59339)) State.FROZEN -> State.FAILED]
[2025-07-15 15:37:06,891] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 59349) -> ('10.118.7.130', 59339)) State.FROZEN -> State.FAILED]
[2025-07-15 15:37:06,891] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.193', 59345) -> ('169.254.43.7', 59341)) State.WAITING -> State.FAILED]
[2025-07-15 15:37:06,891] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.23.176.1', 59346) -> ('169.254.43.7', 59341)) State.FROZEN -> State.FAILED]
[2025-07-15 15:37:06,891] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 59347) -> ('169.254.43.7', 59341)) State.FROZEN -> State.FAILED]
[2025-07-15 15:37:06,892] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 59348) -> ('169.254.43.7', 59341)) State.FROZEN -> State.FAILED]
[2025-07-15 15:37:06,892] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 59349) -> ('169.254.43.7', 59341)) State.FROZEN -> State.FAILED]
[2025-07-15 15:37:06,892] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.193', 59345) -> ('169.254.208.220', 59343)) State.WAITING -> State.FAILED]
[2025-07-15 15:37:06,892] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.23.176.1', 59346) -> ('169.254.208.220', 59343)) State.FROZEN -> State.FAILED]
[2025-07-15 15:37:06,892] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 59347) -> ('169.254.208.220', 59343)) State.FROZEN -> State.FAILED]
[2025-07-15 15:37:06,892] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 59348) -> ('169.254.208.220', 59343)) State.FROZEN -> State.FAILED]
[2025-07-15 15:37:06,892] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 59349) -> ('169.254.208.220', 59343)) State.FROZEN -> State.FAILED]
[2025-07-15 15:37:06,892] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.193', 59345) -> ('58.248.180.197', 36403)) State.WAITING -> State.FAILED]
[2025-07-15 15:37:06,892] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.23.176.1', 59346) -> ('58.248.180.197', 36403)) State.FROZEN -> State.FAILED]
[2025-07-15 15:37:06,892] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 59347) -> ('58.248.180.197', 36403)) State.FROZEN -> State.FAILED]
[2025-07-15 15:37:06,892] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 59348) -> ('58.248.180.197', 36403)) State.FROZEN -> State.FAILED]
[2025-07-15 15:37:06,892] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 59349) -> ('58.248.180.197', 36403)) State.FROZEN -> State.FAILED]
[2025-07-15 15:37:06,893] [ice.py[line:1114]] [INFO] [Connection(0) ICE completed]
[2025-07-15 15:37:37,335] [run_local.py[line:265]] [INFO] [��start_websocket_server�����������б�ȡ����]
[2025-07-15 15:37:37,335] [run_local.py[line:268]] [INFO] [��start_websocket_server�����ڹرշ�����...]
[2025-07-15 15:37:37,335] [run_local.py[line:223]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������.]
[2025-07-15 15:37:37,335] [run_local.py[line:225]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������.]
[2025-07-15 15:37:37,335] [server.py[line:264]] [INFO] [connection closed]
[2025-07-15 15:37:37,337] [server.py[line:759]] [INFO] [server closing]
[2025-07-15 15:37:37,337] [server.py[line:793]] [INFO] [server closed]
[2025-07-15 15:37:37,337] [run_local.py[line:271]] [INFO] [��start_websocket_server���������ѳɹ��رա�]
[2025-07-15 15:37:37,337] [run_local.py[line:279]] [INFO] [
��⵽�û��жϣ����������ڹرա�]
[2025-07-15 19:58:27,024] [server.py[line:707]] [INFO] [server listening on 127.0.0.1:8766]
[2025-07-15 19:58:27,025] [run_local.py[line:248]] [INFO] [��start_websocket_server��WebSocket���������� ws://127.0.0.1:8766 ����]
[2025-07-15 19:58:27,025] [run_local.py[line:249]] [INFO] [��start_websocket_server���ȴ�������... (���300����û�����ӽ��Զ��ر�)]
[2025-07-15 19:58:27,674] [server.py[line:642]] [INFO] [connection open]
[2025-07-15 19:58:27,674] [run_local.py[line:241]] [INFO] [��start_websocket_server�����յ���һ���ͻ������ӣ����������������С�]
[2025-07-15 19:58:27,674] [run_local.py[line:166]] [INFO] [��websocket_handler��WebSocket client ��ʼ��������: v3720p]
[2025-07-15 19:58:27,674] [run_local.py[line:178]] [INFO] [��websocket_handler���Ѿ�������ʽ����: v3720p]
[2025-07-15 19:58:27,675] [run_local.py[line:193]] [INFO] [��websocket_handler����ʽ���� fa3ba59424e711f091782228ff08ca3c ��ʼ��ʽ����~ ���~~.]
[2025-07-15 19:58:27,676] [run_local.py[line:257]] [INFO] [��start_websocket_server�����������ڽ����������С�]
[2025-07-15 19:59:04,678] [ice.py[line:1114]] [INFO] [Connection(0) Could not bind to 169.254.123.208 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-15 19:59:04,678] [ice.py[line:1114]] [INFO] [Connection(0) Could not bind to 169.254.26.119 - [WinError 10049] �����������У�������ĵ�ַ��Ч��]
[2025-07-15 19:59:09,684] [web_log.py[line:211]] [INFO] [127.0.0.1 [15/Jul/2025:19:59:04 +0800] "POST /offer HTTP/1.1" 200 3513 "-" "Python/3.10 aiohttp/3.9.5"]
[2025-07-15 19:59:09,685] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.193', 58704) -> ('2.2.2.193', 56483)) State.FROZEN -> State.WAITING]
[2025-07-15 19:59:09,685] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.23.176.1', 58705) -> ('2.2.2.193', 56483)) State.FROZEN -> State.WAITING]
[2025-07-15 19:59:09,686] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 58706) -> ('2.2.2.193', 56483)) State.FROZEN -> State.WAITING]
[2025-07-15 19:59:09,686] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 58707) -> ('2.2.2.193', 56483)) State.FROZEN -> State.WAITING]
[2025-07-15 19:59:09,686] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 58708) -> ('2.2.2.193', 56483)) State.FROZEN -> State.WAITING]
[2025-07-15 19:59:09,686] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.193', 58704) -> ('172.23.176.1', 56485)) State.FROZEN -> State.WAITING]
[2025-07-15 19:59:09,687] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.193', 58704) -> ('2.2.2.193', 56483)) State.WAITING -> State.IN_PROGRESS]
[2025-07-15 19:59:09,689] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.193', 58704) -> ('2.2.2.193', 56483)) State.IN_PROGRESS -> State.SUCCEEDED]
[2025-07-15 19:59:09,690] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.193', 58704) -> ('10.118.7.130', 56487)) State.FROZEN -> State.WAITING]
[2025-07-15 19:59:09,690] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.193', 58704) -> ('169.254.43.7', 56489)) State.FROZEN -> State.WAITING]
[2025-07-15 19:59:09,690] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.193', 58704) -> ('169.254.208.220', 56491)) State.FROZEN -> State.WAITING]
[2025-07-15 19:59:09,690] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.193', 58704) -> ('58.248.180.197', 25694)) State.FROZEN -> State.WAITING]
[2025-07-15 19:59:09,694] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.23.176.1', 58705) -> ('2.2.2.193', 56483)) State.WAITING -> State.FAILED]
[2025-07-15 19:59:09,694] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 58706) -> ('2.2.2.193', 56483)) State.WAITING -> State.FAILED]
[2025-07-15 19:59:09,694] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 58707) -> ('2.2.2.193', 56483)) State.WAITING -> State.FAILED]
[2025-07-15 19:59:09,694] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 58708) -> ('2.2.2.193', 56483)) State.WAITING -> State.FAILED]
[2025-07-15 19:59:09,695] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.193', 58704) -> ('172.23.176.1', 56485)) State.WAITING -> State.FAILED]
[2025-07-15 19:59:09,695] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.23.176.1', 58705) -> ('172.23.176.1', 56485)) State.FROZEN -> State.FAILED]
[2025-07-15 19:59:09,695] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 58706) -> ('172.23.176.1', 56485)) State.FROZEN -> State.FAILED]
[2025-07-15 19:59:09,695] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 58707) -> ('172.23.176.1', 56485)) State.FROZEN -> State.FAILED]
[2025-07-15 19:59:09,695] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 58708) -> ('172.23.176.1', 56485)) State.FROZEN -> State.FAILED]
[2025-07-15 19:59:09,695] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.193', 58704) -> ('10.118.7.130', 56487)) State.WAITING -> State.FAILED]
[2025-07-15 19:59:09,695] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.23.176.1', 58705) -> ('10.118.7.130', 56487)) State.FROZEN -> State.FAILED]
[2025-07-15 19:59:09,695] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 58706) -> ('10.118.7.130', 56487)) State.FROZEN -> State.FAILED]
[2025-07-15 19:59:09,695] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 58707) -> ('10.118.7.130', 56487)) State.FROZEN -> State.FAILED]
[2025-07-15 19:59:09,695] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 58708) -> ('10.118.7.130', 56487)) State.FROZEN -> State.FAILED]
[2025-07-15 19:59:09,696] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.193', 58704) -> ('169.254.43.7', 56489)) State.WAITING -> State.FAILED]
[2025-07-15 19:59:09,696] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.23.176.1', 58705) -> ('169.254.43.7', 56489)) State.FROZEN -> State.FAILED]
[2025-07-15 19:59:09,696] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 58706) -> ('169.254.43.7', 56489)) State.FROZEN -> State.FAILED]
[2025-07-15 19:59:09,696] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 58707) -> ('169.254.43.7', 56489)) State.FROZEN -> State.FAILED]
[2025-07-15 19:59:09,696] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 58708) -> ('169.254.43.7', 56489)) State.FROZEN -> State.FAILED]
[2025-07-15 19:59:09,696] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.193', 58704) -> ('169.254.208.220', 56491)) State.WAITING -> State.FAILED]
[2025-07-15 19:59:09,696] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.23.176.1', 58705) -> ('169.254.208.220', 56491)) State.FROZEN -> State.FAILED]
[2025-07-15 19:59:09,696] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 58706) -> ('169.254.208.220', 56491)) State.FROZEN -> State.FAILED]
[2025-07-15 19:59:09,696] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 58707) -> ('169.254.208.220', 56491)) State.FROZEN -> State.FAILED]
[2025-07-15 19:59:09,696] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 58708) -> ('169.254.208.220', 56491)) State.FROZEN -> State.FAILED]
[2025-07-15 19:59:09,697] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('2.2.2.193', 58704) -> ('58.248.180.197', 25694)) State.WAITING -> State.FAILED]
[2025-07-15 19:59:09,697] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('172.23.176.1', 58705) -> ('58.248.180.197', 25694)) State.FROZEN -> State.FAILED]
[2025-07-15 19:59:09,697] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('10.118.7.130', 58706) -> ('58.248.180.197', 25694)) State.FROZEN -> State.FAILED]
[2025-07-15 19:59:09,697] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.43.7', 58707) -> ('58.248.180.197', 25694)) State.FROZEN -> State.FAILED]
[2025-07-15 19:59:09,697] [ice.py[line:1114]] [INFO] [Connection(0) Check CandidatePair(('169.254.208.220', 58708) -> ('58.248.180.197', 25694)) State.FROZEN -> State.FAILED]
[2025-07-15 19:59:09,697] [ice.py[line:1114]] [INFO] [Connection(0) ICE completed]
[2025-07-15 19:59:27,498] [server.py[line:642]] [INFO] [connection open]
[2025-07-15 19:59:27,499] [run_local.py[line:166]] [INFO] [��websocket_handler��WebSocket client ��ʼ��������: v3720p]
[2025-07-15 19:59:27,499] [run_local.py[line:178]] [INFO] [��websocket_handler���Ѿ�������ʽ����: v3720p]
[2025-07-15 19:59:27,500] [run_local.py[line:193]] [INFO] [��websocket_handler����ʽ���� fa3ba59424e711f091782228ff08ca3c ��ʼ��ʽ����~ ���~~.]
[2025-07-15 19:59:42,041] [run_local.py[line:216]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������: sent 1011 (internal error) keepalive ping timeout; no close frame received]
[2025-07-15 19:59:42,041] [run_local.py[line:223]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������.]
[2025-07-15 19:59:42,268] [run_local.py[line:225]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������.]
[2025-07-15 19:59:42,269] [server.py[line:264]] [INFO] [connection closed]
[2025-07-15 19:59:43,870] [ice.py[line:1114]] [INFO] [Connection(0) Consent to send expired]
[2025-07-15 19:59:52,196] [run_local.py[line:223]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������.]
[2025-07-29 15:10:43,425] [server.py[line:707]] [INFO] [server listening on 127.0.0.1:8766]
[2025-07-29 15:10:43,425] [run_local.py[line:248]] [INFO] [��start_websocket_server��WebSocket���������� ws://127.0.0.1:8766 ����]
[2025-07-29 15:10:43,425] [run_local.py[line:249]] [INFO] [��start_websocket_server���ȴ�������... (���300����û�����ӽ��Զ��ر�)]
[2025-07-29 15:10:47,657] [run_local.py[line:265]] [INFO] [��start_websocket_server�����������б�ȡ����]
[2025-07-29 15:10:47,657] [run_local.py[line:268]] [INFO] [��start_websocket_server�����ڹرշ�����...]
[2025-07-29 15:10:47,657] [server.py[line:759]] [INFO] [server closing]
[2025-07-29 15:10:47,657] [server.py[line:793]] [INFO] [server closed]
[2025-07-29 15:10:47,657] [run_local.py[line:271]] [INFO] [��start_websocket_server���������ѳɹ��رա�]
[2025-07-29 15:10:47,657] [run_local.py[line:279]] [INFO] [
��⵽�û��жϣ����������ڹرա�]
[2025-07-29 15:12:19,337] [server.py[line:707]] [INFO] [server listening on 127.0.0.1:8766]
[2025-07-29 15:12:19,337] [run_local.py[line:248]] [INFO] [��start_websocket_server��WebSocket���������� ws://127.0.0.1:8766 ����]
[2025-07-29 15:12:19,337] [run_local.py[line:249]] [INFO] [��start_websocket_server���ȴ�������... (���300����û�����ӽ��Զ��ر�)]
[2025-07-29 15:12:20,522] [server.py[line:642]] [INFO] [connection open]
[2025-07-29 15:12:20,525] [run_local.py[line:241]] [INFO] [��start_websocket_server�����յ���һ���ͻ������ӣ����������������С�]
[2025-07-29 15:12:20,605] [run_local.py[line:166]] [INFO] [��websocket_handler��WebSocket client ��ʼ��������: v3720p]
[2025-07-29 15:12:20,621] [run_local.py[line:178]] [INFO] [��websocket_handler���Ѿ�������ʽ����: v3720p]
[2025-07-29 15:12:20,727] [run_local.py[line:193]] [INFO] [��websocket_handler����ʽ���� fa3ba59424e711f091782228ff08ca3c ��ʼ��ʽ����~ ���~~.]
[2025-07-29 15:12:20,727] [run_local.py[line:257]] [INFO] [��start_websocket_server�����������ڽ����������С�]
[2025-07-29 15:14:22,665] [server.py[line:707]] [INFO] [server listening on 127.0.0.1:8766]
[2025-07-29 15:14:22,665] [run_local.py[line:248]] [INFO] [��start_websocket_server��WebSocket���������� ws://127.0.0.1:8766 ����]
[2025-07-29 15:14:22,665] [run_local.py[line:249]] [INFO] [��start_websocket_server���ȴ�������... (���300����û�����ӽ��Զ��ر�)]
[2025-07-29 15:14:23,099] [server.py[line:642]] [INFO] [connection open]
[2025-07-29 15:14:23,099] [run_local.py[line:241]] [INFO] [��start_websocket_server�����յ���һ���ͻ������ӣ����������������С�]
[2025-07-29 15:14:23,099] [run_local.py[line:166]] [INFO] [��websocket_handler��WebSocket client ��ʼ��������: v3720p]
[2025-07-29 15:14:23,099] [run_local.py[line:178]] [INFO] [��websocket_handler���Ѿ�������ʽ����: v3720p]
[2025-07-29 15:14:23,099] [run_local.py[line:193]] [INFO] [��websocket_handler����ʽ���� fa3ba59424e711f091782228ff08ca3c ��ʼ��ʽ����~ ���~~.]
[2025-07-29 15:14:23,099] [run_local.py[line:257]] [INFO] [��start_websocket_server�����������ڽ����������С�]
[2025-07-29 15:38:43,839] [server.py[line:707]] [INFO] [server listening on 127.0.0.1:8766]
[2025-07-29 15:38:43,957] [run_local.py[line:248]] [INFO] [��start_websocket_server��WebSocket���������� ws://127.0.0.1:8766 ����]
[2025-07-29 15:38:43,957] [run_local.py[line:249]] [INFO] [��start_websocket_server���ȴ�������... (���300����û�����ӽ��Զ��ر�)]
[2025-07-29 15:38:44,293] [server.py[line:642]] [INFO] [connection open]
[2025-07-29 15:38:44,337] [run_local.py[line:241]] [INFO] [��start_websocket_server�����յ���һ���ͻ������ӣ����������������С�]
[2025-07-29 15:38:44,349] [run_local.py[line:166]] [INFO] [��websocket_handler��WebSocket client ��ʼ��������: v3720p]
[2025-07-29 15:38:44,350] [run_local.py[line:178]] [INFO] [��websocket_handler���Ѿ�������ʽ����: v3720p]
[2025-07-29 15:38:44,380] [run_local.py[line:193]] [INFO] [��websocket_handler����ʽ���� fa3ba59424e711f091782228ff08ca3c ��ʼ��ʽ����~ ���~~.]
[2025-07-29 15:38:44,384] [run_local.py[line:257]] [INFO] [��start_websocket_server�����������ڽ����������С�]
[2025-07-29 15:38:50,402] [run_local.py[line:223]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������.]
[2025-07-29 15:40:18,728] [server.py[line:707]] [INFO] [server listening on 127.0.0.1:8766]
[2025-07-29 15:40:18,728] [run_local.py[line:248]] [INFO] [��start_websocket_server��WebSocket���������� ws://127.0.0.1:8766 ����]
[2025-07-29 15:40:18,728] [run_local.py[line:249]] [INFO] [��start_websocket_server���ȴ�������... (���300����û�����ӽ��Զ��ر�)]
[2025-07-29 15:40:19,342] [server.py[line:642]] [INFO] [connection open]
[2025-07-29 15:40:19,342] [run_local.py[line:241]] [INFO] [��start_websocket_server�����յ���һ���ͻ������ӣ����������������С�]
[2025-07-29 15:40:19,342] [run_local.py[line:166]] [INFO] [��websocket_handler��WebSocket client ��ʼ��������: v3720p]
[2025-07-29 15:40:19,342] [run_local.py[line:178]] [INFO] [��websocket_handler���Ѿ�������ʽ����: v3720p]
[2025-07-29 15:40:19,342] [run_local.py[line:193]] [INFO] [��websocket_handler����ʽ���� fa3ba59424e711f091782228ff08ca3c ��ʼ��ʽ����~ ���~~.]
[2025-07-29 15:40:19,342] [run_local.py[line:257]] [INFO] [��start_websocket_server�����������ڽ����������С�]
[2025-07-29 15:40:21,767] [run_local.py[line:265]] [INFO] [��start_websocket_server�����������б�ȡ����]
[2025-07-29 15:40:21,767] [run_local.py[line:268]] [INFO] [��start_websocket_server�����ڹرշ�����...]
[2025-07-29 15:40:21,767] [run_local.py[line:223]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������.]
[2025-07-29 15:40:21,767] [run_local.py[line:225]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������.]
[2025-07-29 15:40:21,767] [server.py[line:264]] [INFO] [connection closed]
[2025-07-29 15:40:21,767] [server.py[line:759]] [INFO] [server closing]
[2025-07-29 15:40:21,767] [server.py[line:793]] [INFO] [server closed]
[2025-07-29 15:40:21,767] [run_local.py[line:271]] [INFO] [��start_websocket_server���������ѳɹ��رա�]
[2025-07-29 15:40:21,772] [run_local.py[line:279]] [INFO] [
��⵽�û��жϣ����������ڹرա�]
[2025-07-29 15:42:27,604] [server.py[line:707]] [INFO] [server listening on 127.0.0.1:8766]
[2025-07-29 15:42:27,604] [run_local.py[line:248]] [INFO] [��start_websocket_server��WebSocket���������� ws://127.0.0.1:8766 ����]
[2025-07-29 15:42:27,604] [run_local.py[line:249]] [INFO] [��start_websocket_server���ȴ�������... (���300����û�����ӽ��Զ��ر�)]
[2025-07-29 15:42:33,622] [server.py[line:642]] [INFO] [connection open]
[2025-07-29 15:42:33,622] [run_local.py[line:241]] [INFO] [��start_websocket_server�����յ���һ���ͻ������ӣ����������������С�]
[2025-07-29 15:42:33,622] [run_local.py[line:166]] [INFO] [��websocket_handler��WebSocket client ��ʼ��������: v3720p]
[2025-07-29 15:42:33,622] [run_local.py[line:178]] [INFO] [��websocket_handler���Ѿ�������ʽ����: v3720p]
[2025-07-29 15:42:33,626] [run_local.py[line:193]] [INFO] [��websocket_handler����ʽ���� fa3ba59424e711f091782228ff08ca3c ��ʼ��ʽ����~ ���~~.]
[2025-07-29 15:42:33,626] [run_local.py[line:257]] [INFO] [��start_websocket_server�����������ڽ����������С�]
[2025-07-29 15:42:38,676] [run_local.py[line:223]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������.]
[2025-07-29 15:42:39,426] [server.py[line:264]] [INFO] [connection closed]
[2025-07-29 15:42:39,426] [run_local.py[line:265]] [INFO] [��start_websocket_server�����������б�ȡ����]
[2025-07-29 15:42:39,426] [run_local.py[line:268]] [INFO] [��start_websocket_server�����ڹرշ�����...]
[2025-07-29 15:42:39,428] [server.py[line:759]] [INFO] [server closing]
[2025-07-29 15:42:39,428] [server.py[line:793]] [INFO] [server closed]
[2025-07-29 15:42:39,428] [run_local.py[line:271]] [INFO] [��start_websocket_server���������ѳɹ��رա�]
[2025-07-29 15:42:41,139] [run_local.py[line:279]] [INFO] [
��⵽�û��жϣ����������ڹرա�]
[2025-07-29 16:15:18,066] [server.py[line:707]] [INFO] [server listening on 127.0.0.1:8766]
[2025-07-29 16:15:18,066] [run_local.py[line:248]] [INFO] [��start_websocket_server��WebSocket���������� ws://127.0.0.1:8766 ����]
[2025-07-29 16:15:18,066] [run_local.py[line:249]] [INFO] [��start_websocket_server���ȴ�������... (���300����û�����ӽ��Զ��ر�)]
[2025-07-29 16:15:24,028] [server.py[line:642]] [INFO] [connection open]
[2025-07-29 16:15:24,028] [run_local.py[line:241]] [INFO] [��start_websocket_server�����յ���һ���ͻ������ӣ����������������С�]
[2025-07-29 16:15:24,028] [run_local.py[line:166]] [INFO] [��websocket_handler��WebSocket client ��ʼ��������: v3720p]
[2025-07-29 16:15:24,028] [run_local.py[line:178]] [INFO] [��websocket_handler���Ѿ�������ʽ����: v3720p]
[2025-07-29 16:15:24,028] [run_local.py[line:193]] [INFO] [��websocket_handler����ʽ���� fa3ba59424e711f091782228ff08ca3c ��ʼ��ʽ����~ ���~~.]
[2025-07-29 16:15:24,028] [run_local.py[line:257]] [INFO] [��start_websocket_server�����������ڽ����������С�]
[2025-07-29 16:15:41,243] [run_local.py[line:223]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������.]
[2025-07-29 16:15:41,976] [server.py[line:264]] [INFO] [connection closed]
[2025-07-29 16:15:41,978] [run_local.py[line:265]] [INFO] [��start_websocket_server�����������б�ȡ����]
[2025-07-29 16:15:41,978] [run_local.py[line:268]] [INFO] [��start_websocket_server�����ڹرշ�����...]
[2025-07-29 16:15:41,978] [server.py[line:759]] [INFO] [server closing]
[2025-07-29 16:15:41,978] [server.py[line:793]] [INFO] [server closed]
[2025-07-29 16:15:41,978] [run_local.py[line:271]] [INFO] [��start_websocket_server���������ѳɹ��رա�]
[2025-07-29 16:15:51,981] [run_local.py[line:279]] [INFO] [
��⵽�û��жϣ����������ڹرա�]
[2025-07-29 17:05:44,542] [server.py[line:707]] [INFO] [server listening on 127.0.0.1:8766]
[2025-07-29 17:05:44,544] [run_local.py[line:248]] [INFO] [��start_websocket_server��WebSocket���������� ws://127.0.0.1:8766 ����]
[2025-07-29 17:05:44,546] [run_local.py[line:249]] [INFO] [��start_websocket_server���ȴ�������... (���300����û�����ӽ��Զ��ر�)]
[2025-07-29 17:05:50,359] [server.py[line:642]] [INFO] [connection open]
[2025-07-29 17:05:50,359] [run_local.py[line:241]] [INFO] [��start_websocket_server�����յ���һ���ͻ������ӣ����������������С�]
[2025-07-29 17:05:50,359] [run_local.py[line:166]] [INFO] [��websocket_handler��WebSocket client ��ʼ��������: v3720p]
[2025-07-29 17:05:50,359] [run_local.py[line:178]] [INFO] [��websocket_handler���Ѿ�������ʽ����: v3720p]
[2025-07-29 17:05:50,359] [run_local.py[line:193]] [INFO] [��websocket_handler����ʽ���� fa3ba59424e711f091782228ff08ca3c ��ʼ��ʽ����~ ���~~.]
[2025-07-29 17:05:50,359] [run_local.py[line:257]] [INFO] [��start_websocket_server�����������ڽ����������С�]
[2025-07-29 17:06:10,674] [run_local.py[line:223]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������.]
[2025-07-29 17:06:11,260] [server.py[line:264]] [INFO] [connection closed]
[2025-07-29 17:06:11,261] [run_local.py[line:265]] [INFO] [��start_websocket_server�����������б�ȡ����]
[2025-07-29 17:06:11,261] [run_local.py[line:268]] [INFO] [��start_websocket_server�����ڹرշ�����...]
[2025-07-29 17:06:11,261] [server.py[line:759]] [INFO] [server closing]
[2025-07-29 17:06:11,261] [server.py[line:793]] [INFO] [server closed]
[2025-07-29 17:06:11,261] [run_local.py[line:271]] [INFO] [��start_websocket_server���������ѳɹ��رա�]
[2025-07-29 17:06:21,276] [run_local.py[line:279]] [INFO] [
��⵽�û��жϣ����������ڹرա�]
[2025-07-29 17:08:02,234] [server.py[line:707]] [INFO] [server listening on 127.0.0.1:8766]
[2025-07-29 17:08:02,234] [run_local.py[line:248]] [INFO] [��start_websocket_server��WebSocket���������� ws://127.0.0.1:8766 ����]
[2025-07-29 17:08:02,235] [run_local.py[line:249]] [INFO] [��start_websocket_server���ȴ�������... (���300����û�����ӽ��Զ��ر�)]
[2025-07-29 17:08:05,109] [server.py[line:642]] [INFO] [connection open]
[2025-07-29 17:08:05,109] [run_local.py[line:241]] [INFO] [��start_websocket_server�����յ���һ���ͻ������ӣ����������������С�]
[2025-07-29 17:08:05,109] [run_local.py[line:166]] [INFO] [��websocket_handler��WebSocket client ��ʼ��������: v3720p]
[2025-07-29 17:08:05,110] [run_local.py[line:178]] [INFO] [��websocket_handler���Ѿ�������ʽ����: v3720p]
[2025-07-29 17:08:05,111] [run_local.py[line:193]] [INFO] [��websocket_handler����ʽ���� fa3ba59424e711f091782228ff08ca3c ��ʼ��ʽ����~ ���~~.]
[2025-07-29 17:08:05,111] [run_local.py[line:257]] [INFO] [��start_websocket_server�����������ڽ����������С�]
[2025-07-29 17:09:14,075] [run_local.py[line:265]] [INFO] [��start_websocket_server�����������б�ȡ����]
[2025-07-29 17:09:14,075] [run_local.py[line:268]] [INFO] [��start_websocket_server�����ڹرշ�����...]
[2025-07-29 17:09:14,075] [run_local.py[line:223]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������.]
[2025-07-29 17:09:14,075] [run_local.py[line:225]] [INFO] [��websocket_handler����ʽ���� v3720p ��ʽ�������.]
[2025-07-29 17:09:14,075] [server.py[line:264]] [INFO] [connection closed]
[2025-07-29 17:09:14,080] [server.py[line:759]] [INFO] [server closing]
[2025-07-29 17:09:14,080] [server.py[line:793]] [INFO] [server closed]
[2025-07-29 17:09:14,080] [run_local.py[line:271]] [INFO] [��start_websocket_server���������ѳɹ��رա�]
[2025-07-29 17:09:14,080] [run_local.py[line:279]] [INFO] [
��⵽�û��жϣ����������ڹرա�]
[2025-07-29 17:19:19,914] [server.py[line:707]] [INFO] [server listening on 127.0.0.1:8766]
[2025-07-29 17:19:19,916] [run_local.py[line:248]] [INFO] [��start_websocket_server��WebSocket���������� ws://127.0.0.1:8766 ����]
[2025-07-29 17:19:19,916] [run_local.py[line:249]] [INFO] [��start_websocket_server���ȴ�������... (���300����û�����ӽ��Զ��ر�)]
[2025-07-29 17:19:23,573] [run_local.py[line:265]] [INFO] [��start_websocket_server�����������б�ȡ����]
[2025-07-29 17:19:23,574] [run_local.py[line:268]] [INFO] [��start_websocket_server�����ڹرշ�����...]
[2025-07-29 17:19:23,574] [server.py[line:759]] [INFO] [server closing]
[2025-07-29 17:19:23,574] [server.py[line:793]] [INFO] [server closed]
[2025-07-29 17:19:23,574] [run_local.py[line:271]] [INFO] [��start_websocket_server���������ѳɹ��رա�]
[2025-07-29 17:19:23,575] [run_local.py[line:279]] [INFO] [
��⵽�û��жϣ����������ڹرա�]
