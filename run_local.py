# -*-coding: utf-8 -*-
# @Time :2025-04-02
# <AUTHOR> lishengjing
# @Email : <EMAIL>
# @File : run_local.py

import os

import time
import traceback
from enum import Enum

from y_utils.logger import logger

from y_utils.config import GlobalConfig
# from service.trans_dh_service import TransDhTask, Status
from service.stream_dh_service import TransDhTask, Status

import json
import threading
import gc
import cv2
import asyncio
import websockets
import yaml

class EasyResponse:
    def __init__(
            self,
            code,
            success,
            msg, data: dict):
        self.code = code
        self.success = success
        self.msg = msg
        self.data = data

class Var():
    def __init__(self):
        pass

class ResponseCode(Enum):
    system_error = [9999, '系统异常']
    success = [10000, '成功']
    busy = [10001, '忙碌中']
    error1 = [10002, '参数异常']
    error2 = [10003, '获取锁异常']
    error3 = [10004, '任务不存在']


def easy_submit(request_data):

    try:
        _audio_url = request_data['audio_url']
        _video_url = request_data['video_url']
        _code = request_data['code']
        _watermark_switch = request_data['watermark_switch']
        _speaker_id = request_data['speaker_id']
        _is_train = request_data['is_train']
        _digital_auth = request_data['digital_auth']
        _chaofen = request_data['chaofen']
        _pn = request_data['pn']
        _is_stream = request_data.get('is_stream',False)                            # 流式
        stream_url = request_data.get('stream_url', None)                          # job_params参数
        
        if TransDhTask.instance().run_flag:
            return False,json.dumps(
                EasyResponse(ResponseCode.busy.value[0], True, ResponseCode.busy.value[1], {}),
                default=lambda obj: obj.__dict__,
                sort_keys=True, ensure_ascii=False,
                indent=4)
        else:
            try:
                TransDhTask.instance().run_lock.acquire()
                TransDhTask.instance().run_flag = True
                TransDhTask.instance().task_dic[_code] = (Status.run, 0, '', '')
            except Exception as e:
                traceback.print_exc()
                return False,json.dumps(
                    EasyResponse(ResponseCode.error2.value[0], False, ResponseCode.error2.value[1], {}),
                    default=lambda obj: obj.__dict__,
                    sort_keys=True, ensure_ascii=False,
                    indent=4)
            finally:
                TransDhTask.instance().run_lock.release()

            threading.Thread(
                target=TransDhTask.instance().work,
                args=(_audio_url, _video_url, _code, _watermark_switch,_speaker_id, _is_train, _digital_auth, _chaofen, _pn,_is_stream, stream_url)).start()
            return True,json.dumps(
                EasyResponse(ResponseCode.success.value[0], True, ResponseCode.success.value[0], {}),
                default=lambda obj: obj.__dict__,
                sort_keys=True, ensure_ascii=False,
                indent=4)
    except Exception as e:
        traceback.print_exc()
        return False,json.dumps(
            EasyResponse(ResponseCode.system_error.value[0], False, ResponseCode.system_error.value[1], {}),
            default=lambda obj: obj.__dict__,
            sort_keys=True, ensure_ascii=False,
            indent=4)
    finally:
        gc.collect()

def easy_query(get_data):
    # 任务查询
    finish_flag = False
    result_data = None
    response = Var()
    while not finish_flag:
        del_flag = False
        try:
            _code = get_data.get('code')
            if _code in TransDhTask.instance().task_dic:  #任务存在
                _status, _progress, _result, _msg = TransDhTask.instance().task_dic[_code]
                _msg = { 'code': _code,'status': _status.value,'progress': _progress,'result': _result,'msg': _msg}
                if _status == Status.run:  #任务运行中
                    time.sleep(2)  # 2秒查询一次
                elif _status == Status.success:  #任务成功
                    del_flag = True
                    response=EasyResponse(ResponseCode.success.value[0], True, '',_msg) 
                    finish_flag = True
                elif _status == Status.error:  #任务失败
                    del_flag = True
                    response=EasyResponse(ResponseCode.success.value[0], True, '',_msg)
                    finish_flag = True
            else:  # 任务不存在
                response=EasyResponse(ResponseCode.error3.value[0], True, ResponseCode.error3.value[1], {})
                finish_flag = True
        except Exception as e:   # 系统异常
            traceback.print_exc()
            response=EasyResponse(ResponseCode.system_error.value[0], False, ResponseCode.system_error.value[1], {})
            finish_flag = True
        finally:
            if del_flag:
                try:
                    TransDhTask.instance().run_lock.acquire()
                    del TransDhTask.instance().task_dic[_code]
                    TransDhTask.instance().run_lock.release()
                except Exception as e:
                    traceback.print_exc()
                    response=EasyResponse(ResponseCode.error3.value[0], True, ResponseCode.error3.value[1], {})
    try:
        result_data = json.dumps(response, default=lambda obj: obj.__dict__, sort_keys=True, ensure_ascii=False, indent=4)
    except Exception as e:
        traceback.print_exc()

    return result_data


async def websocket_handler(websocket, path):
    """
    处理单个WebSocket连接。
    它会连接到一个由 `easy_submit` 预先启动的流式任务。
    """
    code = None
    audio_queue = None
    try:
        # 1. 从路径中提取任务代码 (e.g., /task123 -> task123)
        spk_id = path.strip('/')
        if not spk_id:
            logger.error("WebSocket connection rejected: No task code provided in path.")
            await websocket.close(1011, "Task code is required in the URL path.")
            return

        logger.info(f"【websocket_handler】WebSocket client 开始连接任务: {spk_id}")

        # 2. 查找与任务代码关联的音频队列（带重试）
        audio_queue = None
        for i in range(5):  # 重试5次
            # 检查任务是否存在且正在运行
            try:
                code = TransDhTask.instance().spk_2_code[spk_id]
                if code in TransDhTask.instance().task_dic and TransDhTask.instance().task_dic[code][0] == Status.run:
                    # 检查流队列是否已为此任务创建
                    audio_queue = TransDhTask.instance().audio_stream_queue
                    if audio_queue:
                        logger.info(f"【websocket_handler】已经建立流式任务: {spk_id}")
                        break
            except Exception as e:  # 获取任务代码异常,有可能还没有更新参数
                logger.error(f"【websocket_handler】获取任务代码异常: {e}")
                if i < 4: # 在最后一次尝试后不再等待
                    logger.warning(f"【websocket_handler】数字人任务 '{code}' 未就绪. 重试 {i+1}/5 等待5秒.")
                    await asyncio.sleep(10)
        
        if not audio_queue:
            logger.error(f"【websocket_handler】啊哈？试了五次都连接不到流式任务啊大人，您是不是在耍我？: '{spk_id}'")
            await websocket.close(1011, f"【websocket_handler】啊哈？试了五次都连接不到流式任务啊大人，您是不是在耍我？: '{spk_id}'")
            return

        # 3. 发送 "start" 信号
        # audio_queue.put(('start', None))
        logger.info(f"【websocket_handler】流式任务 {code} 开始流式传输~ 起飞~~.")

        # 4. 接收音频数据并放入队列
        async for message_json in websocket:
            message = json.loads(message_json)
            # 假设消息是原始音频字节
            # print("【websocket_handler】发送音频数据",len(message))
            
            _status = message.get('status')
            _data = message.get('data')
            
            # 如果是音频数据，需要从base64解码为字节
            if _status == 'data' and _data is not None:
                import base64
                try:
                    _data = base64.b64decode(_data)
                except Exception as e:
                    logger.error(f"【websocket_handler】base64解码失败: {e}")
                    continue
            
            audio_queue.put((_status,_data))

    except websockets.exceptions.ConnectionClosed as e:
        logger.info(f"【websocket_handler】流式任务 {spk_id} 流式传输结束: {e}")
    except Exception as e:
        logger.error(f"【websocket_handler】流式任务 {spk_id} 流式传输异常: {e}")
        traceback.print_exc()
    finally:
        # 5. 发送流结束信号并清理
        if audio_queue:
            logger.info(f"【websocket_handler】流式任务 {spk_id} 流式传输结束.")
            audio_queue.put(('end', None))  # 'end'作为流结束的标志
        logger.info(f"【websocket_handler】流式任务 {spk_id} 流式传输结束.")


def start_websocket_server(ip, port, timeout):
    """
    启动WebSocket服务器。
    如果服务器在指定时间内没有收到任何连接，它将自动关闭。
    一旦收到第一个连接，它将持续运行。
    """
    async def main():
        # 创建一个事件，用于在第一个客户端连接时发出信号
        first_connection_event = asyncio.Event()

        async def handler_wrapper(websocket, path):
            """包装原始处理程序以检测第一个连接。"""
            if not first_connection_event.is_set():
                logger.info("【start_websocket_server】接收到第一个客户端连接，服务器将持续运行。")
                first_connection_event.set()
            # 调用原始的websocket_handler
            await websocket_handler(websocket, path)

        # 启动WebSocket服务器
        server = await websockets.serve(handler_wrapper, ip, port)
        logger.info(f"【start_websocket_server】WebSocket服务器已在 ws://{ip}:{port} 启动")
        logger.info(f"【start_websocket_server】等待连接中... (如果{timeout}秒内没有连接将自动关闭)")

        try:
            # 等待第一个连接，超时时间为5分钟 (300秒)
            await asyncio.wait_for(first_connection_event.wait(), timeout=float(timeout))
            
            # 收到连接后，让服务器一直运行
            # 等待一个永远不会完成的Future是实现这一目标的标准方法
            logger.info("【start_websocket_server】服务器现在将无限期运行。")
            await asyncio.Future()

        except asyncio.TimeoutError:
            logger.warning(f"【start_websocket_server】超时:{timeout}秒内没有收到连接。服务器将关闭。")
        
        except asyncio.CancelledError:
            # 在程序关闭期间（例如Ctrl+C），任务可能会被取消
            logger.info("【start_websocket_server】服务器运行被取消。")

        finally:
            logger.info("【start_websocket_server】正在关闭服务器...")
            server.close()
            await server.wait_closed()
            logger.info("【start_websocket_server】服务器已成功关闭。")

    # 使用 asyncio.run 运行主协程，它会处理事件循环的启动和关闭
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        # asyncio.run 会捕捉KeyboardInterrupt并取消任务，
        # 所以这里的代码块可能不会被执行，但为了清晰起见保留它。
        logger.info("\n检测到用户中断，服务器正在关闭。")



if __name__ == '__main__':
    config_path = 'service/server_config.yaml'
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        # Navigate to the tts_server configuration
        server_config = config.get('websocket_server', {}).get('tts_server', {})
        ip = server_config.get('ip', '127.0.0.1')
        port = server_config.get('port', 8765)
        timeout = server_config.get('timeout', 300)
    except (FileNotFoundError, yaml.YAMLError) as e:
        logger.warning(f"无法加载或解析 {config_path}: {e}. 使用默认值。")
        ip = "127.0.0.1"
        port = 8765
        timeout = 300
    
    start_websocket_server(ip, port, timeout)

