import os,sys
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)
from cy_app import *

if __name__ == "__main__":
    event = multiprocessing.Event()

    fastapi_process = multiprocessing.Process(target=start_service, args=(event,))
    gradio_process = multiprocessing.Process(target=start_gradio, args=(event,))

    fastapi_process.start()
    gradio_process.start()

    fastapi_process.join()
    gradio_process.join()