# uncompyle6 version 3.9.2
# Python bytecode version base 3.8.0 (3413)
# Decompiled from: Python 3.10.9 | packaged by Anaconda, Inc. | (main, Mar  1 2023, 18:18:15) [MSC v.1916 64 bit (AMD64)]
# Embedded file name: /code/face_detect_utils/face_detect.py
# Compiled at: 2024-03-28 14:31:42
# Size of source mod 2**32: 5003 bytes
import numpy as np, cv2
from .scrfd import SCRFD
import onnxruntime as ort

class FaceDetect:

    def __init__(self, mode='scrfd_500m', cpu=False, model_path='./resources/'):
        if "scrfd" in mode:
            if mode == "scrfd_500m":
                scrfd_model_path = model_path + "scrfd_500m_bnkps_shape640x640.onnx"
            else:
                if mode == "scrfd_10g":
                    scrfd_model_path = model_path + "scrfd_10g_bnkps.onnx"
            self.det_model = SCRFD(scrfd_model_path, cpu=cpu)
            self.det_model.prepare(ctx_id=0, input_size=(640, 640))

    def get_bboxes(self, image, thresh=0.5, max_num=0):
        if type(image) == str:
            image = cv2.cvtColor(cv2.imread(image), cv2.COLOR_BGR2RGB)
        else:
            if type(image) == np.ndarray:
                pass
        bboxes_, kpss_ = self.det_model.detect(image, thresh=thresh, max_num=max_num, metric="max")
        return (bboxes_, kpss_)


class pfpld:

    def __init__(self, cpu=False, model_path='./resources'):
        onnx_path = f"{model_path}/pfpld_robust_sim_bs1_8003.onnx"
        try:
            self.ort_session = ort.InferenceSession(onnx_path, providers=(["CPUExecutionProvider"] if cpu else ["CUDAExecutionProvider"]))
        except Exception as e:
            try:
                raise e("load onnx failed")
            finally:
                e = None
                del e

        else:
            self.input_name = self.ort_session.get_inputs()[0].name

    def forward(self, input):
        size = input.shape
        ort_inputs = {self.input_name: (cv2.resize(input, (112, 112)) / 255).astype(np.float32).transpose(2, 0, 1)[None]}
        pred = self.ort_session.run(None, ort_inputs)
        pred = convert98to68(pred[1])
        return pred.reshape(-1, 68, 2) * size[:2][::-1]


def convert98to68(list_info):
    points = list_info[0, 0:196]
    info_68 = []
    for j in range(17):
        x = points[j * 2 * 2 + 0]
        y = points[j * 2 * 2 + 1]
        info_68.append(x)
        info_68.append(y)

    for j in range(33, 38):
        x = points[j * 2 + 0]
        y = points[j * 2 + 1]
        info_68.append(x)
        info_68.append(y)

    for j in range(42, 47):
        x = points[j * 2 + 0]
        y = points[j * 2 + 1]
        info_68.append(x)
        info_68.append(y)

    for j in range(51, 61):
        x = points[j * 2 + 0]
        y = points[j * 2 + 1]
        info_68.append(x)
        info_68.append(y)

    point_38_x = (float(points[120]) + float(points[124])) / 2.0
    point_38_y = (float(points[121]) + float(points[125])) / 2.0
    point_39_x = (float(points[124]) + float(points[128])) / 2.0
    point_39_y = (float(points[125]) + float(points[129])) / 2.0
    point_41_x = (float(points[128]) + float(points[132])) / 2.0
    point_41_y = (float(points[129]) + float(points[133])) / 2.0
    point_42_x = (float(points[120]) + float(points[132])) / 2.0
    point_42_y = (float(points[121]) + float(points[133])) / 2.0
    point_44_x = (float(points[136]) + float(points[140])) / 2.0
    point_44_y = (float(points[137]) + float(points[141])) / 2.0
    point_45_x = (float(points[140]) + float(points[144])) / 2.0
    point_45_y = (float(points[141]) + float(points[145])) / 2.0
    point_47_x = (float(points[144]) + float(points[148])) / 2.0
    point_47_y = (float(points[145]) + float(points[149])) / 2.0
    point_48_x = (float(points[136]) + float(points[148])) / 2.0
    point_48_y = (float(points[137]) + float(points[149])) / 2.0
    info_68.append(point_38_x)
    info_68.append(point_38_y)
    info_68.append(point_39_x)
    info_68.append(point_39_y)
    info_68.append(points[128])
    info_68.append(points[129])
    info_68.append(point_41_x)
    info_68.append(point_41_y)
    info_68.append(point_42_x)
    info_68.append(point_42_y)
    info_68.append(points[136])
    info_68.append(points[137])
    info_68.append(point_44_x)
    info_68.append(point_44_y)
    info_68.append(point_45_x)
    info_68.append(point_45_y)
    info_68.append(points[144])
    info_68.append(points[145])
    info_68.append(point_47_x)
    info_68.append(point_47_y)
    info_68.append(point_48_x)
    info_68.append(point_48_y)
    for j in range(76, 96):
        x = points[j * 2 + 0]
        y = points[j * 2 + 1]
        info_68.append(x)
        info_68.append(y)

    for j in range(len(list_info[196:])):
        info_68.append(list_info[196 + j])

    return np.array(info_68)
