# uncompyle6 version 3.9.2
# Python bytecode version base 3.8.0 (3413)
# Decompiled from: Python 3.10.9 | packaged by Anaconda, Inc. | (main, Mar  1 2023, 18:18:15) [MSC v.1916 64 bit (AMD64)]
# Embedded file name: /code/face_detect_utils/head_pose.py
# Compiled at: 2024-03-27 17:14:52
# Size of source mod 2**32: 3243 bytes
import numpy as np
import cv2
import onnxruntime as ort

class Headpose:

    def __init__(self, cpu=False, onnx_path='./resources/model_float32.onnx'):
        self.idx_tensor_yaw = [np.array(idx, dtype=np.float32) for idx in range(120)]
        self.idx_tensor = [np.array(idx, dtype=np.float32) for idx in range(66)]
        self.whenet_H = 224
        self.whenet_W = 224
        self.mean = [0.485, 0.456, 0.406]
        self.std = [0.229, 0.224, 0.225]


        # 初始化ONNX运行时
        if cpu:
            providers = ['CPUExecutionProvider']
        else:
            providers = ['CUDAExecutionProvider', 'CPUExecutionProvider']
            
        # 创建ONNX会话
        self.whenet_session = ort.InferenceSession(onnx_path, providers=providers)
        self.whenet_input_name = self.whenet_session.get_inputs()[0].name
        self.whenet_output_names = [output.name for output in self.whenet_session.get_outputs()]
        self.whenet_output_shapes = [output.shape for output in self.whenet_session.get_outputs()]
        assert self.whenet_output_shapes[0] == [1, 120]
        assert self.whenet_output_shapes[1] == [1, 66]
        assert self.whenet_output_shapes[2] == [1, 66]

    def softmax(self, x):
        x -= np.max(x, axis=1, keepdims=True)
        a = np.exp(x)
        b = np.sum(np.exp(x), axis=1, keepdims=True)
        return a / b

    def get_head_pose(self, image):
        croped_resized_frame = cv2.resize(image, (self.whenet_W, self.whenet_H))
        rgb = croped_resized_frame[..., ::-1]
        rgb = (rgb / 255.0 - self.mean) / self.std
        chw = np.transpose(rgb,(2, 0, 1))
        nchw = np.asarray(chw[np.newaxis, :, :, :], dtype=np.float32)
        yaw, roll, pitch = self.whenet_session.run(output_names=self.whenet_output_names,
          input_feed={self.whenet_input_name: nchw})
        yaw = np.sum(self.softmax(yaw) * self.idx_tensor_yaw, axis=1) * 3 - 180
        pitch = np.sum(self.softmax(pitch) * self.idx_tensor, axis=1) * 3 - 99
        roll = np.sum(self.softmax(roll) * self.idx_tensor, axis=1) * 3 - 99
        return (pitch, roll, yaw)


if __name__ == "__main__":
    import os
    hp = Headpose(cpu=False, onnx_path="/home/<USER>/my_code/HeadPoseEstimation-WHENet-yolov4-onnx-openvino/saved_model_224x224/model_float32.onnx")
    base_dir = "/home/<USER>/dataset/项目/测试数据/测试模板/测试缩放/zhengyuqi/532_dlib_crop"
    for img in sorted(os.listdir(base_dir)):
        img = cv2.imread(os.path.join(base_dir, img))
        print(hp.get_head_pose(img))
