# 数字人推流功能实现说明

## 功能概述

本次实现了数字人流式输入输出的推流功能，主要包括：

1. **AudioExtractor音频特征和数据同步传输**：修改AudioExtractor，在传输音频特征的同时传输原始音频数据
2. **RTSPStreamer推流类实现**：实现了基于FFmpeg的RTSP音视频同步推流功能
3. **VideoEncoder推流集成**：在VideoEncoder中集成推流功能，支持实时音视频推流输出

## 实现细节

### 1. AudioExtractor修改

**文件**: `service/stream_dh_service.py`

**修改内容**：
- 修改`run_flow_audio_with_wenet`方法，同时传递音频特征和原始音频数据
- 使用`tolist()`方法解决ndarray在进程间通信的序列化问题
- 更新数据传输格式：`(status, features, audio_data)`

```python
# 同时传递特征和原始音频数据
audio_data = audio_segment.tolist()  # 将音频数据转换为list进行序列化
self.feature_queue.put(("data", features.tolist(), audio_data))
```

### 2. RTSPStreamer推流类

**文件**: `service/stream_dh_service.py`

**功能特性**：
- 基于FFmpeg的RTSP音视频同步推流
- 支持实时视频帧和音频数据推流
- 自动处理视频尺寸调整和音频缓冲区管理
- 音视频同步机制，确保音画对齐
- 优化的编码参数（ultrafast预设，zerolatency调优）
- 完善的错误处理和资源清理

```python
class RTSPStreamer:
    def __init__(self, stream_url, width=1920, height=1080, fps=25, audio_sample_rate=16000)
    def start(self)  # 启动推流
    def write_frame(self, frame, batch_audio_data=None)  # 写入视频帧和音频数据
    def stop()  # 停止推流
```

**音视频同步特性**：
- 自动计算每帧对应的音频采样数（16000Hz / 25fps = 640样本/帧）
- 音频缓冲区管理，确保音视频数据对齐
- 静音填充机制，处理音频不足的情况

### 3. VideoEncoder推流集成

**文件**: `service/stream_dh_service.py`

**集成功能**：
- 在初始化时根据`stream_url`参数创建推流器
- 在处理每帧视频时同时推流视频和音频数据
- 任务结束时自动清理推流资源
- 流式模式下跳过本地视频文件写入

```python
# 推流（如果启用）
if self.rtmp_streamer and self.rtmp_streamer.is_running:
    self.rtmp_streamer.write_frame(image, batch_audio_data)
else:
    # 写入视频文件
    self.video_writer.write(image)
```

### 4. 参数传递链路

**完整传递链路**：
1. `worker.py` → `job_params['stream_url']`
2. `worker_stream_infer.py` → `request_data['stream_url']`
3. `run_local.py` → `easy_submit(stream_url)`
4. `service/stream_dh_service.py` → `work(stream_url=stream_url)`
5. `task_info['stream_url']` → `VideoEncoder._initialize_task()`

## 使用方式

### 1. 配置推流URL

在`job_params`中添加`stream_url`参数：

```python
job_params = {
    'algorithm': 'digitalhuman2dtrainv2',
    'video_url': 'video.mp4',
    'audio_url': 'audio.wav',
    'is_stream': True,
    'speaker_id': 'speaker_id',
    'task_type': 'stream',
    'stream_url': 'rtsp://localhost:8554/live/test'  # RTSP推流地址
}
```

### 2. 运行测试

使用提供的测试脚本：

```bash
python test_streaming.py
```

### 3. 验证推流

可以使用以下工具验证推流：

```bash
# 使用ffplay观看推流
ffplay rtsp://localhost:8554/live/test

# 使用VLC播放器
# 打开网络串流：rtsp://localhost:8554/live/test
```

## 技术特点

### 1. 进程间通信优化
- 使用`tolist()`解决numpy数组序列化问题
- 保持向后兼容的数据格式
- 批次音频数据传输

### 2. 音视频同步推流
- RTSP协议支持，更适合实时流媒体传输
- 音视频数据同步处理
- 自动音频缓冲区管理
- 静音填充机制

### 3. 推流性能优化
- 使用FFmpeg的`ultrafast`预设和`zerolatency`调优
- 最小化编码延迟
- TCP传输确保数据完整性

### 4. 错误处理
- 完善的异常处理机制
- 自动资源清理
- 推流失败时的容错处理

### 5. 扩展性
- 模块化设计，易于扩展其他推流协议
- 灵活的参数配置

## 依赖要求

- FFmpeg（支持H.264编码、AAC音频编码和RTSP输出）
- OpenCV Python
- numpy

## 注意事项

1. **FFmpeg要求**：确保系统已安装FFmpeg并支持libx264编码器和AAC音频编码器
2. **网络要求**：推流需要稳定的网络连接
3. **资源消耗**：推流会增加CPU和网络带宽消耗
4. **RTSP服务器**：需要配置RTSP服务器（如EasyDarwin、Live555等）
5. **音视频同步**：系统会自动处理音视频同步，确保音画对齐

## 故障排除

### 1. 推流启动失败
- 检查FFmpeg是否正确安装并支持RTSP输出
- 验证推流URL格式是否正确
- 确认RTSP服务器是否可达

### 2. 推流中断
- 检查网络连接稳定性
- 查看FFmpeg错误日志
- 验证服务器是否支持连接数限制

### 3. 音视频不同步
- 检查音频采样率配置（默认16kHz）
- 确认帧率设置正确（默认25fps）
- 查看音频缓冲区状态日志

### 4. 延迟过高
- 调整FFmpeg编码参数
- 检查网络带宽
- 优化服务器配置

## 更新历史

- **2025-01-XX**: 初始实现推流功能
- 支持AudioExtractor音频数据同步传输
- 实现RTSPStreamer音视频同步推流类
- 集成VideoEncoder推流功能
- 完成参数传递链路
- 添加音视频同步机制 