#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试AudioExtractor核心逻辑
专注于流式处理和重叠窗口逻辑，不依赖wenet模型
"""

import os
import sys
import time
import queue
import threading
import numpy as np
from loguru import logger
import librosa
import pickle
import torch

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入需要的函数
from wenet.tools._extract_feats import wav2mfcc_v2


def logger_error(e):
    """错误日志记录"""
    import traceback
    logger.error(f"错误: {e}")
    logger.error(f"堆栈跟踪: {traceback.format_exc()}")


def save_debug_data(data, filename):
    """保存调试数据"""
    try:
        with open(filename, 'wb') as f:
            pickle.dump(data, f)
        logger.info(f"调试数据已保存到: {filename}")
    except Exception as e:
        logger.error(f"保存调试数据失败: {e}")


def load_debug_data(filename):
    """加载调试数据"""
    try:
        with open(filename, 'rb') as f:
            data = pickle.load(f)
        logger.info(f"调试数据已加载: {filename}")
        return data
    except Exception as e:
        logger.error(f"加载调试数据失败: {e}")
        return None


class BatchProcessor:
    """
    批处理器，模拟get_aud_feat1的处理逻辑
    """
    
    def __init__(self, fps=25):
        self.fps = fps
        self.sample_rate = 16000
        
        # Mel特征提取参数
        self.mel_params = {
            'sample_rate': 16000,
            'preemphasis': 0.97,
            'n_fft': 1024,
            'hop_length': 160,
            'win_length': 800,
            'num_mels': 80,
            'n_mfcc': 13,
            'window': 'hann',
            'fmin': 0.0,
            'fmax': 8000.0,
            'ref_db': 20,
            'min_db': -80.0,
            'center': True
        }
    
    def process_audio(self, audio_data):
        """
        批处理音频数据
        """
        logger.info("开始批处理音频...")
        
        # 添加静音填充
        zero_padding = np.zeros(6400)  # 0.4秒静音
        audio_padded = np.concatenate([zero_padding, audio_data, zero_padding])
        
        # 提取mel特征
        mel_features = self.extract_mel_features(audio_padded)
        
        if mel_features is not None:
            logger.info(f"批处理mel特征形状: {mel_features.shape}")
            
            # 使用真实wenet模型处理mel特征
            wenet_features = self.process_mel_with_wenet(mel_features)
            logger.info(f"真实wenet特征形状: {wenet_features.shape}")
            
            # 生成index结果
            indexs = self.generate_indexs(wenet_features)
            logger.info(f"生成index数量: {len(indexs)}")
            
            return {
                'audio_padded': audio_padded,
                'mel_features': mel_features,
                'wenet_features': wenet_features,
                'indexs': indexs
            }
        else:
            logger.error("批处理失败: mel特征提取失败")
            return None
    
    def extract_mel_features(self, audio_data):
        """提取mel特征"""
        try:
            mel_features, _ = wav2mfcc_v2(
                audio_data,
                sr=self.mel_params["sample_rate"],
                n_mfcc=self.mel_params["n_mfcc"],
                n_fft=self.mel_params["n_fft"],
                hop_len=self.mel_params["hop_length"],
                win_len=self.mel_params["win_length"],
                window=self.mel_params["window"],
                num_mels=self.mel_params["num_mels"],
                center=self.mel_params["center"]
            )
            return mel_features
        except Exception as e:
            logger.error(f"mel特征提取失败: {e}")
            return None
    
    def process_mel_with_wenet(self, mel_features):
        """使用真实的wenet模型处理mel特征"""
        try:
            import torch
            device = "cuda" if torch.cuda.is_available() else "cpu"

            # 加载wenet模型（如果还没有加载）
            if not hasattr(self, 'wenet_model'):
                logger.info("加载wenet模型...")
                from wenet.compute_ctc_att_bnf import load_ppg_model
                self.wenet_model = load_ppg_model(
                    "wenet/examples/aishell/aidata/conf/train_conformer_multi_cn.yaml",
                    "wenet/examples/aishell/aidata/exp/conformer/wenetmodel.pt",
                    device
                )
                logger.info("wenet模型加载完成")

            # 将mel特征转换为tensor
            mel_tensor = torch.from_numpy(mel_features).float().to(device).unsqueeze(0)
            mel_length = torch.LongTensor([mel_features.shape[0]]).to(device)

            # 使用wenet模型处理mel特征
            with torch.no_grad():
                wenet_features = self.wenet_model(mel_tensor, mel_length)

            # 转换为numpy数组
            wenet_features_np = wenet_features.squeeze(0).cpu().numpy()

            logger.info(f"真实wenet特征提取成功: mel形状={mel_features.shape}, wenet形状={wenet_features_np.shape}")
            return wenet_features_np

        except Exception as e:
            logger.error(f"真实wenet特征提取失败: {e}")
            # 如果失败，返回随机数据作为备用
            logger.warning("使用随机数据作为备用")
            return np.random.randn(mel_features.shape[0], 256)

    def generate_indexs(self, wenet_features):
        """生成index结果"""
        time_duration = wenet_features.shape[0] / (self.sample_rate / self.mel_params["hop_length"])
        cnts = range(int(time_duration * self.fps))
        win_size = 20
        indexs = []

        for cnt in cnts:
            c_count = int(cnt / cnts[-1] * (wenet_features.shape[0] - win_size)) + win_size // 2
            index_slice = wenet_features[(c_count - win_size // 2):c_count + win_size // 2, ...]
            indexs.append(index_slice)

        return indexs


class StreamingProcessor:
    """
    流式处理器，模拟AudioExtractor的处理逻辑
    """
    
    def __init__(self, fps=25):
        self.fps = fps
        self.sample_rate = 16000
        
        # Mel特征提取参数
        self.mel_params = {
            'sample_rate': 16000,
            'preemphasis': 0.97,
            'n_fft': 1024,
            'hop_length': 160,
            'win_length': 800,
            'num_mels': 80,
            'n_mfcc': 13,
            'window': 'hann',
            'fmin': 0.0,
            'fmax': 8000.0,
            'ref_db': 20,
            'min_db': -80.0,
            'center': True
        }
        
        # 重叠窗口参数
        self.window_size = 3200  # 200ms窗口
        self.hop_size = 640  # 40ms跳跃
        self.context_frames = 10  # 上下文帧数
        
        # 缓冲区
        self.overlap_buffer = np.array([])
        self.valid_features_buffer = []
        self.valid_audio_buffer = []
        self.is_first_data = True
        
        # 结果收集
        self.all_mel_features = []
        self.all_wenet_features = []
        self.all_indexs = []

        # 加载wenet模型
        logger.info("流式处理器加载wenet模型...")
        import torch
        device = "cuda" if torch.cuda.is_available() else "cpu"
        from wenet.compute_ctc_att_bnf import load_ppg_model
        self.wenet_model = load_ppg_model(
            "wenet/examples/aishell/aidata/conf/train_conformer_multi_cn.yaml",
            "wenet/examples/aishell/aidata/exp/conformer/wenetmodel.pt",
            device
        )
        logger.info("流式处理器wenet模型加载完成")
    
    def extract_mel_features(self, audio_data):
        """提取mel特征"""
        try:
            mel_features, _ = wav2mfcc_v2(
                audio_data,
                sr=self.mel_params["sample_rate"],
                n_mfcc=self.mel_params["n_mfcc"],
                n_fft=self.mel_params["n_fft"],
                hop_len=self.mel_params["hop_length"],
                win_len=self.mel_params["win_length"],
                window=self.mel_params["window"],
                num_mels=self.mel_params["num_mels"],
                center=self.mel_params["center"]
            )
            return mel_features
        except Exception as e:
            logger.error(f"mel特征提取失败: {e}")
            return None

    def process_mel_with_wenet(self, mel_features):
        """使用真实的wenet模型处理mel特征"""

        device = "cuda" if torch.cuda.is_available() else "cpu"

        # 将mel特征转换为tensor
        mel_tensor = torch.from_numpy(mel_features).float().to(device).unsqueeze(0)
        mel_length = torch.LongTensor([mel_features.shape[0]]).to(device)

        # 使用wenet模型处理mel特征
        with torch.no_grad():
            wenet_features = self.wenet_model(mel_tensor, mel_length)

        # 转换为numpy数组
        wenet_features_np = wenet_features.squeeze(0).cpu().numpy()

        return wenet_features_np

   
    
    def process_audio_chunk(self, audio_chunk):
        """处理音频块"""
        # 如果是第一次处理，添加静音填充
        if self.is_first_data:
            logger.info("第一次处理音频，添加静音填充")
            zero_padding = np.zeros(6400)  # 0.4秒静音
            audio_chunk = np.concatenate([zero_padding, audio_chunk])
            self.is_first_data = False
        
        # 添加到重叠缓冲区
        self.overlap_buffer = np.concatenate([self.overlap_buffer, audio_chunk])
        
        # 处理重叠窗口
        self._process_overlap_windows()
    
    def _process_overlap_windows(self):
        """处理重叠窗口"""
        while len(self.overlap_buffer) >= self.window_size:
            # 提取窗口音频
            window_audio = self.overlap_buffer[:self.window_size]
            
            # 提取mel特征
            mel_features = self.extract_mel_features(window_audio)
            
            if mel_features is not None:
                # 去除边界效应
                if mel_features.shape[0] > 2 * self.context_frames:
                    valid_mel = mel_features[self.context_frames:-self.context_frames]
                    
                    # 计算对应的音频数据
                    samples_per_frame = len(window_audio) / mel_features.shape[0]
                    start_audio_idx = int(self.context_frames * samples_per_frame)
                    end_audio_idx = int((mel_features.shape[0] - self.context_frames) * samples_per_frame)
                    valid_audio = window_audio[start_audio_idx:end_audio_idx]
                    
                    # 保存有效特征
                    self.valid_features_buffer.append(valid_mel)
                    self.valid_audio_buffer.append(valid_audio)
                    
                    # 收集mel特征用于调试
                    self.all_mel_features.append(valid_mel)
                    
                    logger.info(f"处理窗口: 原始={mel_features.shape[0]}帧, 有效={valid_mel.shape[0]}帧")
                else:
                    logger.warning(f"窗口mel特征帧数不足: {mel_features.shape[0]} < {2 * self.context_frames}")
            
            # 滑动窗口
            self.overlap_buffer = self.overlap_buffer[self.hop_size:]
            
            # 检查是否可以生成index
            self._check_and_generate_index()
    
    def _check_and_generate_index(self):
        """检查并生成index"""
        # 计算总帧数
        total_frames = sum(mel.shape[0] for mel in self.valid_features_buffer)
        
        # 当有足够特征时，生成20帧的index
        win_size = 20
        while total_frames >= win_size:
            if len(self.valid_features_buffer) > 0:
                # 连接特征和音频
                concatenated_features = np.concatenate(self.valid_features_buffer, axis=0)
                concatenated_audio = np.concatenate(self.valid_audio_buffer, axis=0)
                
                # 提取20帧窗口
                feature_window = concatenated_features[:win_size]
                
                # 计算对应音频
                samples_per_frame = len(concatenated_audio) / concatenated_features.shape[0]
                audio_samples = int(win_size * samples_per_frame)
                audio_window = concatenated_audio[:audio_samples]
                
                # 使用真实wenet模型处理mel特征
                wenet_features = self.process_mel_with_wenet(feature_window)
                
                # 保存结果
                self.all_wenet_features.append(wenet_features)
                self.all_indexs.append(wenet_features)  # index就是wenet特征
                
                logger.info(f"生成index: mel形状={feature_window.shape}, wenet形状={wenet_features.shape}")
                
                # 移除已处理的特征
                remaining_features = concatenated_features[win_size:]
                remaining_audio = concatenated_audio[audio_samples:]
                
                self.valid_features_buffer = [remaining_features] if remaining_features.shape[0] > 0 else []
                self.valid_audio_buffer = [remaining_audio] if len(remaining_audio) > 0 else []
                
                total_frames = sum(mel.shape[0] for mel in self.valid_features_buffer)
            else:
                break
    
    def finalize(self):
        """最终处理"""
        # 添加末尾静音填充
        if len(self.overlap_buffer) > 0:
            zero_padding = np.zeros(6400)  # 0.4秒静音
            self.overlap_buffer = np.concatenate([self.overlap_buffer, zero_padding])
        
        # 处理剩余窗口
        if len(self.overlap_buffer) >= self.window_size:
            self._process_overlap_windows()
        
        # 处理剩余特征
        if len(self.valid_features_buffer) > 0:
            concatenated_features = np.concatenate(self.valid_features_buffer, axis=0)
            
            # 按20帧处理剩余特征
            win_size = 20
            for i in range(0, concatenated_features.shape[0], win_size):
                if i + win_size <= concatenated_features.shape[0]:
                    feature_window = concatenated_features[i:i + win_size]
                    wenet_features = self.process_mel_with_wenet(feature_window)
                    self.all_wenet_features.append(wenet_features)
                    self.all_indexs.append(wenet_features)
                elif concatenated_features.shape[0] - i >= 10:  # 至少10帧
                    feature_window = concatenated_features[i:]
                    wenet_features = self.process_mel_with_wenet(feature_window)
                    self.all_wenet_features.append(wenet_features)
                    self.all_indexs.append(wenet_features)
        
        logger.info(f"流式处理完成: mel块数={len(self.all_mel_features)}, index数={len(self.all_indexs)}")


def main():
    """
    主函数
    """
    audio_file = "11_16k.wav"
    fps = 25
    
    logger.info("🚀 开始AudioExtractor核心逻辑调试")
    logger.info(f"音频文件: {audio_file}")
    logger.info(f"视频帧率: {fps} fps")
    
    try:
        # 读取音频文件
        audio_data, sr = librosa.load(audio_file, sr=16000)
        logger.info(f"音频文件: 长度={len(audio_data)}, 采样率={sr}, 时长={len(audio_data)/sr:.2f}秒")
        
        # 1. 批处理
        logger.info("步骤1: 批处理...")
        batch_processor = BatchProcessor(fps)
        batch_results = batch_processor.process_audio(audio_data)
        
        # 2. 流式处理
        logger.info("步骤2: 流式处理...")
        streaming_processor = StreamingProcessor(fps)
        
        # 按100ms分块处理
        chunk_size_100ms = int(sr * 0.1)  # 100ms = 1600样本
        
        for i in range(0, len(audio_data), chunk_size_100ms):
            chunk = audio_data[i:i + chunk_size_100ms]
            streaming_processor.process_audio_chunk(chunk)
            logger.info(f"处理音频块 {i//chunk_size_100ms + 1}: {len(chunk)}样本")
        
        # 最终处理
        streaming_processor.finalize()
        
        # 3. 比较结果
        logger.info("步骤3: 比较结果...")
        
        # 比较mel特征
        batch_mel = batch_results['mel_features']
        streaming_mel_list = streaming_processor.all_mel_features
        
        if streaming_mel_list:
            streaming_mel = np.concatenate(streaming_mel_list, axis=0)
            logger.info(f"批处理mel特征形状: {batch_mel.shape}")
            logger.info(f"流式处理mel特征形状: {streaming_mel.shape}")
        
        # 比较index数量
        batch_indexs = batch_results['indexs']
        streaming_indexs = streaming_processor.all_indexs
        
        logger.info(f"批处理index数量: {len(batch_indexs)}")
        logger.info(f"流式处理index数量: {len(streaming_indexs)}")
        
        if batch_indexs and streaming_indexs:
            logger.info(f"批处理index形状: {batch_indexs[0].shape}")
            logger.info(f"流式处理index形状: {streaming_indexs[0].shape}")
        
        logger.info("🎉 调试完成!")
        
    except Exception as e:
        logger.error(f"调试过程中出现错误: {e}")
        logger_error(e)


if __name__ == "__main__":
    main()

