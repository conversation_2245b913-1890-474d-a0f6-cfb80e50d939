# -*-coding: utf-8 -*-
# @Time :2025-07-02
# <AUTHOR> lishengjing
# @Email : <EMAIL>
# @File : audio_extractor.py

import os
import sys
from loguru import logger
import numpy as np
from queue import Empty
import librosa
import time
import base64
from pydub import AudioSegment
import soundfile as sf  # 用于保存wav文件
import torch
from wenet.compute_ctc_att_bnf import get_weget, load_ppg_model, WenetStreamer
from wenet.tools._extract_feats import wav2mfcc_v2

def logger_error(E):
    def locate_exception():
        """
        locate error filename object line
        """
        exc_type, exc_obj, exc_tb = sys.exc_info()
        fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
        return "{},{},{}".format(exc_type, fname, exc_tb.tb_lineno)
    logger.error(locate_exception())
    logger.error("error {}".format(E))


# 均分切割音频
def split_list_uniformly(seg_audio, indexs):
        audio_length = len(seg_audio)
        indexs_length = len(indexs)
        k, m = divmod(audio_length, indexs_length)
        # 确定哪些位置需要多一个元素
        indices = [i * (k + 1) if i < m else m * (k + 1) + (i - m) * k for i in range(indexs_length + 1)]
        return [seg_audio[indices[i]:indices[i + 1]] for i in range(indexs_length)]





class SegmentAudioProcessor:
    """新的流式处理器（基于时间段的wenet计算）"""

    def __init__(self, feature_queue, audio_stream_queue):
        self.work_id_for_logging = "AudExtractor"
        self.feature_queue = feature_queue
        self.audio_stream_queue = audio_stream_queue
        self.wenet_model = load_ppg_model("wenet/examples/aishell/aidata/conf/train_conformer_multi_cn.yaml", "wenet/examples/aishell/aidata/exp/conformer/wenetmodel.pt", "cuda")

        # Mel特征提取参数（与SimpleMelDebugger保持一致）
        self.mel_params = {
            'sample_rate': 16000,
            'preemphasis': 0.97,
            'n_fft': 1024,
            'hop_length': 160,
            'win_length': 800,
            'num_mels': 80,
            'n_mfcc': 13,
            'window': 'hann',
            'fmin': 0.0,
            'fmax': 8000.0,
            'ref_db': 20,
            'min_db': -80.0,
            'center': True
        }

        self.task_info = None
        self.is_stream = False
        self.audio_path = None
        self._is_running = False

        
    def init_task(self, task_info):
        self.task_info = task_info
        self.is_stream = task_info.get('is_stream', False)
        self.audio_path = task_info.get('audio_path')
        self.fps = task_info.get('fps', 25)
        self._status = "Ready"
        self._init_one_session()

        # 计算音频处理参数
        self.sample_rate = 16000
        self.samples_per_video_frame = self.sample_rate // self.fps  # 每视频帧音频样本数

        logger.info(f"【{self.work_id_for_logging}】 AudioExtractor 初始化成功.")
        logger.info(f"  - 流式模式: {self.is_stream}")
        logger.info(f"  - 视频帧率: {self.fps} fps")
        return self


    def _init_one_session(self):
        """初始化一个会话的配置"""
        # 时间段配置（秒）
        # -------------------第一段的和最后一段的需要精心设计-------------------
        self.fisrt_segment_duration = 1.6   # 首次计算的最小时长，最好*fps是整数
        self.fisrt_boundary_duration = 0.6   # 首次计算的需要抛弃的部分，因为首次计算的音频长度比较短，所以边界时间段可以比较小
        self.is_last_segment = False            # 是否是最后一段音频

        # -------------------其他段可以通用-------------------
        self.min_segment_duration = 3.0   # 后续计算的最小时长
        self.max_segment_duration = 6.0   # 最大时长
        self.boundary_duration = 1      # 边界时间段，每次向前移动boundary_duration*2秒，计算出来的结果前后需要去掉边界部分（减少补零的影响），boundary_duration应该大于0.4秒，感觉0.5秒也可以
        
        # -------------------本次计算索引的起点和终点-------------------
        self.last_cal_idx = 0                   # 上一次计算的音频样本数
        self.current_cal_start_idx = 0          # 当前计算的音频样本数
        self.current_cal_end_idx = 0            # 当前计算的音频样本数
        self.collected_audio = []               # 本次计算的音频数据片段

        # ------------------- 流式处理的总体参数 -------------------
        self.all_audio = []                  # 本次会话所有音频数据
        self.calculation_times = 0           # 一共计算了几次wenet
        self.total_processed_audio_samples = 0  # 总处理的音频样本数 就是len(self.all_audio)
        self.all_wenet_results = []         # 所有wenet计算结果

        self.all_mel_features = []
        self.all_wenet_features = []
        self.all_indexs_num = 0
        self.all_mel_indexs = []
        #
        self.max_silence_cache_cnt = 8 # 帧数
        # ----------------缓存要发送到下一个进程的数据 -------------------------
        self.remaining_feature_list = []  #[state,[audio],[index],...]




    def run(self):
        self._is_running = True
        try:
            if self.is_stream:
                self.run_flow_audio_with_wenet()

            else:
                self.run_local_audio()

        except Exception as e:
            logger_error(f"【{self.work_id_for_logging}】 处理失败: {e}")
        finally:
            # self.finish()
            pass

    def run_local_audio(self): # 还没有弄好，需要修改
        logger.info(f"[{self.work_id_for_logging}] Processing local audio file with Wenet: {self.audio_path}")
        f_wenet_all = get_weget(self.audio_path, self.wenet_model)
        logger.info(f"[{self.work_id_for_logging}] Extracted Wenet features from local audio, shape: {f_wenet_all.shape}")

        chunk_size = 100
        for i in range(0, f_wenet_all.shape[0], chunk_size):
            chunk = f_wenet_all[i:i + chunk_size, :]
            if self._is_running:
                self.feature_queue.put(chunk)
            else:
                break


    def run_flow_audio_with_wenet(self):
        """
        流式音频处理逻辑（最终修正版本）- 完全模拟批处理逻辑
        """
        logger.info(f"【{self.work_id_for_logging}】 开始流式音频处理.")
        self._is_running = True

        self._handle_start_state() # 开始先进入开始状态，等待数据
        self._handle_silence_state()  #开始先进入静音状态，等待数据

        while self._is_running:
            try:
                status, audio_chunk = self.audio_stream_queue.get(block=True) #todo 还没有处理没有音频的情况
                self._status = status
                if status == "data" and audio_chunk:
                    self._handle_data_state(audio_chunk,is_data=True)
                elif status == "start":
                    self._handle_start_state()
                elif status == "silence":
                    self._handle_silence_state()

                elif status == "end":
                    logger.info(f"【{self.work_id_for_logging}】 音频流结束信号接收. 最终处理...")
                    self._handle_end_state()
                    self._is_running = False

            except Empty:
                logger.warning(f"【{self.work_id_for_logging}】 等待音频块超时. 处理剩余数据...")
                self._is_running = False
                break

            except Exception as e:
                logger_error(f"【{self.work_id_for_logging}】 流式音频处理循环错误: {e}")
                self._is_running = False

    def _handle_data_state(self, audio_chunk,is_data=True,is_last_segment=False):
        """处理音频块（新的时间段策略）"""
        if is_data: # 有新数据
            # 保存原始音频数据，不进行归一化
            audio_segment = np.frombuffer(audio_chunk, dtype=np.int16)  # audio_chunk:3200 -->audio_segment:1600
            # 累积原始音频数据
            if isinstance(audio_segment, np.ndarray):
                self.all_audio.extend(audio_segment.flatten().tolist())
            else:
                self.all_audio.extend(audio_segment.tolist())

            self.total_processed_audio_samples += len(audio_segment)
            # logger.info(f"【{self.work_id_for_logging} [data]】 累积音频数据: {self.total_processed_audio_samples/self.sample_rate:.2f}秒 , 音频长度: {len(audio_segment)/self.sample_rate*1000} ms")

        # 检查是否需要计算wenet
        seg_audio_chunks,indexs = self._check_and_process_wenet_segment(is_data=is_data,is_last_segment=is_last_segment)

        # 将结果分成每一帧
        if len(indexs) and len(seg_audio_chunks):
            for seg_audio_chunk,indexs_chunk in zip(seg_audio_chunks,indexs):
                self.remaining_feature_list.append(["data",seg_audio_chunk,indexs_chunk.tolist()])

        # 发送数据，如果是满了就先退出，先去处理接受传过来的数据，等下一次循环，所以feature_queue的block 不应该为true
        while not self.feature_queue.full() and len(self.remaining_feature_list) > 0:
            [_status,audio,indexs] = self.remaining_feature_list.pop(0)
            self.feature_queue.put((_status, indexs, audio)) 
            time.sleep(0.002) # 2ms
    

    def _handle_silence_state(self):
        """处理静音段"""
        ##################################### 先处理剩余的音频 #####################################
        if not self._status == "Ready":  # 开始初始化的就不要处理这个了
            logger.info(f"【{self.work_id_for_logging} [silence]】 已接受到静音信号，开始处理剩余数据.")
            self._handle_data_state(None,is_data=False,is_last_segment=True)
 
        # 发送数据
        while len(self.remaining_feature_list) > 0 :
            [_status,audio,indexs] = self.remaining_feature_list.pop(0)
            #  这里的逻辑与data状态不一样 因为end状态需要等待所有数据发送完，所以需要block=True
            self.feature_queue.put((_status, indexs, audio),block=True) 
            time.sleep(0.002) # 2ms
       
        ##################################### 处理静音的逻辑 #####################################
        logger.info(f"【{self.work_id_for_logging} [silence]】 🎉🎉🎉剩余数据已处理完，🚀🚀🚀目前开始处理静音的逻辑.")

        # 重置所有缓冲区和状态
        self._init_one_session()

        # 先加载静音数据和index ,并且只加载一次
        if not hasattr(self,"silence_data_index") or not hasattr(self,"silence_data_wav"):
            self._load_silence_data_and_index()  
        # 前面20帧和后面20帧的值不太一样，其他就还好
        self.silence_data_index_len = len(self.silence_data_index)
        self.silence_data_wav_len = len(self.silence_data_wav)
        self.silence_frame_idx  = 0 # 已经发送了多少帧
        self.data_idx_max = self.silence_data_index_len - 40 # 前后20帧

        # 一直循环发送空白数据，直到有新的状态
        _status = "silence"
        while _status == "silence":
            try:
                # 检查是否需要发送静音数据
                self.check_and_put_one_silence_frame(self.max_silence_cache_cnt)

                # 检查上一个队列是否有新数据，如果有新数据，则切换到数据状态
                # todo : 这里可能有问题，如果feature_queue只有5帧，也就是只能维持200ms，这会可能会播放完了但是data数据还没有处理完？？
                if not self.audio_stream_queue.empty():
                    print(f"【{self.work_id_for_logging} [silence]】队列不为空，开始获取数据...")
                    _status, audio_chunk = self.audio_stream_queue.get_nowait()
                    self._status = _status
         
                    # 直接处理新状态，不调用process_state避免递归
                    if _status == "data":
                        print(f"【{self.work_id_for_logging}[swith status]】从silence状态切换到data状态")
                        self._handle_data_state(audio_chunk,is_data=True)
                    elif _status == "start": # 要注意该状态！！
                        logger.info(f"【{self.work_id_for_logging} [silence]】 从silence状态切换到start状态.")
                        self.remaining_feature_list.append(["start",None,None])
                    elif _status == "end":
                        logger.info(f"【{self.work_id_for_logging} [silence]】 从silence状态切换到end状态.") 
                        self._handle_end_state()
                        self._is_running = False
            except Exception as e:
                logger_error(f"【{self.work_id_for_logging}】 静音状态处理错误: {e}")
                self._is_running = False
                break

        logger.info(f"【{self.work_id_for_logging} [silence]】 🎉🎉🎉静音逻辑处理完成.")


    def _handle_end_state(self,):
        """处理结束段"""
        # 先处理剩余的音频
        self._handle_data_state(None,is_data=False,is_last_segment=True)
        self.remaining_feature_list.append(("end", None, None))
        # 发送数据
        while len(self.remaining_feature_list) > 0:
            [_status,audio,indexs] = self.remaining_feature_list.pop(0)
            #  这里的逻辑与data状态不一样 因为end状态需要等待所有数据发送完，所以需要block=True
            self.feature_queue.put((_status, indexs, audio),block=True) 
            time.sleep(0.002) # 2ms
        self._init_one_session()

    def _handle_start_state(self):
        """处理开始段"""
        logger.info(f"【{self.work_id_for_logging} [start]】 🎉🎉🎉音频流开始信号接收. 开始处理...")
        self._init_one_session()
        self.feature_queue.put(("start",None,None))
        logger.info(f"【{self.work_id_for_logging} [start]】 🚀🚀🚀已发送start信号到 DhStreamProcessor...")

    def _handle_timeout_state(self):
        pass


    def _check_and_process_wenet_segment(self,is_data=True,is_last_segment=False):
        """检查并处理wenet段"""
        current_duration = (self.total_processed_audio_samples-self.last_cal_idx) / self.sample_rate # 已经积累了多少秒的音频样本

        # 判断是否需要处理
        should_process = False
        # 要先判断是否是最后一段，不然假如同时是第一段且是最后一段，is_last_segment=true的逻辑不会被执行
        if is_last_segment: 
            self.is_last_segment = True
            forward_duration = self.boundary_duration*2  if  self.calculation_times>=2 else self.boundary_duration+self.fisrt_boundary_duration  # 需要获取前移音频的秒数
            self.current_cal_start_idx = int(max(0,self.last_cal_idx-forward_duration*self.sample_rate))
            self.current_cal_end_idx = len(self.all_audio)
            self.collected_audio = self.all_audio[self.current_cal_start_idx:self.current_cal_end_idx] # 截取最大时长
            logger.info(f"【{self.work_id_for_logging} [{self._status}]】最后一段音频，第{self.calculation_times+1}次累计数据: 音频时长={current_duration:.2f}秒，从第{self.current_cal_start_idx/self.sample_rate:.6f}秒到第{self.current_cal_end_idx/self.sample_rate:.6f}秒")
            
        elif self.calculation_times==0:
            # 首次计算：超过最小时长即可
            if current_duration >= self.fisrt_segment_duration:
                should_process = True
                self.current_cal_start_idx = 0
                self.current_cal_end_idx = int(self.fisrt_segment_duration*self.sample_rate)
                self.collected_audio = self.all_audio[self.current_cal_start_idx:self.current_cal_end_idx] #只是截取一秒钟的音频
                logger.info(f"【{self.work_id_for_logging} [{self._status}][累计数据]】首次累计数据: 音频时长={len(self.collected_audio)/self.sample_rate:.2f}秒，从第{self.current_cal_start_idx/self.sample_rate:.2f}秒到第{self.current_cal_end_idx/self.sample_rate:.2f}秒")
        else:
            # 后续计算：检查时长条件
            # 因为每次计算完，会前后抛弃boundary_duration，所以需要向前移动boundary_duration*2，这样才可以把数据对接上
            # 第二次的时候，只是向前移动一次boundary_duration + fisrt_boundary_duration，因为第一次的fisrt_boundary_duration被剪掉
            # 第三次开始，因为上一次的最后boundary_duration被剪掉，所以需要向前移动两次boundary_duration，将上次最后的部分也加上
            forward_duration = self.boundary_duration*2  if  self.calculation_times>=2 else self.boundary_duration+self.fisrt_boundary_duration  # 需要获取前移音频的秒数
            self.current_cal_start_idx = int(max(0,self.last_cal_idx-forward_duration*self.sample_rate))
            should_process = True

            if current_duration >= self.max_segment_duration-forward_duration:  # 满足计算条件：积累的音频 + 需要前移的音频 >= 最大计算音频的长度
                self.current_cal_end_idx = int(self.current_cal_start_idx +self.max_segment_duration*self.sample_rate)
                self.collected_audio = self.all_audio[self.current_cal_start_idx:self.current_cal_end_idx] # 截取最大时长
                logger.info(f"【{self.work_id_for_logging} [{self._status}][累计数据]】达到最大时长，第{self.calculation_times+1}次累计数据: 音频时长={len(self.collected_audio)/self.sample_rate:.2f}秒，从第{self.current_cal_start_idx/self.sample_rate:.2f}秒到第{self.current_cal_end_idx/self.sample_rate:.2f}秒")
                
            elif current_duration >= self.min_segment_duration and not is_data:
                # 此时如果没有接收到数据且达到最小时长：立即处理
                self.current_cal_end_idx = int(self.current_cal_start_idx +self.min_segment_duration*self.sample_rate)
                self.collected_audio = self.all_audio[self.current_cal_start_idx:self.current_cal_end_idx] # 截取最大时长
                logger.info(f"【{self.work_id_for_logging} [{self._status}][累计数据]】达到最小时长且无数据，第{self.calculation_times+1}次累计数据: 音频时长={current_duration:.2f}秒，从第{self.current_cal_start_idx/self.sample_rate:.2f}秒到第{self.current_cal_end_idx/self.sample_rate:.2f}秒")

            else:
                should_process = False # 还没有满足条件，继续循环
        indexs = [] # 每一帧的计算结果，[20*256,20*256...]
        audio_segment =[]
        seg_audio_chunks = [] # 每一帧的音频数据
        if should_process:
            indexs,audio_segment = self._process_wenet_segment()
            self.last_cal_idx = self.current_cal_end_idx

        # 将结果分成每一帧
        if len(indexs) and len(audio_segment):
            seg_audio_chunks = split_list_uniformly(audio_segment, indexs)
        return seg_audio_chunks,indexs

    def _process_wenet_segment(self):
        """处理wenet段"""
        if len(self.collected_audio) == 0:
            return []

        # 转换为numpy数组并进行归一化（wenet处理前）
        # self.collected_audio 中存储的是原始 int16 数据，需要转换为 -1 到 1 的浮点数据
        audio_array = np.array(self.collected_audio, dtype=np.int16).astype(np.float32) / 32768.0

        win_size = 20 # 窗口大小
        time_duration = len(self.collected_audio) / self.sample_rate # 音频时长:秒
        cnts = range(int(time_duration * self.fps)) # 音频帧数
        indexs = []
        effective_wenet = get_weget(audio_array,self.wenet_model)
        effective_wenet_shape = effective_wenet.shape
        for cnt in cnts:
            c_count = int(cnt / cnts[-1] * (effective_wenet.shape[0] - 20)) + win_size // 2
            indexs.append(effective_wenet[(c_count - win_size // 2):c_count + win_size // 2, ...])
        # print("indexs",len(indexs))
        if self.calculation_times == 0:
            start_idx = 0
            end_idx = -int(self.fisrt_boundary_duration*self.fps)   # 最后0.6秒不需要
            start_audio =0
            end_audio = -int(self.fisrt_boundary_duration*self.sample_rate)
        elif self.is_last_segment:
            start_idx = int(self.boundary_duration*self.fps)
            end_idx = len(indexs) # 不要用-1，会少一位
            start_audio = int(self.fisrt_boundary_duration*self.sample_rate)
            end_audio = len(self.collected_audio)
            # print("start_idx",start_idx,'end_idx',end_idx)
        else:
            start_idx = int(self.boundary_duration*self.fps)
            end_idx = -int(self.boundary_duration*self.fps)
            start_audio = int(self.boundary_duration*self.sample_rate)
            end_audio = -int(self.boundary_duration*self.sample_rate)

        indexs = indexs[start_idx:end_idx]
        # 返回原始音频数据（未归一化），用于传输到下一个进程
        audio_collected = np.array(self.collected_audio)[start_audio:end_audio].tolist()


        # 更新状态
        self.calculation_times +=1
        # 按照您的正确设计：清空音频缓存（边界信息已保存在boundary_audio_buffer中）
        self.all_indexs_num += len(indexs)
        logger.info(f":第{self.calculation_times}次wenet运算：本次处理{time_duration:.2f}秒音频，得到{effective_wenet_shape}帧，其中有效数据应该是{time_duration-self.boundary_duration*2:.2f}秒，有效音频从第{self.current_cal_start_idx/self.sample_rate+start_idx/self.fps:.2f}秒到第{self.current_cal_end_idx/self.sample_rate-(max(0,-end_idx)/self.fps):.2f}秒，得到{len(indexs)}帧特征，start_idx:{start_idx}, end_idx:{end_idx}")
        self.collected_audio = []
        # logger.info("清空音频缓存（边界信息已保存在边界缓冲中）")
        return indexs,audio_collected
        

    def _load_silence_data_and_index(self):
        """创建静音数据并生成index,并将结果保存下来"""
        
        silence_data_index_file = "silence_5s_index.npy"
        silence_wav_file = "silence_5s_audio.wav"
        # 如果不存在就先保存
        if not os.path.exists(silence_data_index_file) or not os.path.exists(silence_wav_file):
            logger.info(f"【{self.work_id_for_logging}】 静音数据不存在，开始创建静音数据...")
            silence_data_5s = AudioSegment.silent(duration=5000, frame_rate=16000)
            # 保存 audio为wav文件
            silence_data_5s.export(silence_wav_file, format="wav")

            indexs = get_aud_feat1(silence_wav_file, self.fps, self.wenet_model)
            # 保存 indexs
            np.save(silence_data_index_file, indexs)


        # 加载
        silence_data_wav = AudioSegment.from_wav(silence_wav_file)
        self.silence_data_index = np.load(silence_data_index_file)
        self.silence_data_wav= np.array(silence_data_wav.get_array_of_samples(),dtype=np.int16)
        print("!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!self.silence_data_wav",self.silence_data_wav.shape,self.silence_data_wav.dtype,self.silence_data_wav.max(),self.silence_data_wav.min())
        logger.info(f"【{self.work_id_for_logging}】 静音数据已创建，index数量：{len(self.silence_data_index)}")
   

    def finalize(self):
        """完成流式处理"""
        # 处理剩余音频
        indexs,audio_segment = self._check_and_process_wenet_segment(is_last_segment=True)
        self.all_indexs_num += len(indexs) if indexs is not None else 0
        logger.info("音频长度：{}".format(len(self.all_audio)/self.sample_rate))
        logger.info(f"最终index数量：{self.all_indexs_num}")
        return indexs,audio_segment

    def check_and_put_one_silence_frame(self,max_silence_cache_cnt=8):
        """获取静音数据"""
        qsize = self.feature_queue.qsize() # 下一进程缓存的数据数量
        if qsize < max_silence_cache_cnt: # 下一进程已经消耗了比较多的数据，需要补充
            index_idx = self.silence_frame_idx if self.silence_frame_idx < 20 else (self.silence_frame_idx-20)%self.data_idx_max + 20 # 选择第几帧的index todo:看看是否需要反复循环
            # print("self.silence_frame_idx",self.silence_frame_idx,index_idx)
            audio_idx = index_idx*self.samples_per_video_frame # 选择第几帧的index todo:看看是否需要反复循环
            silence_index_data = self.silence_data_index[index_idx].tolist() #20*256
            silence_audio_data = self.silence_data_wav[audio_idx:audio_idx+self.samples_per_video_frame].tolist() # 16000//25=640
            print("silence_index_data",np.array(silence_index_data).shape,"silence_audio_data",np.array(silence_audio_data).shape)
            self.feature_queue.put(("silence",silence_index_data,silence_audio_data))  # 放入一帧数据
            # 打印 放入情况
            # if qsize < 5:
            #     logger.info(f"【{self.work_id_for_logging} [silence]】 放入一帧数据: {self.silence_frame_idx} ,下一进程队列大小: {self.feature_queue.qsize()}")
            self.silence_frame_idx +=1



def feature_extraction_wenet(audio_file, fps, wenet_model, mfccnorm=True, section=560000):
    """
    音频特征提取 (用于非流式)
    """
    rate = 16000
    win_size = 20
    if type(audio_file) == str:
        sig, rate = librosa.load(audio_file, sr=rate, duration=None)
    else:
        sig = audio_file
    time_duration = len(sig) / rate # 音频时长:秒
    cnts = range(int(time_duration * fps)) # 音频帧数
    indexs = []
    f_wenet_all = get_weget(audio_file, wenet_model, section)
    print(f"f_wenet_all.shape:{f_wenet_all.shape}")
    for cnt in cnts:
        c_count = int(cnt / cnts[-1] * (f_wenet_all.shape[0] - 20)) + win_size // 2
        indexs.append(f_wenet_all[(c_count - win_size // 2):c_count + win_size // 2, ...])
    return indexs


def get_aud_feat1(wav_fragment, fps, wenet_model):
    return feature_extraction_wenet(wav_fragment, fps, wenet_model)



