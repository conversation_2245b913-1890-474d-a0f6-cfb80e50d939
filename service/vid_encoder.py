# -*-coding: utf-8 -*-
# @Time :2025-07-02
# <AUTHOR> lishengjing
# @Email : <EMAIL>
# @File : encoder.py


import time
import os
import cv2
import sys
import numpy as np
import subprocess
import queue  # 添加queue导入
import copy
import shutil
import socket
import gc, multiprocessing, os, subprocess, threading, time, traceback,sys
import json
import uuid
from weakref import WeakSet
from typing import Dict
from loguru import logger
from h_utils.custom import CustomError
import fractions
from typing import Tuple, Dict, Optional, Set, Union
import asyncio
from aiohttp import web
from aiortc import RTCPeerConnection, RTCSessionDescription
from aiortc.contrib.media import MediaStreamTrack
from av import VideoFrame, AudioFrame
from av.frame import Frame
from av.packet import Packet
from av import AudioFrame
#
def logger_error(E):
    def locate_exception():
        """
        locate error filename object line
        """
        exc_type, exc_obj, exc_tb = sys.exc_info()
        fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
        return "{},{},{}".format(exc_type, fname, exc_tb.tb_lineno)
    logger.error(locate_exception())
    logger.error("error {}".format(E))



class VideoEncoder:
    """
    一个长期运行的类，通过队列接收指令和数据来处理视频写入任务。
    """
    def __init__(self, output_imgs_queue, result_queue):
        self.output_imgs_queue = output_imgs_queue
        self.result_queue = result_queue
        self.video_writer = None
        self.status_code = None
        self.is_stream = False
        self.work_id = "VideoEncoder"  # 用于日志打印
        self.last_send_time = time.time()
        self.send_time_list = [] # 记录发送帧的时间
        self.rtp_streamer = None  # 推流器
        
        # 新增：队列管理和性能监控
        self.max_queue_size = 8  # 降低最大队列大小，减少延迟
        self.frame_drop_count = 0  # 丢帧统计
        self.process_count = 0    # 处理帧数统计
        self.last_queue_check = time.time()  # 上次队列检查时间
        
        # 内存管理参数
        self.memory_check_interval = 1000  # 每处理50帧检查一次内存/40s
        self.last_memory_check = 0
        self.gc_interval = 4  # 每4帧进行一次垃圾回收

    def run(self): 
        """主循环，等待并处理任务。"""
        while True:
            try:
               
                self.status_code, reason, data_tuple = self.output_imgs_queue.get(block=True)


                if reason is not None: # 报错直接退出
                    logger.error(f"【VideoEncoder】 处理错误: {reason}")
                    self.result_queue.put([False, f"【VideoEncoder】 处理错误: {reason}"])
                    continue

                # 正常运行状态
                # 1. 初始化任务
                if self.status_code == 'start':
                    self._initialize_task(data_tuple)
                # 2. 循环处理当前任务的视频帧
                elif self.status_code in ['data', 'silence']:
                    self._process_frames_loop(data_tuple)
  
                # 4. 收尾工作
                elif self.status_code == 'finish':
                    self._finalize_task(data_tuple)
                    self._cleanup_after_task()

            except Exception as e:
                logger_error(f"【{self.work_id}】 VideoEncoder 处理失败: {e}")
                self.result_queue.put([False, f"【{self.work_id}】 VideoEncoder 处理失败: {e}"])

    def _initialize_task(self, task_info):
        """从'start'消息中解包参数并初始化视频写入器。"""

        self.work_id = task_info['work_id']
        self.audio_path = task_info['audio_path']
        self.original_frame_shape_init = task_info.get('original_frame_shape_init', (0,0))
        self.width = self.original_frame_shape_init[1]
        self.height = self.original_frame_shape_init[0]
        self.fps = task_info['fps'] 
        self.is_stream = task_info['is_stream']
        self.use_npy = task_info['use_npy']
        
        # 初始化推流器（如果是流式模式且有推流URL）
        self.stream_url = task_info.get('stream_url',None)
        self.streamer_type = task_info.get('streamer_type', 'webrtc')  # 默认使用webrtc
        
        if self.is_stream and not self.stream_url:
            logger.error(f"【**VideoEncoder**】【{self.work_id}】 推流URL为空")
            self.result_queue.put([False, f"【VideoEncoder】 推流URL为空"])
            return

        if self.is_stream and self.stream_url:

            # elif self.streamer_type == 'webrtc':
            self.rtp_streamer = WebRTCStreamer(
                    signaling_url=self.stream_url,
                    audio_sample_rate=16000, # 假设采样率为16k
                    video_fps=self.fps
                )
            logger.info(f"【**VideoEncoder**】【{self.work_id}】 使用统一的WebRTCStreamer进行推流")
            # else:  # 'ffmpeg' 或其他
            #     # 使用FFmpeg推流器（原始实现）
            #     self.rtp_streamer = RTPStreamer(
            #         self.stream_url,
            #         self.width,
            #         self.height,
            #         self.fps
            #     )
            #     logger.info(f"【**VideoEncoder**】【{self.work_id}】 使用RTPStreamer进行推流 (音视频)")
            #
            if not self.rtp_streamer.start():
                logger_error(f"【**VideoEncoder**】【{self.work_id}】 推流器启动失败")
            
        
        # todo 这里需要优化，兼容is_stream
        self.temp_video_path = os.path.join("workspace",  f'{self.work_id}-temp.mp4')
        self.final_path = os.path.join("workspace", f'{self.work_id}-result.mp4') 
        if not self.is_stream:      
            fourcc = (cv2.VideoWriter_fourcc)(*"mp4v")
            self.video_writer = cv2.VideoWriter(self.temp_video_path, fourcc, self.fps, (self.width, self.height))
            logger.info(f"【**VideoEncoder非流式**】【{self.work_id}】 视频写入器初始化完成: {self.temp_video_path}")
        logger.info(f"【**VideoEncoder初始化**】【{self.work_id}】 视频写入器初始化完成: {self.temp_video_path}")

    def _process_frames_loop(self,data_tuple):
        """处理一个批次的视频帧。"""
        drivered_fnames_list, face_params_tuple, processed_raw_face_regions, no_face_indices, use_npy_flag,batch_audio_data = data_tuple
        
        # 添加日志以验证音频数据
        if batch_audio_data is not None and len(batch_audio_data) > 0:
            logger.info(f"【VideoEncoder】接收到批处理的音频数据。块数: {len(batch_audio_data)}")
        else:
            logger.warning("【VideoEncoder】收到的批处理中没有音频数据。")

        out_shape, output_resize, y1_list, y2_list, x1_list, x2_list = face_params_tuple
        
        t_start_batch = time.time()
        processed_frames = 0
        
        # # 内存优化：检查队列大小，如果积压过多则跳过部分帧
        # current_queue_size = self.output_imgs_queue.qsize()
        # if current_queue_size > self.max_queue_size:
        #     logger.warning(f"【VideoEncoder】队列积压严重: {current_queue_size}个数据包，将跳过部分帧以释放内存")
        #     # 跳过部分帧以减少内存压力
        #     skip_ratio = min(0.5, current_queue_size / (self.max_queue_size * 2))
        #     frames_to_process = max(1, int(len(drivered_fnames_list) * (1 - skip_ratio)))
        #     drivered_fnames_list = drivered_fnames_list[:frames_to_process]
        #     self.frame_drop_count += len(drivered_fnames_list) - frames_to_process
        
        for i, fname in enumerate(drivered_fnames_list):
            try:
                image = np.load(fname) if self.use_npy else cv2.imread(fname)
                if image is None: raise CustomError(f"Failed to load image: {fname}")
            except Exception as e:
                logger_error(f"【{self.work_id}】 加载驱动图像失败: {fname}: {e}")
                continue

            if i not in no_face_indices:
                image = get_one_complete_img(image, processed_raw_face_regions[i], (y1_list[i], y2_list[i], x1_list[i], x2_list[i]), os.path.basename(fname))
            
            # 推流（如果启用）
            if self.rtp_streamer and self.rtp_streamer.is_running:
                self.rtp_streamer.write_frame(image,batch_audio_data)
            else:
                # 写入视频文件
                self.video_writer.write(image) 
            
            processed_frames += 1
            self.process_count += 1
            self.last_frame_time = time.time()
            self.send_time_list.append(time.time())
            if len(self.send_time_list) > 10:
                self.send_time_list.pop(0)
            
            # 内存优化：每处理4帧进行一次垃圾回收
            if processed_frames % 100 == 0:
                gc.collect()
        
        # 性能统计
        batch_time = time.time() - t_start_batch
        current_queue_size = self.output_imgs_queue.qsize()
        
        # 计算实时FPS
        if len(self.send_time_list) >= 2:
            time_span = self.send_time_list[-1] - self.send_time_list[0]
            real_fps = len(self.send_time_list) / max(time_span, 0.001)
        else:
            real_fps = 0
        
        logger.info(f"【VideoEncoder】处理{processed_frames}帧, 耗时{batch_time:.3f}s, "
                   f"队列:{current_queue_size}, 实时FPS:{real_fps:.1f}, 累计:{self.process_count}帧, 丢帧:{self.frame_drop_count}")
        
        # 如果队列持续积压，发出警告
        if current_queue_size > 6:
            logger.warning(f"【VideoEncoder】队列积压警告: {current_queue_size}个数据包待处理，建议检查推流性能")
        
        # 内存监控
        if self.process_count - self.last_memory_check >= self.memory_check_interval:
            self._check_memory_usage()
            self.last_memory_check = self.process_count

    def _check_memory_usage(self):
        """检查内存使用情况"""
        try:
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024
            logger.info(f"【VideoEncoder】内存使用: {memory_mb:.1f} MB")
            
            # 如果内存使用过高，强制垃圾回收
            if memory_mb > 1000:  # 超过1GB
                logger.warning(f"【VideoEncoder】内存使用过高: {memory_mb:.1f} MB，执行强制垃圾回收")
                gc.collect()
        except ImportError:
            logger.warning("【VideoEncoder】psutil未安装，无法监控内存使用")
        except Exception as e:
            logger.warning(f"【VideoEncoder】内存监控失败: {e}")

    def _finalize_task(self,data_tuple):
        """完成视频的收尾工作，如合并音频。"""
        if not self.is_stream and os.path.exists(self.temp_video_path):
            logger.info(f"[{self.work_id}] Merging audio from '{self.audio_path}' to create final video at '{self.final_path}'")
            cmd = f'ffmpeg -y -i "{self.temp_video_path}" -i "{self.audio_path}" -c:v copy -c:a aac -strict experimental "{self.final_path}"'
            subprocess.call(cmd, shell=True)
            self.result_queue.put([True, self.final_path])
        else:
            self.result_queue.put([True, "Stream finished." if self.is_stream else "Task finished."])

    def _cleanup_after_task(self):
        """在每个任务结束后（无论成功与否）进行清理。"""
        if self.video_writer :
            try:
                self.video_writer.release()
            except Exception as e:
                pass

        # 关闭推流器
        if self.rtp_streamer:
            try:
                self.rtp_streamer.stop()
                self.rtp_streamer = None
                logger.info(f"【VideoEncoder】【{self.work_id}】 推流器已关闭")
            except Exception as e:
                logger_error(f"【VideoEncoder】【{self.work_id}】 关闭推流器失败: {e}")
        if self.video_writer :
            try:
                self.video_writer.release()
            except Exception as e:
                pass

        logger.info(">>>>>>>>>>>>>>>>>>>>>>>>>>【VideoEncoder】【{self.work_id}】 任务处理完成. 等待新任务.<<<<<<<<<<<<<<<<<<<<<<<<")
        self.work_id = "VideoEncoder" # 重置 work_id




# ====================================================================
#  统一、简化的WebRTC实现
# ====================================================================

class MediaRelayTrack(MediaStreamTrack):
    """
    一个中继媒体帧的轨道，用于连接同步和异步代码。
    这个轨道内部包含一个队列，用于实现背压。
    """
    def __init__(self, kind, maxsize=4):  # 降低队列大小以减少延迟
        super().__init__()
        self.kind = kind
        self._queue = asyncio.Queue(maxsize=maxsize)

    async def recv(self):
        """aiortc调用此方法来获取下一帧以发送给客户端。"""
        frame = await self._queue.get()
        return frame

    async def queue_frame(self, frame):
        """生产者（外部代码）调用此方法将帧放入队列。"""
        # 如果队列已满，`put`将会异步等待，直到有空位为止。
        await self._queue.put(frame)


AUDIO_PTIME = 0.020  # 20ms audio packetization
VIDEO_CLOCK_RATE = 90000
VIDEO_PTIME = 1 / 25  # 30fps
VIDEO_TIME_BASE = fractions.Fraction(1, VIDEO_CLOCK_RATE)
SAMPLE_RATE = 16000
AUDIO_TIME_BASE = fractions.Fraction(1, SAMPLE_RATE)


class PlayerStreamTrack(MediaStreamTrack):
    """
    A video track that returns an animated flag.
    """

    def __init__(self, player, kind,maxsize=8):
        super().__init__()  # don't forget this!
        self.kind = kind
        self._player = player
        self._queue = asyncio.Queue(maxsize=maxsize)
        self.timelist = []  # 记录最近包的时间戳
        if self.kind == 'video':
            self.framecount = 0
            self.lasttime = time.perf_counter()
            self.totaltime = 0

    _start: float
    _timestamp: int

    async def next_timestamp(self) -> Tuple[int, fractions.Fraction]:
        if self.readyState != "live":
            raise Exception

        if self.kind == 'video':
            if hasattr(self, "_timestamp"):
                # self._timestamp = (time.time()-self._start) * VIDEO_CLOCK_RATE
                self._timestamp += int(VIDEO_PTIME * VIDEO_CLOCK_RATE)
                wait = self._start + (self._timestamp / VIDEO_CLOCK_RATE) - time.time()
                # wait = self.timelist[0] + len(self.timelist)*VIDEO_PTIME - time.time()
                if wait > 0:
                    await asyncio.sleep(wait)
                # if len(self.timelist)>=100:
                #     self.timelist.pop(0)
                # self.timelist.append(time.time())
            else:
                self._start = time.time()
                self._timestamp = 0
                self.timelist.append(self._start)
                print('video start:', self._start)
            return self._timestamp, VIDEO_TIME_BASE
        else:  # audio
            if hasattr(self, "_timestamp"):
                # self._timestamp = (time.time()-self._start) * SAMPLE_RATE
                self._timestamp += int(AUDIO_PTIME * SAMPLE_RATE)
                wait = self._start + (self._timestamp / SAMPLE_RATE) - time.time()
                # wait = self.timelist[0] + len(self.timelist)*AUDIO_PTIME - time.time()
                if wait > 0:
                    await asyncio.sleep(wait)
                # if len(self.timelist)>=200:
                #     self.timelist.pop(0)
                #     self.timelist.pop(0)
                # self.timelist.append(time.time())
            else:
                self._start = time.time()
                self._timestamp = 0
                self.timelist.append(self._start)
                print('audio start:', self._start)
            return self._timestamp, AUDIO_TIME_BASE

    async def recv(self) -> Union[Frame, Packet]:
        # frame = self.frames[self.counter % 30]
        self._player._start(self)
        # if self.kind == 'video':
        #     frame = await self._queue.get()
        # else: #audio
        #     if hasattr(self, "_timestamp"):
        #         wait = self._start + self._timestamp / SAMPLE_RATE + AUDIO_PTIME - time.time()
        #         if wait>0:
        #             await asyncio.sleep(wait)
        #         if self._queue.qsize()<1:
        #             #frame = AudioFrame(format='s16', layout='mono', samples=320)
        #             audio = np.zeros((1, 320), dtype=np.int16)
        #             frame = AudioFrame.from_ndarray(audio, layout='mono', format='s16')
        #             frame.sample_rate=16000
        #         else:
        #             frame = await self._queue.get()
        #     else:
        #         frame = await self._queue.get()
        frame = await self._queue.get()
        pts, time_base = await self.next_timestamp()
        frame.pts = pts
        frame.time_base = time_base
        if frame is None:
            self.stop()
            raise Exception
        if self.kind == 'video':
            self.totaltime += (time.perf_counter() - self.lasttime)
            self.framecount += 1
            self.lasttime = time.perf_counter()
            if self.framecount == 100:
                print(f"------actual avg final fps:{self.framecount / self.totaltime:.4f}")
                self.framecount = 0
                self.totaltime = 0
        return frame

    def stop(self):
        super().stop()
        if self._player is not None:
            self._player._stop(self)
            self._player = None


class WebRTCStreamer:
    """
    一个自包含的WebRTC推流器，在后台线程中运行自己的信令服务器和事件循环。
    它使用队列来实现反压，确保稳定性。
    """
    def __init__(self, signaling_url, audio_sample_rate=16000, video_fps=25):
        try:
            # URL格式应为 'host:port'
            host, port_str = signaling_url.split(":")
            port = int(port_str)
        except (ValueError, AttributeError):
            logger.error(f"【WebRTCStreamer】无效的信令URL '{signaling_url}'。将使用默认值 '0.0.0.0:8080'。")
            host, port = "0.0.0.0", 8080
            
        self._host = host
        self._port = port
        self._audio_sample_rate = audio_sample_rate
        self._video_fps = video_fps
        
        self._pcs = WeakSet()
        self._thread = None
        self._loop = None
        self.is_running = False
        
        # 此事件用于发出事件循环已启动的信号
        self._loop_started = threading.Event()

        # 所有对等连接共享的轨道 - 降低队列大小
        self._video_track = None
        self._audio_track = None

        # A/V 同步
        self._video_pts = 0
        self._audio_pts = 0
        
        # 性能优化参数
        self._max_video_bitrate = 1000000  # 1Mbps 视频码率
        self._max_audio_bitrate = 64000    # 64kbps 音频码率

    def start(self):
        if self.is_running:
            return True
        
        self._thread = threading.Thread(target=self._run_event_loop, daemon=True)
        self._thread.start()
        
        # 等待事件循环准备就绪
        if not self._loop_started.wait(timeout=10):
            logger.error("【WebRTCStreamer】Asyncio事件循环启动超时。")
            self.stop()
            return False
        
        logger.info(f"【WebRTCStreamer】推流器启动成功。信令服务位于 http://{self._host}:{self._port}/offer")
        return True

    def stop(self):
        if not self.is_running:
            return
        
        self.is_running = False
        if self._loop and self._loop.is_running():
            # 安排关闭协程并等待其完成
            future = asyncio.run_coroutine_threadsafe(self._shutdown(), self._loop)
            try:
                future.result(timeout=5)
            except asyncio.TimeoutError:
                logger.warning("【WebRTCStreamer】关闭超时。")

        if self._thread:
            self._thread.join(timeout=5)
        
        logger.info("【WebRTCStreamer】推流器已停止。")

    def write_frame(self, bgr_frame, batch_audio_data):
        if not self.is_running or not self._loop:
            return
            
        # 此方法从VideoEncoder的线程调用。
        # 我们需要安排异步的 `_queue_frames` 协程
        # 在推流器的事件循环上运行。
        asyncio.run_coroutine_threadsafe(
            self._queue_frames(bgr_frame, batch_audio_data), 
            self._loop
        )

    def _run_event_loop(self):
        """在后台线程中运行asyncio事件循环。"""
        self._loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self._loop)
        self.is_running = True
        
        # 在正确的事件循环中创建共享媒体轨道
        self._video_track = MediaRelayTrack(kind="video", maxsize=8)
        self._audio_track = MediaRelayTrack(kind="audio", maxsize=8)
        
        self._loop_started.set() # 发出循环和轨道已准备就绪的信号

        try:
            self._loop.run_until_complete(self._run_server())
        except Exception:
            logger.error(f"【WebRTCStreamer】事件循环崩溃: {traceback.format_exc()}")
        finally:
            self.is_running = False
            logger.info("【WebRTCStreamer】事件循环已关闭。")

    async def _run_server(self):
        """设置并运行aiohttp信令服务器。"""
        app = web.Application()
        app.router.add_post("/offer", self._handle_offer)
        
        runner = web.AppRunner(app)
        await runner.setup()
        site = web.TCPSite(runner, self._host, self._port)
        await site.start()

        # 保持服务器运行直到 stop() 被调用
        while self.is_running:
            await asyncio.sleep(0.1)

        await runner.cleanup()

    async def _handle_offer(self, request):
        """处理来自WebRTC客户端的SDP提议。"""
        params = await request.json()
        offer = RTCSessionDescription(sdp=params["sdp"], type=params["type"])
        
        # 创建优化的RTCPeerConnection
        pc = self._create_optimized_peer_connection()
        self._pcs.add(pc)

        @pc.on("connectionstatechange")
        async def on_connectionstatechange():
            logger.info(f"【WebRTCStreamer】对等连接状态: {pc.connectionState}")
            if pc.connectionState in ("failed", "closed", "disconnected"):
                await pc.close()
                self._pcs.discard(pc)

        @pc.on("iceconnectionstatechange")
        async def on_iceconnectionstatechange():
            logger.info(f"【WebRTCStreamer】ICE连接状态: {pc.iceConnectionState}")

        @pc.on("icegatheringstatechange")
        async def on_icegatheringstatechange():
            logger.info(f"【WebRTCStreamer】ICE收集状态: {pc.iceGatheringState}")

        # 将共享轨道添加到新的对等连接
        if self._video_track:
            pc.addTrack(self._video_track)
        if self._audio_track:
            pc.addTrack(self._audio_track)
        
        await pc.setRemoteDescription(offer)
        answer = await pc.createAnswer()
        await pc.setLocalDescription(answer)
        
        logger.info(f"【WebRTCStreamer】新客户端已连接。总客户端数: {len(self._pcs)}")
        
        return web.Response(
            content_type="application/json",
            text=json.dumps({"sdp": pc.localDescription.sdp, "type": pc.localDescription.type}),
        )

    def _create_optimized_peer_connection(self):
        """创建优化的RTCPeerConnection"""
        pc = RTCPeerConnection()
        
        # 注意：aiortc的RTCPeerConnection会自动处理ICE配置
        # 不需要手动设置这些属性
        
        logger.info("【WebRTCStreamer】创建优化的WebRTC对等连接")
        return pc
    
    async def _queue_frames(self, bgr_frame, batch_audio_data):
        """转换并排队视频/音频帧。"""
        if self._video_track and bgr_frame is not None:
            # 降低视频分辨率以减少码率
            if bgr_frame.shape[1] > 1280 or bgr_frame.shape[0] > 720:
                bgr_frame = cv2.resize(bgr_frame, (1280, 720))
            
            video_frame = VideoFrame.from_ndarray(bgr_frame, format="bgr24")
            
            # 设置时间戳以进行同步
            video_frame.pts = self._video_pts
            video_frame.time_base = fractions.Fraction(1, 90000)
            self._video_pts += int(video_frame.time_base.denominator / self._video_fps)

            # 添加帧率控制，避免发送过快
            try:
                await self._video_track.queue_frame(video_frame)
            except asyncio.QueueFull:
                logger.warning("【WebRTCStreamer】视频队列已满，跳过当前帧")
                return

        if self._audio_track and batch_audio_data is not None and len(batch_audio_data) > 0:
            try:
                concatenated_audio = np.concatenate(batch_audio_data)
                audio_int16 = np.clip(concatenated_audio * 32767, -32768, 32767).astype(np.int16)
                
                # 将音频数据分块为WebRTC友好的20ms小块
                samples_per_20ms = int(self._audio_sample_rate * 0.02)
                
                offset = 0
                while offset < len(audio_int16):
                    chunk = audio_int16[offset:offset + samples_per_20ms]
                    num_samples = len(chunk)

                    if num_samples > 0:
                        audio_frame = AudioFrame(format='s16', layout='mono', samples=num_samples)
                        audio_frame.planes[0].update(chunk.tobytes())
                        audio_frame.sample_rate = self._audio_sample_rate
                        
                        # 设置时间戳
                        audio_frame.pts = self._audio_pts
                        audio_frame.time_base = fractions.Fraction(1, self._audio_sample_rate)
                        self._audio_pts += num_samples
                        
                        try:
                            await self._audio_track.queue_frame(audio_frame)
                        except asyncio.QueueFull:
                            logger.warning("【WebRTCStreamer】音频队列已满，跳过当前音频块")
                            break

                    offset += samples_per_20ms
                    
            except Exception as e:
                logger_error(f"【WebRTCStreamer】处理音频数据时出错: {e}")


    async def _shutdown(self):
        """用于关闭所有对等连接的协程。"""
        tasks = [pc.close() for pc in self._pcs]
        await asyncio.gather(*tasks, return_exceptions=True)
        self._pcs.clear()
        logger.info("【WebRTCStreamer】所有对等连接已关闭。")


def logger_error(E):
    def locate_exception():
        """
        locate error filename object line
        """
        import sys, os
        exc_type, exc_obj, exc_tb = sys.exc_info()
        if exc_tb is not None:
            fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
            return f"{exc_type.__name__}, in {fname}, line {exc_tb.tb_lineno}"
        else:
            return "No active exception"
    
    error_location = locate_exception()
    logger.error(f"【Error Location】: {error_location}")
    logger.error(f"【Error Details】: {E}")


def get_one_complete_img(image, mask_B_pre, box_info,image_id):
    # 3. 融合 - 内存优化版本
    y1, y2, x1, x2 = box_info # 裁剪人脸框
    face_box_width,face_box_height = y2 - y1,x2 - x1
    
    try:
        # 内存优化：直接修改原图像，避免创建副本
        img_h, img_w = image.shape[:2]
        
        img_paste_start_row = max(0, x1)
        img_paste_end_row = min(img_h, x2)
        img_paste_start_col = max(0, y1)
        img_paste_end_col = min(img_w, y2)

        src_start_row = -x1 if x1 < 0 else 0
        src_start_col = -y1 if y1 < 0 else 0

        paste_height = img_paste_end_row - img_paste_start_row
        paste_width = img_paste_end_col - img_paste_start_col
        
        src_end_row = src_start_row + paste_height
        src_end_col = src_start_col + paste_width

        if paste_height > 0 and paste_width > 0 and src_end_row <= mask_B_pre.shape[0] and src_end_col <= mask_B_pre.shape[1]:
            # 内存优化：只在需要时调整大小
            if mask_B_pre.shape[:2] != (face_box_height, face_box_width):
                mask_B_pre_resize = cv2.resize(mask_B_pre, (face_box_width, face_box_height))
            else:
                mask_B_pre_resize = mask_B_pre
                
            # 直接修改原图像
            image[img_paste_start_row:img_paste_end_row, img_paste_start_col:img_paste_end_col] = \
                mask_B_pre_resize[src_start_row:src_end_row, src_start_col:src_end_col]
        else:
            logger.warning(f"[Frame {image_id}: Invalid dimensions for pasting face mask. Skipping blend.")

    except cv2.error as e_resize:
        logger.error(f"[ Frame {image_id}: cv2.resize error for mask_B_pre: {e_resize}. Skipping blend.")

    return image


def get_complete_imgs(output_img_list, drivered_imgs_for_batch, params, no_face_indices_for_batch, work_id_for_logging):
    t1 = time.time()
    out_shape, output_resize, _, Y1_list, Y2_list, X1_list, X2_list = params
    complete_imgs = []

    if not drivered_imgs_for_batch:
        logger.error(f"[{work_id_for_logging}] get_complete_imgs: drivered_imgs_for_batch is empty.")
        return complete_imgs
    
    # The BBox lists in `params` are for the *entire* video, so we need to get the correct slice
    start_frame_index = -1 # We need this info. Let's assume it's passed somehow. For now, this is a bug.
                          # Correction: let's pass it in.
    
    # We will get the start_index from the call site.
    # Let's adjust the signature to be:
    # get_complete_imgs(output_img_list, drivered_imgs_for_batch, params, no_face_indices_for_batch, work_id_for_logging, start_frame_index)
    # Actually, the face_params are already sliced in the processor. We can assume the lists are correctly sized.
    
    for i, image in enumerate(drivered_imgs_for_batch):
        if i in no_face_indices_for_batch:
            complete_imgs.append(image)
            continue

        image = image.copy()
        if i >= len(output_img_list) or output_img_list[i] is None:
            logger.warning(f"[{work_id_for_logging}] Frame {i}: No valid raw face region found. Using driver frame.")
        else:
            mask_B_pre = output_img_list[i]
            if i < len(Y1_list):
                y1, y2, x1, x2 = (Y1_list[i], Y2_list[i], X1_list[i], X2_list[i])
                image = get_one_complete_img(image, mask_B_pre, (y1, y2, x1, x2), f"{work_id_for_logging}-frame{i}")
            else:
                 logger.error(f"[{work_id_for_logging}] Frame {i}: Index out of bounds for bounding box lists.")

        complete_imgs.append(image)

    t2 = time.time()
    logger.info(f"[{work_id_for_logging}] get_complete_imgs processed {len(complete_imgs)} frames, 耗时: {t2-t1:.2f}秒")
    return complete_imgs

