# -*-coding: utf-8 -*-
# @Time :2025-07-02
# <AUTHOR> l<PERSON><PERSON>jing
# @Email : <EMAIL>
# @File : encoder.py


import time
import os
import cv2
import sys
import numpy as np
import subprocess
import queue  # 添加queue导入
import copy
import shutil
import socket
import gc, multiprocessing, os, subprocess, threading, time, traceback,sys

from loguru import logger
from h_utils.custom import CustomError
from service.webrtc import WebRTCStreamer


#
def logger_error(E):
    def locate_exception():
        """
        locate error filename object line
        """
        exc_type, exc_obj, exc_tb = sys.exc_info()
        fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
        return "{},{},{}".format(exc_type, fname, exc_tb.tb_lineno)
    logger.error(locate_exception())
    logger.error("error {}".format(E))



class VideoEncoder:
    """
    一个长期运行的类，通过队列接收指令和数据来处理视频写入任务。
    """
    def __init__(self, output_imgs_queue, result_queue):
        self.output_imgs_queue = output_imgs_queue
        self.result_queue = result_queue
        self.video_writer = None
        self.status_code = None
        self.is_stream = False
        self.work_id = "VidEnc"  # 用于日志打印
        self.last_send_time = time.time()
        self.send_time_list = [] # 记录发送帧的时间
        self.rtp_streamer = None  # 推流器
        
        # 新增：队列管理和性能监控
        self.max_queue_size =4  # 降低最大队列大小，减少延迟
        self.frame_drop_count = 0  # 丢帧统计
        self.process_count = 0    # 处理帧数统计
        self.last_queue_check = time.time()  # 上次队列检查时间
        
        # 内存管理参数
        self.memory_check_interval = 1000  # 每处理50帧检查一次内存/40s
        self.last_memory_check = 0
        self.gc_interval = 4  # 每4帧进行一次垃圾回收

    def run(self): 
        """主循环，等待并处理任务。"""
        while True:
            try:
               
                self.status_code, reason, data_tuple = self.output_imgs_queue.get(block=True)


                if reason is not None: # 报错直接退出
                    logger.error(f"【VideoEncoder】 处理错误: {reason}")
                    self.result_queue.put([False, f"【VideoEncoder】 处理错误: {reason}"])
                    continue

                # 正常运行状态
                # 1. 初始化任务
                if self.status_code == 'start':
                    self._initialize_task(data_tuple)
                # 2. 循环处理当前任务的视频帧
                elif self.status_code in ['data', 'silence']:
                    self._process_frames_loop(data_tuple)
  
                # 4. 收尾工作
                elif self.status_code == 'finish':
                    self._finalize_task(data_tuple)
                    self._cleanup_after_task()

            except Exception as e:
                logger_error(f"【{self.work_id}】 VideoEncoder 处理失败: {e}")
                self.result_queue.put([False, f"【{self.work_id}】 VideoEncoder 处理失败: {e}"])

    def _initialize_task(self, task_info):
        """从'start'消息中解包参数并初始化视频写入器。"""

        self.task_id = task_info['work_id']
        self.audio_path = task_info['audio_path']
        self.original_frame_shape_init = task_info.get('original_frame_shape_init', (0,0))
        self.width = self.original_frame_shape_init[1]
        self.height = self.original_frame_shape_init[0]
        self.fps = task_info['fps'] 
        self.is_stream = task_info['is_stream']
        self.use_npy = task_info['use_npy']
        
        # 初始化推流器（如果是流式模式且有推流URL）
        self.stream_url = task_info.get('stream_url',None)
        self.streamer_type = task_info.get('streamer_type', 'webrtc')  # 默认使用webrtc
        
        if self.is_stream and not self.stream_url:
            logger.error(f"【{self.work_id}】 推流URL为空")
            self.result_queue.put([False, f"【{self.work_id}】 推流URL为空"])
            return

        if self.is_stream and self.stream_url:

            # elif self.streamer_type == 'webrtc':
            self.pl_streamer = WebRTCStreamer(
                    signaling_url=self.stream_url,
                    audio_sample_rate=16000, # 假设采样率为16k
                    video_fps=self.fps
                )
            logger.info(f"【{self.work_id}】 使用统一的WebRTCStreamer进行推流")
            # else:  # 'ffmpeg' 或其他
            #     # 使用FFmpeg推流器（原始实现）
            #     self.rtp_streamer = RTPStreamer(
            #         self.stream_url,
            #         self.width,
            #         self.height,
            #         self.fps
            #     )
            #     logger.info(f"【**VideoEncoder**】【{self.work_id}】 使用RTPStreamer进行推流 (音视频)")
            #
            try:
                if not self.pl_streamer.start():
                    logger_error(f"【{self.work_id}】 推流器启动失败")
                    # 如果启动失败，设置为None，避免后续调用出错
                    self.pl_streamer = None
                else:
                    logger.info(f"【{self.work_id}】 WebRTC推流器启动成功")
            except Exception as e:
                logger_error(f"【{self.work_id}】 推流器启动异常: {e}")
                # 如果启动异常，设置为None，避免后续调用出错
                self.pl_streamer = None
            
        
        # todo 这里需要优化，兼容is_stream
        self.temp_video_path = os.path.join("workspace",  f'{self.work_id}-temp.mp4')
        self.final_path = os.path.join("workspace", f'{self.work_id}-result.mp4') 
        if not self.is_stream:      
            fourcc = (cv2.VideoWriter_fourcc)(*"mp4v")
            self.video_writer = cv2.VideoWriter(self.temp_video_path, fourcc, self.fps, (self.width, self.height))
            logger.info(f"【{self.work_id}非流式】 视频写入器初始化完成: {self.temp_video_path}")
        logger.info(f"【{self.work_id}非流式】 视频写入器初始化完成: {self.temp_video_path}")

    def _process_frames_loop(self,data_tuple):
        """处理一个批次的视频帧。"""
        drivered_fnames_list, face_params_tuple, processed_raw_face_regions, no_face_indices, use_npy_flag,batch_audio_data = data_tuple
        
        # 添加日志以验证音频数据
        # if batch_audio_data is not None and len(batch_audio_data) > 0:
            # logger.info(f"【{self.work_id}】接收到批处理的音频数据。块数: {len(batch_audio_data)}")
        # else:
        #     logger.warning(f"【{self.work_id}】收到的批处理中没有音频数据。")

        out_shape, output_resize, y1_list, y2_list, x1_list, x2_list = face_params_tuple
        
        t_start_batch = time.time()
        processed_frames = 0
    
        
        for i, (fname, audio_data) in enumerate(zip(drivered_fnames_list, batch_audio_data)):
            try:
                image = np.load(fname) if self.use_npy else cv2.imread(fname)
                if image is None: raise CustomError(f"Failed to load image: {fname}")
            except Exception as e:
                logger_error(f"【{self.work_id}】 加载驱动图像失败: {fname}: {e}")
                continue
           
            if i not in no_face_indices:
                image = get_one_complete_img(image, processed_raw_face_regions[i], (y1_list[i], y2_list[i], x1_list[i], x2_list[i]), os.path.basename(fname))
            
            # print("!!!!!!!!!!!!!!!!!!!!!!!audio_data",len(audio_data),max(audio_data),min(audio_data),self.pl_streamer.is_running)
            # 推流（如果启用）
            if self.pl_streamer and self.pl_streamer.is_running:
                try:
                    # 检查连接状态
                    conn_status = self.pl_streamer.get_connection_status()
                    if not conn_status['is_connected']:
                        if self.process_count % 100 == 0:  # 每100帧输出一次
                            logger.warning(f"【{self.work_id}】WebRTC未连接，信令地址: {conn_status['signaling_url']}")
                    else:
                        # 添加额外的状态检查，确保推流器完全就绪
                        if hasattr(self.pl_streamer, '_loop') and self.pl_streamer._loop and self.pl_streamer._loop.is_running():
                            self.pl_streamer.write_frame(image, audio_data)
                        else:
                            self.process_count += 1
                            if self.process_count % 100 == 0:  # 每100帧输出一次
                                logger.warning(f"【{self.work_id}】WebRTC事件循环未就绪，跳过推流")
                except Exception as e:
                    if self.process_count % 100 == 0:  # 每100帧输出一次
                        logger.error(f"【{self.work_id}】WebRTC推流失败: {e}")
                    # 继续处理，不中断整个流程
            else:
                # 写入视频文件
                self.video_writer.write(image) 
            
            processed_frames += 1
            self.process_count += 1
            self.last_frame_time = time.time()
            self.send_time_list.append(time.time())
            if len(self.send_time_list) > 10:
                self.send_time_list.pop(0)
            
            # 内存优化：每处理4帧进行一次垃圾回收
            if processed_frames % 100 == 0:
                gc.collect()
        
        # 性能统计
        batch_time = time.time() - t_start_batch
        current_queue_size = self.output_imgs_queue.qsize()
        
        # 计算实时FPS
        if self.process_count %100 == 0:
            if len(self.send_time_list) >= 2:
                time_span = self.send_time_list[-1] - self.send_time_list[0]
                real_fps = len(self.send_time_list) / max(time_span, 0.001)
            else:
                real_fps = 0
            # 丰富打印信息
            if self.is_stream:
                is_connected = "已连接" if self.pl_streamer.is_connected else "未连接"
                frame_drop_count = self.pl_streamer.get_total_drop_count()
            else:
                is_connected = "非流式"
                frame_drop_count = self.frame_drop_count
            logger.info(f"【{self.work_id}-{is_connected}】处理{processed_frames}帧, 耗时{batch_time:.3f}s, "
                    f"队列:{current_queue_size}, 实时FPS:{real_fps:.1f}, 累计:{self.process_count}帧, 丢帧:{frame_drop_count}")
            
        # 如果队列持续积压，发出警告
        if current_queue_size > 6:
            logger.warning(f"【VidEnc】队列积压警告: {current_queue_size}个数据包待处理，建议检查推流性能")
        
        # 内存监控
        if self.process_count - self.last_memory_check >= self.memory_check_interval:
            self._check_memory_usage()
            self.last_memory_check = self.process_count

    def _check_memory_usage(self):
        """检查内存使用情况"""
        try:
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024
            logger.info(f"【VideoEncoder】内存使用: {memory_mb:.1f} MB")
            
            # 如果内存使用过高，强制垃圾回收
            if memory_mb > 1000:  # 超过1GB
                logger.warning(f"【VideoEncoder】内存使用过高: {memory_mb:.1f} MB，执行强制垃圾回收")
                gc.collect()
        except ImportError:
            logger.warning("【VideoEncoder】psutil未安装，无法监控内存使用")
        except Exception as e:
            logger.warning(f"【VideoEncoder】内存监控失败: {e}")

    def _finalize_task(self,data_tuple):
        """完成视频的收尾工作，如合并音频。"""
        if not self.is_stream and os.path.exists(self.temp_video_path):
            logger.info(f"[{self.work_id}] Merging audio from '{self.audio_path}' to create final video at '{self.final_path}'")
            cmd = f'ffmpeg -y -i "{self.temp_video_path}" -i "{self.audio_path}" -c:v copy -c:a aac -strict experimental "{self.final_path}"'
            subprocess.call(cmd, shell=True)
            self.result_queue.put([True, self.final_path])
        else:
            self.result_queue.put([True, "Stream finished." if self.is_stream else "Task finished."])

    def _cleanup_after_task(self):
        """在每个任务结束后（无论成功与否）进行清理。"""
        if self.video_writer :
            try:
                self.video_writer.release()
            except Exception as e:
                pass

        # 关闭推流器
        if self.rtp_streamer:
            try:
                self.rtp_streamer.stop()
                self.rtp_streamer = None
                logger.info(f"【VideoEncoder】【{self.work_id}】 推流器已关闭")
            except Exception as e:
                logger_error(f"【VideoEncoder】【{self.work_id}】 关闭推流器失败: {e}")
        if self.video_writer :
            try:
                self.video_writer.release()
            except Exception as e:
                pass

        logger.info(">>>>>>>>>>>>>>>>>>>>>>>>>>【VideoEncoder】【{self.work_id}】 任务处理完成. 等待新任务.<<<<<<<<<<<<<<<<<<<<<<<<")
        self.work_id = "VideoEncoder" # 重置 work_id




def logger_error(E):
    def locate_exception():
        """
        locate error filename object line
        """
        import sys, os
        exc_type, exc_obj, exc_tb = sys.exc_info()
        if exc_tb is not None:
            fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
            return f"{exc_type.__name__}, in {fname}, line {exc_tb.tb_lineno}"
        else:
            return "No active exception"
    
    error_location = locate_exception()
    logger.error(f"【Error Location】: {error_location}")
    logger.error(f"【Error Details】: {E}")


def get_one_complete_img(image, mask_B_pre, box_info,image_id):
    # 3. 融合 - 内存优化版本
    y1, y2, x1, x2 = box_info # 裁剪人脸框
    face_box_width,face_box_height = y2 - y1,x2 - x1
    
    try:
        # 内存优化：直接修改原图像，避免创建副本
        img_h, img_w = image.shape[:2]
        
        img_paste_start_row = max(0, x1)
        img_paste_end_row = min(img_h, x2)
        img_paste_start_col = max(0, y1)
        img_paste_end_col = min(img_w, y2)

        src_start_row = -x1 if x1 < 0 else 0
        src_start_col = -y1 if y1 < 0 else 0

        paste_height = img_paste_end_row - img_paste_start_row
        paste_width = img_paste_end_col - img_paste_start_col
        
        src_end_row = src_start_row + paste_height
        src_end_col = src_start_col + paste_width

        if paste_height > 0 and paste_width > 0 and src_end_row <= mask_B_pre.shape[0] and src_end_col <= mask_B_pre.shape[1]:
            # 内存优化：只在需要时调整大小
            if mask_B_pre.shape[:2] != (face_box_height, face_box_width):
                mask_B_pre_resize = cv2.resize(mask_B_pre, (face_box_width, face_box_height))
            else:
                mask_B_pre_resize = mask_B_pre
                
            # 直接修改原图像
            image[img_paste_start_row:img_paste_end_row, img_paste_start_col:img_paste_end_col] = \
                mask_B_pre_resize[src_start_row:src_end_row, src_start_col:src_end_col]
        else:
            logger.warning(f"[Frame {image_id}: Invalid dimensions for pasting face mask. Skipping blend.")

    except cv2.error as e_resize:
        logger.error(f"[ Frame {image_id}: cv2.resize error for mask_B_pre: {e_resize}. Skipping blend.")

    return image


def get_complete_imgs(output_img_list, drivered_imgs_for_batch, params, no_face_indices_for_batch, work_id_for_logging):
    t1 = time.time()
    out_shape, output_resize, _, Y1_list, Y2_list, X1_list, X2_list = params
    complete_imgs = []

    if not drivered_imgs_for_batch:
        logger.error(f"[{work_id_for_logging}] get_complete_imgs: drivered_imgs_for_batch is empty.")
        return complete_imgs
    
    # The BBox lists in `params` are for the *entire* video, so we need to get the correct slice
    start_frame_index = -1 # We need this info. Let's assume it's passed somehow. For now, this is a bug.
                          # Correction: let's pass it in.
    
    # We will get the start_index from the call site.
    # Let's adjust the signature to be:
    # get_complete_imgs(output_img_list, drivered_imgs_for_batch, params, no_face_indices_for_batch, work_id_for_logging, start_frame_index)
    # Actually, the face_params are already sliced in the processor. We can assume the lists are correctly sized.
    
    for i, image in enumerate(drivered_imgs_for_batch):
        if i in no_face_indices_for_batch:
            complete_imgs.append(image)
            continue

        image = image.copy()
        if i >= len(output_img_list) or output_img_list[i] is None:
            logger.warning(f"[{work_id_for_logging}] Frame {i}: No valid raw face region found. Using driver frame.")
        else:
            mask_B_pre = output_img_list[i]
            if i < len(Y1_list):
                y1, y2, x1, x2 = (Y1_list[i], Y2_list[i], X1_list[i], X2_list[i])
                image = get_one_complete_img(image, mask_B_pre, (y1, y2, x1, x2), f"{work_id_for_logging}-frame{i}")
            else:
                 logger.error(f"[{work_id_for_logging}] Frame {i}: Index out of bounds for bounding box lists.")

        complete_imgs.append(image)

    t2 = time.time()
    logger.info(f"[{work_id_for_logging}] get_complete_imgs processed {len(complete_imgs)} frames, 耗时: {t2-t1:.2f}秒")
    return complete_imgs

