# -*-coding: utf-8 -*-
# @Time :2025-07-02
# <AUTHOR> lishengjing
# @Email : <EMAIL>
# @File : encoder.py


import time
import os
import cv2
import sys
import numpy as np
import subprocess
import queue  # 添加queue导入
import copy
import shutil
import socket
import gc, multiprocessing, os, subprocess, threading, time, traceback,sys
import json
import uuid
from weakref import WeakSet
from typing import Dict
from loguru import logger
from h_utils.custom import CustomError
import fractions

import asyncio
from aiohttp import web
from aiortc import RTCPeerConnection, RTCSessionDescription
from aiortc.contrib.media import MediaStreamTrack
from av import VideoFrame, AudioFrame

#
def logger_error(E):
    def locate_exception():
        """
        locate error filename object line
        """
        exc_type, exc_obj, exc_tb = sys.exc_info()
        fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
        return "{},{},{}".format(exc_type, fname, exc_tb.tb_lineno)
    logger.error(locate_exception())
    logger.error("error {}".format(E))



class VideoEncoder:
    """
    一个长期运行的类，通过队列接收指令和数据来处理视频写入任务。
    """
    def __init__(self, output_imgs_queue, result_queue):
        self.output_imgs_queue = output_imgs_queue
        self.result_queue = result_queue
        self.video_writer = None
        self.status_code = None
        self.is_stream = False
        self.work_id = "VideoEncoder"  # 用于日志打印
        self.last_send_time = time.time()
        self.send_time_list = [] # 记录发送帧的时间
        self.rtp_streamer = None  # 推流器
        
        # 新增：队列管理和性能监控
        self.max_queue_size = 8  # 降低最大队列大小，减少延迟
        self.frame_drop_count = 0  # 丢帧统计
        self.process_count = 0    # 处理帧数统计
        self.last_queue_check = time.time()  # 上次队列检查时间
        
        # 内存管理参数
        self.memory_check_interval = 1000  # 每处理50帧检查一次内存/40s
        self.last_memory_check = 0
        self.gc_interval = 4  # 每4帧进行一次垃圾回收

    def run(self): 
        """主循环，等待并处理任务。"""
        while True:
            try:
               
                self.status_code, reason, data_tuple = self.output_imgs_queue.get(block=True)


                if reason is not None: # 报错直接退出
                    logger.error(f"【VideoEncoder】 处理错误: {reason}")
                    self.result_queue.put([False, f"【VideoEncoder】 处理错误: {reason}"])
                    continue

                # 正常运行状态
                # 1. 初始化任务
                if self.status_code == 'start':
                    self._initialize_task(data_tuple)
                # 2. 循环处理当前任务的视频帧
                elif self.status_code in ['data', 'silence']:
                    self._process_frames_loop(data_tuple)
  
                # 4. 收尾工作
                elif self.status_code == 'finish':
                    self._finalize_task(data_tuple)
                    self._cleanup_after_task()

            except Exception as e:
                logger_error(f"【{self.work_id}】 VideoEncoder 处理失败: {e}")
                self.result_queue.put([False, f"【{self.work_id}】 VideoEncoder 处理失败: {e}"])

    def _initialize_task(self, task_info):
        """从'start'消息中解包参数并初始化视频写入器。"""

        self.work_id = task_info['work_id']
        self.audio_path = task_info['audio_path']
        self.original_frame_shape_init = task_info.get('original_frame_shape_init', (0,0))
        self.width = self.original_frame_shape_init[1]
        self.height = self.original_frame_shape_init[0]
        self.fps = task_info['fps'] 
        self.is_stream = task_info['is_stream']
        self.use_npy = task_info['use_npy']
        
        # 初始化推流器（如果是流式模式且有推流URL）
        self.stream_url = task_info.get('stream_url',None)
        self.streamer_type = task_info.get('streamer_type', 'webrtc')  # 默认使用webrtc
        
        if self.is_stream and not self.stream_url:
            logger.error(f"【**VideoEncoder**】【{self.work_id}】 推流URL为空")
            self.result_queue.put([False, f"【VideoEncoder】 推流URL为空"])
            return

        if self.is_stream and self.stream_url:
            if self.streamer_type == 'opencv':
                # 使用OpenCV推流器（推荐）
                self.rtp_streamer = OpenCVStreamer(
                    self.stream_url,
                    self.width,
                    self.height,
                    self.fps
                )
                logger.info(f"【**VideoEncoder**】【{self.work_id}】 使用OpenCVStreamer进行推流")
            elif self.streamer_type == 'webrtc':
                self.rtp_streamer = WebRTCStreamer(
                    signaling_url=self.stream_url,
                    audio_sample_rate=16000, # 假设采样率为16k
                    video_fps=self.fps
                )
                logger.info(f"【**VideoEncoder**】【{self.work_id}】 使用统一的WebRTCStreamer进行推流")
            else:  # 'ffmpeg' 或其他
                # 使用FFmpeg推流器（原始实现）
                self.rtp_streamer = RTPStreamer(
                    self.stream_url,
                    self.width,
                    self.height,
                    self.fps
                )
                logger.info(f"【**VideoEncoder**】【{self.work_id}】 使用RTPStreamer进行推流 (音视频)")
            
            if not self.rtp_streamer.start():
                logger_error(f"【**VideoEncoder**】【{self.work_id}】 推流器启动失败")
            
        
        # todo 这里需要优化，兼容is_stream
        self.temp_video_path = os.path.join("workspace",  f'{self.work_id}-temp.mp4')
        self.final_path = os.path.join("workspace", f'{self.work_id}-result.mp4') 
        if not self.is_stream:      
            fourcc = (cv2.VideoWriter_fourcc)(*"mp4v")
            self.video_writer = cv2.VideoWriter(self.temp_video_path, fourcc, self.fps, (self.width, self.height))
            logger.info(f"【**VideoEncoder非流式**】【{self.work_id}】 视频写入器初始化完成: {self.temp_video_path}")
        logger.info(f"【**VideoEncoder初始化**】【{self.work_id}】 视频写入器初始化完成: {self.temp_video_path}")

    def _process_frames_loop(self,data_tuple):
        """处理一个批次的视频帧。"""
        drivered_fnames_list, face_params_tuple, processed_raw_face_regions, no_face_indices, use_npy_flag,batch_audio_data = data_tuple
        
        # 添加日志以验证音频数据
        if batch_audio_data is not None and len(batch_audio_data) > 0:
            logger.info(f"【VideoEncoder】接收到批处理的音频数据。块数: {len(batch_audio_data)}")
        else:
            logger.warning("【VideoEncoder】收到的批处理中没有音频数据。")

        out_shape, output_resize, y1_list, y2_list, x1_list, x2_list = face_params_tuple
        
        t_start_batch = time.time()
        processed_frames = 0
        
        # # 内存优化：检查队列大小，如果积压过多则跳过部分帧
        # current_queue_size = self.output_imgs_queue.qsize()
        # if current_queue_size > self.max_queue_size:
        #     logger.warning(f"【VideoEncoder】队列积压严重: {current_queue_size}个数据包，将跳过部分帧以释放内存")
        #     # 跳过部分帧以减少内存压力
        #     skip_ratio = min(0.5, current_queue_size / (self.max_queue_size * 2))
        #     frames_to_process = max(1, int(len(drivered_fnames_list) * (1 - skip_ratio)))
        #     drivered_fnames_list = drivered_fnames_list[:frames_to_process]
        #     self.frame_drop_count += len(drivered_fnames_list) - frames_to_process
        
        for i, fname in enumerate(drivered_fnames_list):
            try:
                image = np.load(fname) if self.use_npy else cv2.imread(fname)
                if image is None: raise CustomError(f"Failed to load image: {fname}")
            except Exception as e:
                logger_error(f"【{self.work_id}】 加载驱动图像失败: {fname}: {e}")
                continue

            if i not in no_face_indices:
                image = get_one_complete_img(image, processed_raw_face_regions[i], (y1_list[i], y2_list[i], x1_list[i], x2_list[i]), os.path.basename(fname))
            
            # 推流（如果启用）
            if self.rtp_streamer and self.rtp_streamer.is_running:
                self.rtp_streamer.write_frame(image,batch_audio_data)
            else:
                # 写入视频文件
                self.video_writer.write(image) 
            
            processed_frames += 1
            self.process_count += 1
            self.last_frame_time = time.time()
            self.send_time_list.append(time.time())
            if len(self.send_time_list) > 10:
                self.send_time_list.pop(0)
            
            # 内存优化：每处理4帧进行一次垃圾回收
            if processed_frames % 100 == 0:
                gc.collect()
        
        # 性能统计
        batch_time = time.time() - t_start_batch
        current_queue_size = self.output_imgs_queue.qsize()
        
        # 计算实时FPS
        if len(self.send_time_list) >= 2:
            time_span = self.send_time_list[-1] - self.send_time_list[0]
            real_fps = len(self.send_time_list) / max(time_span, 0.001)
        else:
            real_fps = 0
        
        logger.info(f"【VideoEncoder】处理{processed_frames}帧, 耗时{batch_time:.3f}s, "
                   f"队列:{current_queue_size}, 实时FPS:{real_fps:.1f}, 累计:{self.process_count}帧, 丢帧:{self.frame_drop_count}")
        
        # 如果队列持续积压，发出警告
        if current_queue_size > 6:
            logger.warning(f"【VideoEncoder】队列积压警告: {current_queue_size}个数据包待处理，建议检查推流性能")
        
        # 内存监控
        if self.process_count - self.last_memory_check >= self.memory_check_interval:
            self._check_memory_usage()
            self.last_memory_check = self.process_count

    def _check_memory_usage(self):
        """检查内存使用情况"""
        try:
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()
            memory_mb = memory_info.rss / 1024 / 1024
            logger.info(f"【VideoEncoder】内存使用: {memory_mb:.1f} MB")
            
            # 如果内存使用过高，强制垃圾回收
            if memory_mb > 1000:  # 超过1GB
                logger.warning(f"【VideoEncoder】内存使用过高: {memory_mb:.1f} MB，执行强制垃圾回收")
                gc.collect()
        except ImportError:
            logger.warning("【VideoEncoder】psutil未安装，无法监控内存使用")
        except Exception as e:
            logger.warning(f"【VideoEncoder】内存监控失败: {e}")

    def _finalize_task(self,data_tuple):
        """完成视频的收尾工作，如合并音频。"""
        if not self.is_stream and os.path.exists(self.temp_video_path):
            logger.info(f"[{self.work_id}] Merging audio from '{self.audio_path}' to create final video at '{self.final_path}'")
            cmd = f'ffmpeg -y -i "{self.temp_video_path}" -i "{self.audio_path}" -c:v copy -c:a aac -strict experimental "{self.final_path}"'
            subprocess.call(cmd, shell=True)
            self.result_queue.put([True, self.final_path])
        else:
            self.result_queue.put([True, "Stream finished." if self.is_stream else "Task finished."])

    def _cleanup_after_task(self):
        """在每个任务结束后（无论成功与否）进行清理。"""
        if self.video_writer :
            try:
                self.video_writer.release()
            except Exception as e:
                pass

        # 关闭推流器
        if self.rtp_streamer:
            try:
                self.rtp_streamer.stop()
                self.rtp_streamer = None
                logger.info(f"【VideoEncoder】【{self.work_id}】 推流器已关闭")
            except Exception as e:
                logger_error(f"【VideoEncoder】【{self.work_id}】 关闭推流器失败: {e}")
        if self.video_writer :
            try:
                self.video_writer.release()
            except Exception as e:
                pass

        logger.info(">>>>>>>>>>>>>>>>>>>>>>>>>>【VideoEncoder】【{self.work_id}】 任务处理完成. 等待新任务.<<<<<<<<<<<<<<<<<<<<<<<<")
        self.work_id = "VideoEncoder" # 重置 work_id




# 添加推流类 - OpenCV版本
class OpenCVStreamer:
    """
    基于OpenCV的简单推流类，适用于RTMP等协议
    """
    def __init__(self, stream_url, width=1920, height=1080, fps=25, audio_sample_rate=16000):
        self.stream_url = stream_url
        self.width = width
        self.height = height
        self.fps = fps
        self.audio_sample_rate = audio_sample_rate
        self.is_running = False
        self.frame_count = 0
        
        # OpenCV VideoWriter
        self.video_writer = None
        
        # 音频处理 - 使用subprocess调用ffmpeg处理音频
        self.ffmpeg_process = None # 重命名 self.audio_process
        
        logger.info(f"【OpenCVStreamer】初始化. URL: {self.stream_url}, Res: {width}x{height}, FPS: {fps}")

    def start(self):
        """启动推流"""
        try:
            # 对于RTSP或RTMP推流，使用FFmpeg
            if self.stream_url.startswith('rtsp://') or self.stream_url.startswith('rtmp://'):
                return self._start_ffmpeg_pusher()
            else:
                # 对于文件等其他情况，使用OpenCV（仅视频）
                fourcc = cv2.VideoWriter_fourcc(*'H264')
                self.video_writer = cv2.VideoWriter(
                    self.stream_url, 
                    fourcc, 
                    self.fps, 
                    (self.width, self.height)
                )
                if not self.video_writer.isOpened():
                    logger.error(f"【OpenCVStreamer】无法打开输出流: {self.stream_url}")
                    return False
                
                self.is_running = True
                logger.info(f"【OpenCVStreamer】OpenCV写入器已启动: {self.stream_url}")
                return True
                
        except Exception as e:
            logger_error(f"【OpenCVStreamer】启动推流失败: {e}")
            return False

    def _start_ffmpeg_pusher(self):
        """启动FFmpeg推流进程 (目前仅支持纯音频)"""
        try:
            import subprocess
            
            # 仅音频推流命令
            cmd = [
                'ffmpeg', '-re',
                '-f', 's16le',
                '-ar', str(self.audio_sample_rate),
                '-ac', '1',
                '-i', 'pipe:0',  # 从标准输入读取音频数据
                '-c:a', 'aac',
                '-b:a', '128k',
                '-f', 'rtsp' if self.stream_url.startswith('rtsp://') else 'flv',
                self.stream_url
            ]

            if self.stream_url.startswith('rtsp://'):
                cmd.extend(['-rtsp_transport', 'tcp'])

            logger.info(f"【OpenCVStreamer】FFmpeg命令 (纯音频): {' '.join(cmd)}")
            
            self.ffmpeg_process = subprocess.Popen(
                cmd,
                stdin=subprocess.PIPE,
                stdout=subprocess.DEVNULL, # 不需要ffmpeg的stdout
                stderr=subprocess.PIPE
            )
            
            time.sleep(0.5) # 等待ffmpeg启动
            if self.ffmpeg_process.poll() is not None:
                stderr_output = self.ffmpeg_process.stderr.read().decode('utf-8', errors='ignore')
                logger.error(f"【OpenCVStreamer】FFmpeg进程启动失败: {stderr_output}")
                return False
            
            self.is_running = True
            logger.info(f"【OpenCVStreamer】FFmpeg推流已启动到: {self.stream_url}, PID: {self.ffmpeg_process.pid}")
            return True
            
        except Exception as e:
            logger_error(f"【OpenCVStreamer】启动FFmpeg推流失败: {e}")
            return False

    def write_frame(self, frame, batch_audio_data=None):
        """写入视频帧或音频数据"""
        if not self.is_running:
            return False
        
        try:
            if self.video_writer:
                if frame is not None:
                    if frame.shape[:2] != (self.height, self.width):
                        frame = cv2.resize(frame, (self.width, self.height))
                    self.video_writer.write(frame)

            elif self.ffmpeg_process and batch_audio_data:
                try:
                    concatenated_audio = np.concatenate(batch_audio_data)
                    audio_int16 = np.clip(concatenated_audio * 32767, -32768, 32767).astype(np.int16)
                    audio_bytes = audio_int16.tobytes()
                    self.ffmpeg_process.stdin.write(audio_bytes)
                    self.ffmpeg_process.stdin.flush()
                except (IOError, BrokenPipeError):
                    logger.error("【OpenCVStreamer】FFmpeg管道中断")
                    self.is_running = False
                    return False
            
            self.frame_count += 1
            return True
            
        except Exception as e:
            logger_error(f"【OpenCVStreamer】写入数据失败: {e}")
            self.is_running = False # 发生错误时停止运行
            return False

    def stop(self):
        """停止推流"""
        if not self.is_running:
            return
        
        self.is_running = False
        logger.info("【OpenCVStreamer】开始停止推流...")
        
        try:
            if self.video_writer:
                self.video_writer.release()
                self.video_writer = None
                
            if self.ffmpeg_process:
                try:
                    if self.ffmpeg_process.stdin:
                        self.ffmpeg_process.stdin.close()
                    self.ffmpeg_process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    self.ffmpeg_process.kill()
                    self.ffmpeg_process.wait()
                finally:
                    self.ffmpeg_process = None
                    
            logger.info(f"【OpenCVStreamer】推流已停止. 共处理 {self.frame_count} 个数据包.")
            
        except Exception as e:
            logger_error(f"【OpenCVStreamer】停止推流时发生错误: {e}")

# ====================================================================
#  统一、简化的WebRTC实现
# ====================================================================

class MediaRelayTrack(MediaStreamTrack):
    """
    一个中继媒体帧的轨道，用于连接同步和异步代码。
    这个轨道内部包含一个队列，用于实现背压。
    """
    def __init__(self, kind, maxsize=8):
        super().__init__()
        self.kind = kind
        self._queue = asyncio.Queue(maxsize=maxsize)

    async def recv(self):
        """aiortc调用此方法来获取下一帧以发送给客户端。"""
        frame = await self._queue.get()
        return frame

    async def queue_frame(self, frame):
        """生产者（外部代码）调用此方法将帧放入队列。"""
        # 如果队列已满，`put`将会异步等待，直到有空位为止。
        await self._queue.put(frame)

class WebRTCStreamer:
    """
    一个自包含的WebRTC推流器，在后台线程中运行自己的信令服务器和事件循环。
    它使用队列来实现反压，确保稳定性。
    """
    def __init__(self, signaling_url, audio_sample_rate=16000, video_fps=25):
        try:
            # URL格式应为 'host:port'
            host, port_str = signaling_url.split(":")
            port = int(port_str)
        except (ValueError, AttributeError):
            logger.error(f"【WebRTCStreamer】无效的信令URL '{signaling_url}'。将使用默认值 '0.0.0.0:8080'。")
            host, port = "0.0.0.0", 8080
            
        self._host = host
        self._port = port
        self._audio_sample_rate = audio_sample_rate
        self._video_fps = video_fps
        
        self._pcs = WeakSet()
        self._thread = None
        self._loop = None
        self.is_running = False
        
        # 此事件用于发出事件循环已启动的信号
        self._loop_started = threading.Event()

        # 所有对等连接共享的轨道
        self._video_track = None
        self._audio_track = None

        # A/V 同步
        self._video_pts = 0
        self._audio_pts = 0

    def start(self):
        if self.is_running:
            return True
        
        self._thread = threading.Thread(target=self._run_event_loop, daemon=True)
        self._thread.start()
        
        # 等待事件循环准备就绪
        if not self._loop_started.wait(timeout=10):
            logger.error("【WebRTCStreamer】Asyncio事件循环启动超时。")
            self.stop()
            return False
        
        logger.info(f"【WebRTCStreamer】推流器启动成功。信令服务位于 http://{self._host}:{self._port}/offer")
        return True

    def stop(self):
        if not self.is_running:
            return
        
        self.is_running = False
        if self._loop and self._loop.is_running():
            # 安排关闭协程并等待其完成
            future = asyncio.run_coroutine_threadsafe(self._shutdown(), self._loop)
            try:
                future.result(timeout=5)
            except asyncio.TimeoutError:
                logger.warning("【WebRTCStreamer】关闭超时。")

        if self._thread:
            self._thread.join(timeout=5)
        
        logger.info("【WebRTCStreamer】推流器已停止。")

    def write_frame(self, bgr_frame, batch_audio_data):
        if not self.is_running or not self._loop:
            return
            
        # 此方法从VideoEncoder的线程调用。
        # 我们需要安排异步的 `_queue_frames` 协程
        # 在推流器的事件循环上运行。
        asyncio.run_coroutine_threadsafe(
            self._queue_frames(bgr_frame, batch_audio_data), 
            self._loop
        )

    def _run_event_loop(self):
        """在后台线程中运行asyncio事件循环。"""
        self._loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self._loop)
        self.is_running = True
        
        # 在正确的事件循环中创建共享媒体轨道
        self._video_track = MediaRelayTrack(kind="video", maxsize=8)
        self._audio_track = MediaRelayTrack(kind="audio", maxsize=8)
        
        self._loop_started.set() # 发出循环和轨道已准备就绪的信号

        try:
            self._loop.run_until_complete(self._run_server())
        except Exception:
            logger.error(f"【WebRTCStreamer】事件循环崩溃: {traceback.format_exc()}")
        finally:
            self.is_running = False
            logger.info("【WebRTCStreamer】事件循环已关闭。")

    async def _run_server(self):
        """设置并运行aiohttp信令服务器。"""
        app = web.Application()
        app.router.add_post("/offer", self._handle_offer)
        
        runner = web.AppRunner(app)
        await runner.setup()
        site = web.TCPSite(runner, self._host, self._port)
        await site.start()

        # 保持服务器运行直到 stop() 被调用
        while self.is_running:
            await asyncio.sleep(0.1)

        await runner.cleanup()

    async def _handle_offer(self, request):
        """处理来自WebRTC客户端的SDP提议。"""
        params = await request.json()
        offer = RTCSessionDescription(sdp=params["sdp"], type=params["type"])
        
        pc = RTCPeerConnection()
        self._pcs.add(pc)

        @pc.on("connectionstatechange")
        async def on_connectionstatechange():
            logger.info(f"【WebRTCStreamer】对等连接状态: {pc.connectionState}")
            if pc.connectionState in ("failed", "closed", "disconnected"):
                await pc.close()
                self._pcs.discard(pc)

        # 将共享轨道添加到新的对等连接
        if self._video_track:
            pc.addTrack(self._video_track)
        if self._audio_track:
            pc.addTrack(self._audio_track)
        
        await pc.setRemoteDescription(offer)
        answer = await pc.createAnswer()
        await pc.setLocalDescription(answer)
        
        logger.info(f"【WebRTCStreamer】新客户端已连接。总客户端数: {len(self._pcs)}")
        
        return web.Response(
            content_type="application/json",
            text=json.dumps({"sdp": pc.localDescription.sdp, "type": pc.localDescription.type}),
        )
    
    async def _queue_frames(self, bgr_frame, batch_audio_data):
        """转换并排队视频/音频帧。"""
        if self._video_track and bgr_frame is not None:
            video_frame = VideoFrame.from_ndarray(bgr_frame, format="bgr24")
            
            # 设置时间戳以进行同步
            video_frame.pts = self._video_pts
            video_frame.time_base = fractions.Fraction(1, 90000)
            self._video_pts += int(video_frame.time_base.denominator / self._video_fps)

            await self._video_track.queue_frame(video_frame)

        if self._audio_track and batch_audio_data is not None and len(batch_audio_data) > 0:
            try:
                concatenated_audio = np.concatenate(batch_audio_data)
                audio_int16 = np.clip(concatenated_audio * 32767, -32768, 32767).astype(np.int16)
                
                # 将音频数据分块为WebRTC友好的20ms小块
                samples_per_20ms = int(self._audio_sample_rate * 0.02)
                
                offset = 0
                while offset < len(audio_int16):
                    chunk = audio_int16[offset:offset + samples_per_20ms]
                    num_samples = len(chunk)

                    if num_samples > 0:
                        audio_frame = AudioFrame(format='s16', layout='mono', samples=num_samples)
                        audio_frame.planes[0].update(chunk.tobytes())
                        audio_frame.sample_rate = self._audio_sample_rate
                        
                        # 设置时间戳
                        audio_frame.pts = self._audio_pts
                        audio_frame.time_base = fractions.Fraction(1, self._audio_sample_rate)
                        self._audio_pts += num_samples
                        
                        await self._audio_track.queue_frame(audio_frame)

                    offset += num_samples
                    
            except Exception as e:
                logger_error(f"【WebRTCStreamer】处理音频数据时出错: {e}")


    async def _shutdown(self):
        """用于关闭所有对等连接的协程。"""
        tasks = [pc.close() for pc in self._pcs]
        await asyncio.gather(*tasks, return_exceptions=True)
        self._pcs.clear()
        logger.info("【WebRTCStreamer】所有对等连接已关闭。")

# 保留原来的RTPStreamer作为备选方案
class RTPStreamer:
    """
    RTP推流类，使用FFmpeg进行音视频同步推流。
    通过创建TCP套接字将音视频数据传递给FFmpeg，然后将它们复用并通过RTP推送到单个端口。
    """
    def __init__(self, stream_url, width=1920, height=1080, fps=25, audio_sample_rate=16000):
        self.stream_url = stream_url
        self.width = width
        self.height = height
        self.fps = fps
        self.audio_sample_rate = audio_sample_rate
        self.process = None
        self.is_running = False
        self.frame_count = 0
        self.first_audio_processed = False

        # TCP Listener setup
        self.ip = '127.0.0.1'
        # 为视频和音频动态选择可用端口
        self.video_port = self._get_free_port()
        self.audio_port = self._get_free_port()
        
        self.video_sock = None
        self.audio_sock = None
        self.video_conn = None
        self.audio_conn = None
        
        self.connection_thread = None
        self.log_thread = None
        logger.info(f"【RTPStreamer】初始化. URL: {self.stream_url}, Res: {width}x{height}, FPS: {fps}, ListenIP: {self.ip}")

    def _get_free_port(self):
        """获取一个空闲的TCP端口"""
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('', 0))
            return s.getsockname()[1]

    def _start_listeners(self):
        """启动TCP监听器以接受来自FFmpeg的连接"""
        try:
            self.video_sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.video_sock.bind((self.ip, self.video_port))
            self.video_sock.listen(1)

            self.audio_sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.audio_sock.bind((self.ip, self.audio_port))
            self.audio_sock.listen(1)
            logger.info(f"【RTPStreamer】TCP监听器启动: Video@{self.ip}:{self.video_port}, Audio@{self.ip}:{self.audio_port}")
            return True
        except Exception as e:
            logger_error(f"【RTPStreamer】启动TCP监听器失败: {e}")
            return False

    def _accept_connections(self):
        """在一个单独的线程中接受FFmpeg的TCP连接。"""
        logger.info("【RTPStreamer】开始等待FFmpeg连接...")
        try:
            # 设置超时以防FFmpeg未能启动
            self.video_sock.settimeout(20.0)  # 增加超时时间
            logger.info(f"【RTPStreamer】等待FFmpeg连接 (video) on 127.0.0.1:{self.video_port}...")
            self.video_conn, addr = self.video_sock.accept()
            logger.info(f"【RTPStreamer】FFmpeg已连接到视频套接字，来自: {addr}")

            self.audio_sock.settimeout(20.0) # 增加超时时间
            logger.info(f"【RTPStreamer】等待FFmpeg连接 (audio) on 127.0.0.1:{self.audio_port}...")
            self.audio_conn, addr = self.audio_sock.accept()
            logger.info(f"【RTPStreamer】FFmpeg已连接到音频套接字，来自: {addr}")

        except socket.timeout:
            logger.error("【RTPStreamer】等待FFmpeg连接超时。FFmpeg可能未能正确启动或连接。")
            self.stop() # 连接失败时停止所有相关进程
        except Exception as e:
            if self.is_running:
                logger.error(f"【RTPStreamer】接受FFmpeg连接时发生错误: {e}")
                self.stop()

    def _log_ffmpeg_output(self, pipe):
        """在一个线程中运行，读取并记录ffmpeg的stderr输出"""
        logger.info("【RTPStreamer】开始监听FFmpeg输出...")
        try:
            for line in iter(pipe.readline, b''):
                line_str = line.decode('utf-8', errors='ignore').strip()
                if "ffmpeg version" in line_str or "configuration" in line_str:
                     logger.debug(f"[FFMPEG-CONFIG]: {line_str}")
                else:
                    logger.info(f"[FFMPEG]: {line_str}")
            pipe.close()
        except Exception as e:
            if self.is_running:
                logger.error(f"【RTPStreamer】读取ffmpeg stderr时出错: {e}")
        logger.info("【RTPStreamer】FFmpeg输出监听线程结束")

    def write_frame(self, image_np, batch_audio_data):
        """
        将视频帧和音频数据写入FFmpeg。
        此方法将独立处理视频和音频连接，以避免死锁。
        """
        if not self.is_running:
            return

        try:
            # 1. 发送视频数据 (如果视频连接已建立)
            if self.video_conn:
                if image_np.shape[:2] != (self.height, self.width):
                    image_np = cv2.resize(image_np, (self.width, self.height))
                self.video_conn.sendall(image_np.tobytes())
            
            # 2. 发送音频数据 (如果音频连接已建立)
            if self.audio_conn and batch_audio_data:
                concatenated_audio = np.concatenate(batch_audio_data)
                audio_int16 = np.clip(concatenated_audio * 32767, -32768, 32767).astype(np.int16)
                audio_bytes = audio_int16.tobytes()
                self.audio_conn.sendall(audio_bytes)
                
                if not self.first_audio_processed:
                    logger.info(f"【RTPStreamer】成功发送第一个音频数据包，字节长度: {len(audio_bytes)}")
                    self.first_audio_processed = True

            self.frame_count += 1

        except (IOError, BrokenPipeError, socket.error) as e:
            logger.warning(f"【RTPStreamer】写入帧时管道/套接字中断: {e}. 推流可能已停止.")
            self.stop()
        except Exception as e:
            logger_error(f"【RTPStreamer】写入帧失败: {e}")
            self.stop()

    def stop(self):
        """停止推流"""
        if not self.is_running:
            return
        self.is_running = False
        logger.info("【RTPStreamer】开始停止推流...")

        # 终止ffmpeg进程
        if self.process:
            try:
                logger.info("【RTPStreamer】正在终止FFmpeg进程...")
                self.process.terminate()
                # 等待进程终止
                self.process.wait(timeout=3)
                logger.info(f"【RTPStreamer】FFmpeg进程已终止。共推流 {self.frame_count} 帧.")
            except subprocess.TimeoutExpired:
                self.process.kill()
                logger.warning("【RTPStreamer】强制终止FFmpeg进程")
            except Exception as e:
                logger_error(f"【RTPStreamer】停止FFmpeg时发生错误: {e}")
            finally:
                self.process = None
        
        # 等待连接和日志线程结束
        if self.connection_thread and self.connection_thread.is_alive():
            self.connection_thread.join(timeout=1)
        if self.log_thread and self.log_thread.is_alive():
            self.log_thread.join(timeout=1)

        # 关闭套接字
        for conn in [self.video_conn, self.audio_conn, self.video_sock, self.audio_sock]:
            if conn:
                try: conn.close()
                except: pass
        self.video_conn, self.audio_conn, self.video_sock, self.audio_sock = None, None, None, None
        
        logger.info("【RTPStreamer】停止流程完成.")

    def start(self):
        """启动标准RTP推流进程 - 分离音视频流，使用偶数端口"""
        if not self._start_listeners():
            return False

        try:
            import re
            
            rtp_match = re.match(r'rtp://([^:]+):(\d+)', self.stream_url)
            if not rtp_match:
                logger.error(f"【RTPStreamer】无效的RTP URL格式: {self.stream_url}")
                return False
            
            target_ip = rtp_match.group(1)
            base_port = int(rtp_match.group(2))
            
            if base_port % 2 != 0:
                base_port -= 1
                logger.warning(f"【RTPStreamer】RTP端口应为偶数，已自动调整为: {base_port}")
            
            self.rtp_video_port = base_port
            self.rtp_audio_port = base_port + 2
            
            video_input_url = f'tcp://{self.ip}:{self.video_port}'
            audio_input_url = f'tcp://{self.ip}:{self.audio_port}'
            
            sdp_file = os.path.join(os.getcwd(), 'data', 'temp', f'stream_{self.rtp_video_port}.sdp').replace('\\', '/')
            os.makedirs(os.path.dirname(sdp_file), exist_ok=True)

            cmd = [
                'ffmpeg', '-hide_banner', '-loglevel', 'debug', '-y',
                
                # Video Input Options
                '-analyzeduration', '1',
                '-probesize', '1M',
                '-thread_queue_size', '512',
                '-f', 'rawvideo',
                '-pix_fmt', 'bgr24',
                '-s', f'{self.width}x{self.height}',
                '-r', str(self.fps),
                '-i', video_input_url,
                
                # Audio Input Options
                '-analyzeduration', '1',
                '-probesize', '32k',
                '-thread_queue_size', '512',
                '-f', 's16le',
                '-ar', str(self.audio_sample_rate),
                '-ac', '1',
                '-i', audio_input_url,
                
                # Output Options
                '-map', '0:v',
                '-map', '1:a',
                '-c:v', 'libx264',
                '-pix_fmt', 'yuv420p',
                '-preset', 'veryfast',
                '-tune', 'zerolatency',
                '-b:v', '2000k',
                '-c:a', 'aac',
                '-b:a', '128k',
                '-ar', str(self.audio_sample_rate),
                '-ac', '1',
                '-f', 'rtp_mpegts',
                self.stream_url # Use the base stream_url provided
            ]
            
            # SDP file is now generated by the rtp_mpegts muxer automatically
            # We reference it, but FFmpeg controls the content.
            sdp_file = os.path.join(os.getcwd(), 'data', 'temp', 'stream.sdp').replace('\\', '/')
            os.makedirs(os.path.dirname(sdp_file), exist_ok=True)

            final_cmd = cmd + ['-sdp_file', sdp_file]

            logger.info(f"【RTPStreamer】FFmpeg命令 (MPEG-TS over RTP): {' '.join(final_cmd)}")
            
            self.process = subprocess.Popen(
                final_cmd,
                stdin=subprocess.DEVNULL,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            self.is_running = True
            
            self.connection_thread = threading.Thread(target=self._accept_connections, daemon=True)
            self.connection_thread.start()

            self.log_thread = threading.Thread(target=self._log_ffmpeg_output, args=(self.process.stderr,), daemon=True)
            self.log_thread.start()

            logger.info(f"【RTPStreamer】FFmpeg进程已启动 (MPEG-TS over RTP模式)")
            logger.info(f"  进程PID: {self.process.pid}")
            logger.info(f"  输出流: {self.stream_url}")
            logger.info(f"  SDP文件: {sdp_file}")
            
            return True
        except FileNotFoundError:
            logger_error("【RTPStreamer】启动失败: 找不到FFmpeg可执行文件。请确保FFmpeg已安装并在PATH中。")
            self.stop()
            return False
        except Exception as e:
            logger_error(f"【RTPStreamer】启动FFmpeg失败: {e}")
            self.stop()
            return False

# 保留原来的RTPStreamer作为备选方案
class RTP_audio_Streamer:
    """
    RTP推流类，使用FFmpeg进行音频推流。
    通过创建TCP套接字将音频数据传递给FFmpeg，然后通过RTP推送到单个端口。
    """
    def __init__(self, stream_url, width=1920, height=1080, fps=25, audio_sample_rate=16000):
        self.stream_url = stream_url
        self.width = width
        self.height = height
        self.fps = fps
        self.audio_sample_rate = audio_sample_rate
        self.process = None
        self.is_running = False
        self.frame_count = 0
        self.first_audio_processed = False

        # TCP Listener setup for audio only
        self.ip = '127.0.0.1'
        self.audio_port = self._get_free_port()
        
        self.audio_sock = None
        self.audio_conn = None
        
        self.connection_thread = None
        self.log_thread = None
        logger.info(f"【RTP_audio_Streamer】初始化. URL: {self.stream_url}, Res: {width}x{height}, FPS: {fps}, ListenIP: {self.ip}")

    def _get_free_port(self):
        """获取一个空闲的TCP端口"""
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('', 0))
            return s.getsockname()[1]

    def _start_listeners(self):
        """启动TCP监听器以接受来自FFmpeg的连接 (仅音频)"""
        try:
            self.audio_sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.audio_sock.bind((self.ip, self.audio_port))
            self.audio_sock.listen(1)
            logger.info(f"【RTP_audio_Streamer】TCP监听器启动: Audio@{self.ip}:{self.audio_port}")
            return True
        except Exception as e:
            logger_error(f"【RTP_audio_Streamer】启动TCP监听器失败: {e}")
            return False

    def _accept_connections(self):
        """在一个单独的线程中接受FFmpeg的TCP连接 (仅音频)。"""
        logger.info("【RTP_audio_Streamer】开始等待FFmpeg连接...")
        try:
            self.audio_sock.settimeout(20.0)
            logger.info(f"【RTP_audio_Streamer】等待FFmpeg连接 (audio) on 127.0.0.1:{self.audio_port}...")
            self.audio_conn, addr = self.audio_sock.accept()
            logger.info(f"【RTP_audio_Streamer】FFmpeg已连接到音频套接字，来自: {addr}")
        except socket.timeout:
            logger.error("【RTP_audio_Streamer】等待FFmpeg连接超时。FFmpeg可能未能正确启动或连接。")
            self.stop()
        except Exception as e:
            if self.is_running:
                logger.error(f"【RTP_audio_Streamer】接受FFmpeg连接时发生错误: {e}")
                self.stop()

    def _log_ffmpeg_output(self, pipe):
        """在一个线程中运行，读取并记录ffmpeg的stderr输出"""
        logger.info("【RTP_audio_Streamer】开始监听FFmpeg输出...")
        try:
            for line in iter(pipe.readline, b''):
                line_str = line.decode('utf-8', errors='ignore').strip()
                if "ffmpeg version" in line_str or "configuration" in line_str:
                     logger.debug(f"[FFMPEG-CONFIG]: {line_str}")
                else:
                    logger.info(f"[FFMPEG]: {line_str}")
            pipe.close()
        except Exception as e:
            if self.is_running:
                logger.error(f"【RTP_audio_Streamer】读取ffmpeg stderr时出错: {e}")
        logger.info("【RTP_audio_Streamer】FFmpeg输出监听线程结束")

    def write_frame(self, image_np, batch_audio_data):
        """
        将音频数据写入FFmpeg。视频帧(image_np)将被忽略。
        """
        while self.is_running and not self.audio_conn:
            if self.process and self.process.poll() is not None:
                logger.error(f"【RTP_audio_Streamer】FFmpeg进程已退出，退出码: {self.process.poll()}。停止写入。")
                self.stop()
                return
            logger.info("【RTP_audio_Streamer】等待FFmpeg音频连接，暂停写入...")
            time.sleep(0.1)

        if not self.is_running:
            return

        try:
            # 发送音频数据
            if batch_audio_data and self.audio_conn:
                concatenated_audio = np.concatenate(batch_audio_data)
                audio_int16 = np.clip(concatenated_audio * 32767, -32768, 32767).astype(np.int16)
                audio_bytes = audio_int16.tobytes()
                self.audio_conn.sendall(audio_bytes)
                
                if not self.first_audio_processed:
                    logger.info(f"【RTP_audio_Streamer】成功发送第一个音频数据包，字节长度: {len(audio_bytes)}")
                    self.first_audio_processed = True

            self.frame_count += 1

        except (IOError, BrokenPipeError, socket.error) as e:
            logger.warning(f"【RTP_audio_Streamer】写入帧时管道/套接字中断: {e}. 推流可能已停止.")
            self.stop()
        except Exception as e:
            logger_error(f"【RTP_audio_Streamer】写入帧失败: {e}")
            self.stop()

    def stop(self):
        """停止推流"""
        if not self.is_running:
            return
        self.is_running = False
        logger.info("【RTP_audio_Streamer】开始停止推流...")

        if self.process:
            try:
                logger.info("【RTP_audio_Streamer】正在终止FFmpeg进程...")
                self.process.terminate()
                self.process.wait(timeout=3)
                logger.info(f"【RTP_audio_Streamer】FFmpeg进程已终止。共推流 {self.frame_count} 帧.")
            except subprocess.TimeoutExpired:
                self.process.kill()
                logger.warning("【RTP_audio_Streamer】强制终止FFmpeg进程")
            except Exception as e:
                logger_error(f"【RTP_audio_Streamer】停止FFmpeg时发生错误: {e}")
            finally:
                self.process = None
        
        if self.connection_thread and self.connection_thread.is_alive():
            self.connection_thread.join(timeout=1)
        if self.log_thread and self.log_thread.is_alive():
            self.log_thread.join(timeout=1)

        for conn in [self.audio_conn, self.audio_sock]:
            if conn:
                try: conn.close()
                except: pass
        self.audio_conn, self.audio_sock = None, None
        
        logger.info("【RTP_audio_Streamer】停止流程完成.")

    def start(self):
        """启动RTP/RTSP音频推流进程"""
        if not self._start_listeners():
            return False

        try:
            audio_input_url = f'tcp://{self.ip}:{self.audio_port}'
            
            # 区分RTP和RTSP协议
            is_rtsp = self.stream_url.startswith('rtsp://')
            
            if is_rtsp:
                # RTSP推流命令 (带虚拟视频轨道)
                cmd = [
                    'ffmpeg', '-re',
                    '-f', 'lavfi', '-i', f'color=c=black:s={self.width}x{self.height}:r={self.fps}', # 虚拟视频
                    '-f', 's16le', '-ar', str(self.audio_sample_rate), '-ac', '1', '-i', audio_input_url, # 真实音频
                    '-map', '0:v', '-c:v', 'libx264', '-preset', 'ultrafast', '-tune', 'zerolatency', '-b:v', '20k',
                    '-map', '1:a', '-c:a', 'aac', '-b:a', '128k',
                    '-f', 'rtsp', '-rtsp_transport', 'tcp', self.stream_url
                ]
                logger.info(f"【RTP_audio_Streamer】FFmpeg命令 (RTSP模式): {' '.join(cmd)}")
            else:
                # 原始的RTP推流逻辑 (仅音频)
                import re
                rtp_match = re.match(r'rtp://([^:]+):(\d+)', self.stream_url)
                if not rtp_match:
                    logger.error(f"【RTP_audio_Streamer】无效的RTP URL格式: {self.stream_url}")
                    return False
                target_ip = rtp_match.group(1)
                target_port = int(rtp_match.group(2))
                sdp_file = os.path.join(os.getcwd(), 'data', 'temp', f'stream_audio_{target_port}.sdp').replace('\\', '/')
                os.makedirs(os.path.dirname(sdp_file), exist_ok=True)
                cmd = [
                    'ffmpeg', '-hide_banner', '-loglevel', 'info', '-y',
                    '-thread_queue_size', '512', '-f', 's16le', '-ar', str(self.audio_sample_rate), '-ac', '1', '-i', audio_input_url,
                    '-map', '0:a', '-c:a', 'aac', '-b:a', '128k',
                    '-f', 'rtp', f'rtp://{target_ip}:{target_port}',
                    '-sdp_file', sdp_file
                ]
                logger.info(f"【RTP_audio_Streamer】FFmpeg命令 (RTP模式): {' '.join(cmd)}")

            self.process = subprocess.Popen(
                cmd,
                stdin=subprocess.DEVNULL,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE
            )
            
            self.is_running = True
            
            self.connection_thread = threading.Thread(target=self._accept_connections, daemon=True)
            self.connection_thread.start()

            self.log_thread = threading.Thread(target=self._log_ffmpeg_output, args=(self.process.stderr,), daemon=True)
            self.log_thread.start()

            logger.info(f"【RTP_audio_Streamer】FFmpeg进程已启动 (PID: {self.process.pid})")
            if not is_rtsp:
                 sdp_file_path = [item for item in cmd if item.endswith('.sdp')]
                 if sdp_file_path:
                    logger.info(f"  SDP文件: {sdp_file_path[0]}")

            return True
        except FileNotFoundError:
            logger_error("【RTP_audio_Streamer】启动失败: 找不到FFmpeg可执行文件。请确保FFmpeg已安装并在PATH中。")
            self.stop()
            return False
        except Exception as e:
            logger_error(f"【RTP_audio_Streamer】启动FFmpeg失败: {e}")
            self.stop()
            return False


def logger_error(E):
    def locate_exception():
        """
        locate error filename object line
        """
        import sys, os
        exc_type, exc_obj, exc_tb = sys.exc_info()
        if exc_tb is not None:
            fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
            return f"{exc_type.__name__}, in {fname}, line {exc_tb.tb_lineno}"
        else:
            return "No active exception"
    
    error_location = locate_exception()
    logger.error(f"【Error Location】: {error_location}")
    logger.error(f"【Error Details】: {E}")


def get_one_complete_img(image, mask_B_pre, box_info,image_id):
    # 3. 融合 - 内存优化版本
    y1, y2, x1, x2 = box_info # 裁剪人脸框
    face_box_width,face_box_height = y2 - y1,x2 - x1
    
    try:
        # 内存优化：直接修改原图像，避免创建副本
        img_h, img_w = image.shape[:2]
        
        img_paste_start_row = max(0, x1)
        img_paste_end_row = min(img_h, x2)
        img_paste_start_col = max(0, y1)
        img_paste_end_col = min(img_w, y2)

        src_start_row = -x1 if x1 < 0 else 0
        src_start_col = -y1 if y1 < 0 else 0

        paste_height = img_paste_end_row - img_paste_start_row
        paste_width = img_paste_end_col - img_paste_start_col
        
        src_end_row = src_start_row + paste_height
        src_end_col = src_start_col + paste_width

        if paste_height > 0 and paste_width > 0 and src_end_row <= mask_B_pre.shape[0] and src_end_col <= mask_B_pre.shape[1]:
            # 内存优化：只在需要时调整大小
            if mask_B_pre.shape[:2] != (face_box_height, face_box_width):
                mask_B_pre_resize = cv2.resize(mask_B_pre, (face_box_width, face_box_height))
            else:
                mask_B_pre_resize = mask_B_pre
                
            # 直接修改原图像
            image[img_paste_start_row:img_paste_end_row, img_paste_start_col:img_paste_end_col] = \
                mask_B_pre_resize[src_start_row:src_end_row, src_start_col:src_end_col]
        else:
            logger.warning(f"[Frame {image_id}: Invalid dimensions for pasting face mask. Skipping blend.")

    except cv2.error as e_resize:
        logger.error(f"[ Frame {image_id}: cv2.resize error for mask_B_pre: {e_resize}. Skipping blend.")

    return image


def get_complete_imgs(output_img_list, drivered_imgs_for_batch, params, no_face_indices_for_batch, work_id_for_logging):
    t1 = time.time()
    out_shape, output_resize, _, Y1_list, Y2_list, X1_list, X2_list = params
    complete_imgs = []

    if not drivered_imgs_for_batch:
        logger.error(f"[{work_id_for_logging}] get_complete_imgs: drivered_imgs_for_batch is empty.")
        return complete_imgs
    
    # The BBox lists in `params` are for the *entire* video, so we need to get the correct slice
    start_frame_index = -1 # We need this info. Let's assume it's passed somehow. For now, this is a bug.
                          # Correction: let's pass it in.
    
    # We will get the start_index from the call site.
    # Let's adjust the signature to be:
    # get_complete_imgs(output_img_list, drivered_imgs_for_batch, params, no_face_indices_for_batch, work_id_for_logging, start_frame_index)
    # Actually, the face_params are already sliced in the processor. We can assume the lists are correctly sized.
    
    for i, image in enumerate(drivered_imgs_for_batch):
        if i in no_face_indices_for_batch:
            complete_imgs.append(image)
            continue

        image = image.copy()
        if i >= len(output_img_list) or output_img_list[i] is None:
            logger.warning(f"[{work_id_for_logging}] Frame {i}: No valid raw face region found. Using driver frame.")
        else:
            mask_B_pre = output_img_list[i]
            if i < len(Y1_list):
                y1, y2, x1, x2 = (Y1_list[i], Y2_list[i], X1_list[i], X2_list[i])
                image = get_one_complete_img(image, mask_B_pre, (y1, y2, x1, x2), f"{work_id_for_logging}-frame{i}")
            else:
                 logger.error(f"[{work_id_for_logging}] Frame {i}: Index out of bounds for bounding box lists.")

        complete_imgs.append(image)

    t2 = time.time()
    logger.info(f"[{work_id_for_logging}] get_complete_imgs processed {len(complete_imgs)} frames, 耗时: {t2-t1:.2f}秒")
    return complete_imgs

