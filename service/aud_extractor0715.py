# -*-coding: utf-8 -*-
# @Time :2025-07-02
# <AUTHOR> l<PERSON><PERSON>jing
# @Email : <EMAIL>
# @File : audio_extractor.py

import os
import sys
from loguru import logger
import numpy as np
from queue import Empty
import librosa
import torch
from wenet.compute_ctc_att_bnf import get_weget, load_ppg_model, WenetStreamer
from wenet.tools._extract_feats import wav2mfcc_v2

def logger_error(E):
    def locate_exception():
        """
        locate error filename object line
        """
        exc_type, exc_obj, exc_tb = sys.exc_info()
        fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
        return "{},{},{}".format(exc_type, fname, exc_tb.tb_lineno)
    logger.error(locate_exception())
    logger.error("error {}".format(E))



class AudioExtractor(object):
    """
    负责从音频源（流或文件）提取Wenet特征和Mel特征。
    它是一个纯粹的音频处理器，不关心视频帧或批处理。
    支持两种特征提取模式：
    1. Wenet特征提取（原有功能）
    2. Mel特征提取（新增功能，基于SimpleMelDebugger的流式处理逻辑）
    """
    def __init__(self, feature_queue, audio_stream_queue, feature_type="wenet"):
        self.work_id_for_logging = "AudioExtractor"
        self.feature_queue = feature_queue
        self.audio_stream_queue = audio_stream_queue
        self.feature_type = feature_type  # "wenet" 或 "mel"

        # Wenet模型初始化（仅在需要时加载）
        if feature_type == "wenet":
            self.wenet_model = load_ppg_model("wenet/examples/aishell/aidata/conf/train_conformer_multi_cn.yaml", "wenet/examples/aishell/aidata/exp/conformer/wenetmodel.pt", "cuda")
            self.streamer = WenetStreamer(self.wenet_model)
        else:
            self.wenet_model = None
            self.streamer = None

        # Mel特征提取参数（与SimpleMelDebugger保持一致）
        self.mel_params = {
            'sample_rate': 16000,
            'preemphasis': 0.97,
            'n_fft': 1024,
            'hop_length': 160,
            'win_length': 800,
            'num_mels': 80,
            'n_mfcc': 13,
            'window': 'hann',
            'fmin': 0.0,
            'fmax': 8000.0,
            'ref_db': 20,
            'min_db': -80.0,
            'center': True
        }

        self.task_info = None
        self.is_stream = False
        self.audio_path = None
        self._is_running = False

    def init_task(self, task_info):
        self.task_info = task_info
        self.is_stream = task_info.get('is_stream', False)
        self.audio_path = task_info.get('audio_path')
        self.fps = task_info.get('fps', 25)
        self.feature_type = task_info.get('feature_type', self.feature_type)

        # 计算音频处理参数
        self.sample_rate = 16000
        self.chunk_samples = 1280  # 80ms = 1280样本（与SimpleMelDebugger保持一致）
        self.samples_per_video_frame = self.sample_rate // self.fps  # 每视频帧音频样本数

        logger.info(f"【{self.work_id_for_logging}】 AudioExtractor 初始化成功.")
        logger.info(f"  - 流式模式: {self.is_stream}")
        logger.info(f"  - 特征类型: {self.feature_type}")
        logger.info(f"  - 视频帧率: {self.fps} fps")
        logger.info(f"  - 音频参数: chunk_samples={self.chunk_samples}, samples_per_frame={self.samples_per_video_frame}")
        return self

    def extract_mel_features(self, audio_data):
        """
        提取mel特征，基于SimpleMelDebugger的实现

        Args:
            audio_data: numpy数组，音频数据

        Returns:
            dict: 包含mel特征和相关信息的字典
        """
        try:
            # 使用wav2mfcc_v2提取mel特征
            mel_features, x_stft = wav2mfcc_v2(
                audio_data,
                sr=self.mel_params["sample_rate"],
                n_mfcc=self.mel_params["n_mfcc"],
                n_fft=self.mel_params["n_fft"],
                hop_len=self.mel_params["hop_length"],
                win_len=self.mel_params["win_length"],
                window=self.mel_params["window"],
                num_mels=self.mel_params["num_mels"],
                center=self.mel_params["center"]
            )

            return {
                'log_mel_spec': mel_features,
                'stft': x_stft,
                'audio_length': len(audio_data),
                'mel_shape': mel_features.shape,
                'processing_method': 'wav2mfcc_v2'
            }

        except Exception as e:
            logger.error(f"【{self.work_id_for_logging}】 mel特征提取失败: {e}")
            logger_error(e)
            return None

    def _process_mel_with_wenet(self, mel_features):
        """
        使用wenet模型处理mel特征，生成wenet特征

        Args:
            mel_features: numpy数组，mel特征，形状为(frames, 80)

        Returns:
            numpy数组，wenet特征
        """
        try:
            # 确保wenet模型已加载
            if self.wenet_model is None:
                logger.info(f"【{self.work_id_for_logging}】 加载wenet模型...")
                self.wenet_model = load_ppg_model(
                    "wenet/examples/aishell/aidata/conf/train_conformer_multi_cn.yaml",
                    "wenet/examples/aishell/aidata/exp/conformer/wenetmodel.pt",
                    "cuda"
                )
                logger.info(f"【{self.work_id_for_logging}】 wenet模型加载完成")

            # 将mel特征转换为tensor
            device = "cuda"
            mel_tensor = torch.from_numpy(mel_features).float().to(device).unsqueeze(0)
            mel_length = torch.LongTensor([mel_features.shape[0]]).to(device)

            # 使用wenet模型处理mel特征
            with torch.no_grad():
                wenet_features = self.wenet_model(mel_tensor, mel_length)

            # 转换为numpy数组
            wenet_features_np = wenet_features.squeeze(0).cpu().numpy()

            logger.info(f"【{self.work_id_for_logging}】 wenet特征提取成功: mel形状={mel_features.shape}, wenet形状={wenet_features_np.shape}")
            return wenet_features_np

        except Exception as e:
            logger.error(f"【{self.work_id_for_logging}】 wenet特征提取失败: {e}")
            logger_error(e)
            # 如果wenet特征提取失败，返回一个与mel特征形状相同的全零数组
            # 这样可以保证流程不会中断
            return np.zeros((mel_features.shape[0], 256), dtype=np.float32)

    def run(self):
        self._is_running = True
        try:
            if self.is_stream:
                if self.feature_type == "wenet":
                    self.run_flow_audio_with_wenet()
                elif self.feature_type == "mel":
                    self.run_flow_audio_with_mel()
                else:
                    raise ValueError(f"不支持的特征类型: {self.feature_type}")
            else:
                if self.feature_type == "wenet":
                    self.run_local_audio()
                elif self.feature_type == "mel":
                    self.run_local_audio_mel()
                else:
                    raise ValueError(f"不支持的特征类型: {self.feature_type}")
        except Exception as e:
            logger_error(f"【{self.work_id_for_logging}】 处理失败: {e}")
        finally:
            # self.finish()
            pass

    def run_local_audio(self):
        logger.info(f"[{self.work_id_for_logging}] Processing local audio file with Wenet: {self.audio_path}")
        f_wenet_all = get_weget(self.audio_path, self.wenet_model)
        logger.info(f"[{self.work_id_for_logging}] Extracted Wenet features from local audio, shape: {f_wenet_all.shape}")

        chunk_size = 100
        for i in range(0, f_wenet_all.shape[0], chunk_size):
            chunk = f_wenet_all[i:i + chunk_size, :]
            if self._is_running:
                self.feature_queue.put(chunk)
            else:
                break

    def run_local_audio_mel(self):
        """
        本地音频文件mel特征提取
        """
        logger.info(f"[{self.work_id_for_logging}] Processing local audio file with Mel: {self.audio_path}")

        # 读取音频文件
        audio_data, sr = librosa.load(self.audio_path, sr=self.sample_rate)
        logger.info(f"[{self.work_id_for_logging}] Loaded audio: length={len(audio_data)}, sr={sr}, duration={len(audio_data)/sr:.2f}s")

        # 添加静音填充（与SimpleMelDebugger保持一致）
        zero = np.zeros(6400)  # 0.4秒的静音
        audio_data_padded = np.concatenate((zero, audio_data, zero))
        logger.info(f"[{self.work_id_for_logging}] Added silence padding: {len(audio_data)} -> {len(audio_data_padded)} samples")

        # 提取完整的mel特征
        mel_result = self.extract_mel_features(audio_data_padded)
        if mel_result is not None:
            mel_features = mel_result['log_mel_spec']
            logger.info(f"[{self.work_id_for_logging}] Extracted Mel features, shape: {mel_features.shape}")

            # 分块发送mel特征
            chunk_size = 100
            for i in range(0, mel_features.shape[0], chunk_size):
                chunk = mel_features[i:i + chunk_size, :]
                if self._is_running:
                    self.feature_queue.put(("data", chunk.tolist(), None))
                else:
                    break
        else:
            logger.error(f"[{self.work_id_for_logging}] Failed to extract Mel features from local audio")

    def run_flow_audio_with_mel(self):
        """
        流式音频mel特征提取，基于SimpleMelDebugger的流式处理逻辑
        充分考虑silence、data、start、end等状态
        """
        logger.info(f"【{self.work_id_for_logging}】 开始流式音频mel特征处理.")
        self._is_running = True

        # 音频缓冲区和处理队列
        audio_buffer = np.array([])  # 原始音频缓冲区
        frame_audio_queue = []  # 存储每个视频帧对应的音频数据

        logger.info(f"【{self.work_id_for_logging}】 Mel特征配置: fps={self.fps}, chunk_samples={self.chunk_samples}, samples_per_frame={self.samples_per_video_frame}")

        while self._is_running:
            try:
                status, audio_chunk = self.audio_stream_queue.get(block=True)

                if status == "data" and audio_chunk:
                    audio_buffer = self._process_audio_data_mel(audio_chunk, audio_buffer, frame_audio_queue)

                elif status == "start":
                    audio_buffer, frame_audio_queue = self._handle_start_mel()

                elif status == "silence":
                    audio_buffer, frame_audio_queue = self._handle_silence_mel(frame_audio_queue)

                elif status == "end":
                    self._handle_end_mel(frame_audio_queue)
                    break

            except Empty:
                logger.warning(f"【{self.work_id_for_logging}】 等待音频块超时. 处理剩余数据...")
                self._handle_timeout_mel(frame_audio_queue)
                break

            except Exception as e:
                logger_error(f"【{self.work_id_for_logging}】 流式mel特征处理循环错误: {e}")
                break

    def _process_audio_data_mel(self, audio_chunk, audio_buffer, frame_audio_queue):
        """
        处理音频数据状态的mel特征提取
        注意：这个方法会修改传入的audio_buffer和frame_audio_queue
        """
        # 将音频字节转换为numpy数组并添加到缓冲区
        audio_segment = np.frombuffer(audio_chunk, dtype=np.int16).astype(np.float32) / 32768.0

        # 由于numpy数组是不可变的，我们需要重新创建
        if len(audio_buffer) == 0:
            audio_buffer = audio_segment.flatten()
        else:
            audio_buffer = np.concatenate([audio_buffer, audio_segment.flatten()])

        # 按视频帧率存储音频数据（用于音视频同步）
        while len(audio_buffer) >= self.samples_per_video_frame:
            frame_audio = audio_buffer[:self.samples_per_video_frame]
            audio_buffer = audio_buffer[self.samples_per_video_frame:]
            frame_audio_queue.append(frame_audio)

        # 当有足够的音频数据时，提取mel特征
        self._extract_mel_from_queue(frame_audio_queue)

        return audio_buffer

    def _handle_start_mel(self):
        """
        处理开始状态
        """
        audio_buffer = np.array([])
        frame_audio_queue = []
        self.feature_queue.put(("start", None, None))
        logger.info(f"【{self.work_id_for_logging}】 流式mel特征处理开始，缓冲区已重置.")
        return audio_buffer, frame_audio_queue

    def _handle_silence_mel(self, frame_audio_queue):
        """
        处理静音状态 - 处理剩余数据并重置
        """
        logger.info(f"【{self.work_id_for_logging}】 音频段结束信号接收. 处理剩余数据...")

        # 处理剩余的音频帧
        self._process_remaining_audio_mel(frame_audio_queue, is_final=False)

        self.feature_queue.put(("silence", None, None))
        audio_buffer = np.array([])
        frame_audio_queue = []
        logger.info(f"【{self.work_id_for_logging}】 静音处理完成，缓冲区已重置.")
        return audio_buffer, frame_audio_queue

    def _handle_end_mel(self, frame_audio_queue):
        """
        处理结束状态 - 最终处理所有剩余数据
        """
        logger.info(f"【{self.work_id_for_logging}】 音频流结束信号接收. 最终处理...")

        # 处理所有剩余的音频数据
        self._process_remaining_audio_mel(frame_audio_queue, is_final=True)

        self.feature_queue.put(("end", None, None))
        self._is_running = False

    def _handle_timeout_mel(self, frame_audio_queue):
        """
        处理超时状态 - 处理剩余数据并结束
        """
        self._process_remaining_audio_mel(frame_audio_queue, is_final=True)
        self._is_running = False

    def _extract_mel_from_queue(self, frame_audio_queue):
        """
        从音频队列中提取mel特征
        """
        total_samples_in_queue = len(frame_audio_queue) * self.samples_per_video_frame

        while total_samples_in_queue >= self.chunk_samples:
            # 提取chunk_samples数量的音频数据
            mel_audio_data = []
            remaining_samples = self.chunk_samples

            while remaining_samples > 0 and len(frame_audio_queue) > 0:
                frame_data = frame_audio_queue.pop(0)
                samples_to_take = min(remaining_samples, len(frame_data))

                if samples_to_take == len(frame_data):
                    # 取整帧
                    mel_audio_data.extend(frame_data)
                    remaining_samples -= samples_to_take
                else:
                    # 取部分帧，剩余部分放回队列
                    mel_audio_data.extend(frame_data[:samples_to_take])
                    frame_audio_queue.insert(0, frame_data[samples_to_take:])
                    remaining_samples -= samples_to_take

            # 转换为numpy数组并提取mel特征
            mel_input = np.array(mel_audio_data, dtype=np.float32)
            mel_result = self.extract_mel_features(mel_input)

            if mel_result is not None:
                mel_features = mel_result['log_mel_spec']
                # 发送每一帧mel特征
                for i in range(mel_features.shape[0]):
                    single_feature = mel_features[i:i+1]
                    # 计算对应的音频数据
                    samples_per_feature = len(mel_audio_data) / mel_features.shape[0]
                    start_idx = int(i * samples_per_feature)
                    end_idx = int((i + 1) * samples_per_feature)
                    audio_data = mel_audio_data[start_idx:end_idx]

                    self.feature_queue.put(("data", single_feature.tolist(), audio_data))

            # 重新计算队列中的样本数
            total_samples_in_queue = len(frame_audio_queue) * self.samples_per_video_frame
            if len(frame_audio_queue) > 0 and len(frame_audio_queue[0]) != self.samples_per_video_frame:
                total_samples_in_queue = len(frame_audio_queue[0]) + (len(frame_audio_queue) - 1) * self.samples_per_video_frame

    def _process_remaining_audio_mel(self, frame_audio_queue, is_final=False):
        """
        处理剩余的音频数据
        """
        total_samples_in_queue = len(frame_audio_queue) * self.samples_per_video_frame
        if len(frame_audio_queue) > 0 and len(frame_audio_queue[0]) != self.samples_per_video_frame:
            total_samples_in_queue = len(frame_audio_queue[0]) + (len(frame_audio_queue) - 1) * self.samples_per_video_frame

        while total_samples_in_queue > 0:
            # 收集可用的音频数据
            mel_audio_data = []
            remaining_samples = min(self.chunk_samples, total_samples_in_queue)
            samples_collected = 0

            while samples_collected < remaining_samples and len(frame_audio_queue) > 0:
                frame_data = frame_audio_queue.pop(0)
                samples_to_take = min(remaining_samples - samples_collected, len(frame_data))

                mel_audio_data.extend(frame_data[:samples_to_take])
                samples_collected += samples_to_take

                if samples_to_take < len(frame_data):
                    frame_audio_queue.insert(0, frame_data[samples_to_take:])
                    break

            # 如果不足chunk_samples，用静音填充
            if len(mel_audio_data) < self.chunk_samples:
                padding_needed = self.chunk_samples - len(mel_audio_data)
                padding = np.zeros(padding_needed, dtype=np.float32)
                mel_audio_data.extend(padding)

            mel_input = np.array(mel_audio_data[:self.chunk_samples], dtype=np.float32)
            mel_result = self.extract_mel_features(mel_input)

            if mel_result is not None:
                mel_features = mel_result['log_mel_spec']
                for i in range(mel_features.shape[0]):
                    single_feature = mel_features[i:i+1]
                    samples_per_feature = len(mel_audio_data) / mel_features.shape[0]
                    start_idx = int(i * samples_per_feature)
                    end_idx = int((i + 1) * samples_per_feature)
                    audio_data = mel_audio_data[start_idx:end_idx]

                    self.feature_queue.put(("data", single_feature.tolist(), audio_data))

            # 重新计算剩余样本数
            total_samples_in_queue = len(frame_audio_queue) * self.samples_per_video_frame
            if len(frame_audio_queue) > 0 and len(frame_audio_queue[0]) != self.samples_per_video_frame:
                total_samples_in_queue = len(frame_audio_queue[0]) + (len(frame_audio_queue) - 1) * self.samples_per_video_frame

    def run_flow_audio_with_wenet(self):
        logger.info(f"【{self.work_id_for_logging}】 开始流式音频mel特征处理.")
        self._is_running = True

        # 音频处理参数
        audio_buffer = np.array([])  # 原始音频缓冲区

        # 重叠窗口处理参数
        self.overlap_buffer = np.array([])  # 重叠处理缓冲区
        self.valid_features_buffer = []  # 有效特征缓冲区
        self.valid_audio_buffer = []  # 对应的有效音频数据缓冲区
        self.is_first_data = True  # 是否是第一次接收data状态

        # 重叠窗口参数
        self.window_size = 3200  # 200ms窗口，足够生成20+帧mel特征
        self.hop_size = 640  # 40ms跳跃，与视频帧率同步
        self.context_frames = 10  # 上下文帧数，用于去除边界效应

        logger.info(f"【{self.work_id_for_logging}】 重叠窗口配置: window_size={self.window_size}, hop_size={self.hop_size}, context_frames={self.context_frames}")

        while self._is_running:
            try:
                status, audio_chunk = self.audio_stream_queue.get(block=True)

                # print("【AudioExtractor】接收音频数据", status, len(audio_chunk) if audio_chunk is not None else None)

                if status == "data" and audio_chunk:
                    # 将音频字节转换为numpy数组
                    audio_segment = np.frombuffer(audio_chunk, dtype=np.int16).astype(np.float32) / 32768.0

                    # 如果是第一次接收data状态，添加初始静音填充（模拟get_aud_feat1）
                    if self.is_first_data:
                        logger.info(f"【{self.work_id_for_logging}】 第一次接收data，添加初始静音填充")
                        zero_padding = np.zeros(6400)  # 0.4秒静音填充
                        audio_segment = np.concatenate([zero_padding, audio_segment])
                        self.is_first_data = False

                    # 添加到重叠缓冲区
                    self.overlap_buffer = np.concatenate([self.overlap_buffer, audio_segment.flatten()])

                    # 使用重叠窗口处理音频
                    self._process_overlap_windows()
                                    
                elif status == "start":
                    # 重置所有缓冲区和状态
                    audio_buffer = np.array([])
                    self.overlap_buffer = np.array([])
                    self.valid_features_buffer = []
                    self.valid_audio_buffer = []
                    self.is_first_data = True
                    self.feature_queue.put(("start", None, None))
                    logger.info(f"【{self.work_id_for_logging}】 流式mel特征处理开始，所有缓冲区已重置.")

                elif status == "silence":
                    logger.info(f"【{self.work_id_for_logging}】 音频段结束信号接收. 处理剩余数据...")

                    # 处理剩余的重叠窗口
                    self._process_remaining_overlap_windows(is_final=False)

                    # 发送累积的有效特征
                    self._send_accumulated_features()

                    self.feature_queue.put(("silence", None, None))
                    # 重置缓冲区但保持状态
                    self.overlap_buffer = np.array([])
                    self.valid_features_buffer = []
                    self.valid_audio_buffer = []
                    logger.info(f"【{self.work_id_for_logging}】 静音处理完成，缓冲区已重置.")

                elif status == "end":
                    logger.info(f"【{self.work_id_for_logging}】 音频流结束信号接收. 最终处理...")

                    # 添加末尾静音填充（模拟get_aud_feat1）
                    if len(self.overlap_buffer) > 0:
                        zero_padding = np.zeros(6400)  # 0.4秒静音填充
                        self.overlap_buffer = np.concatenate([self.overlap_buffer, zero_padding])

                    # 处理所有剩余的重叠窗口
                    self._process_remaining_overlap_windows(is_final=True)

                    # 发送累积的有效特征
                    self._send_accumulated_features()

                    self.feature_queue.put(("end", None, None))
                    self._is_running = False

            except Empty:
                logger.warning(f"【{self.work_id_for_logging}】 等待音频块超时. 处理剩余数据...")
                self._process_remaining_overlap_windows(is_final=True)
                self._send_accumulated_features()
                self._is_running = False

                break
            except Exception as e:
                logger_error(f"【{self.work_id_for_logging}】 流式mel特征处理循环错误: {e}")
                self._is_running = False

    def _process_overlap_windows(self):
        """
        使用重叠窗口处理音频，去除边界效应
        """
        while len(self.overlap_buffer) >= self.window_size:
            # 提取一个窗口的音频数据
            window_audio = self.overlap_buffer[:self.window_size]

            # 提取mel特征
            mel_result = self.extract_mel_features(window_audio)

            if mel_result is not None:
                mel_features = mel_result['log_mel_spec']

                # 去除边界效应：只保留中间的有效帧
                if mel_features.shape[0] > 2 * self.context_frames:
                    valid_mel = mel_features[self.context_frames:-self.context_frames]

                    # 计算对应的有效音频数据
                    samples_per_frame = len(window_audio) / mel_features.shape[0]
                    start_audio_idx = int(self.context_frames * samples_per_frame)
                    end_audio_idx = int((mel_features.shape[0] - self.context_frames) * samples_per_frame)
                    valid_audio = window_audio[start_audio_idx:end_audio_idx]

                    # 将有效特征和对应音频添加到缓冲区
                    self.valid_features_buffer.append(valid_mel)
                    self.valid_audio_buffer.append(valid_audio)

                    # logger.info(f"【{self.work_id_for_logging}】 处理窗口: 原始={mel_features.shape[0]}帧, 有效={valid_mel.shape[0]}帧, 音频={len(valid_audio)}样本")
                else:
                    logger.warning(f"【{self.work_id_for_logging}】 窗口mel特征帧数不足: {mel_features.shape[0]} < {2 * self.context_frames}")

            # 滑动窗口
            self.overlap_buffer = self.overlap_buffer[self.hop_size:]

            # 检查是否有足够的特征可以发送
            self._check_and_send_features()

    def _check_and_send_features(self):
        """
        检查是否有足够的特征可以发送（20帧窗口）
        """
        # 计算总的有效特征帧数
        total_frames = sum(mel.shape[0] for mel in self.valid_features_buffer)

        # 当有足够的特征时，按20帧窗口发送
        win_size = 20
        while total_frames >= win_size:
            # 连接所有有效特征和音频
            if len(self.valid_features_buffer) > 0:
                concatenated_features = np.concatenate(self.valid_features_buffer, axis=0)
                concatenated_audio = np.concatenate(self.valid_audio_buffer, axis=0)

                # 提取20帧窗口
                feature_window = concatenated_features[:win_size]

                # 计算对应的音频数据
                samples_per_frame = len(concatenated_audio) / concatenated_features.shape[0]
                audio_samples = int(win_size * samples_per_frame)
                audio_window = concatenated_audio[:audio_samples]

                # 通过wenet模型处理mel特征得到wenet特征
                wenet_features = self._process_mel_with_wenet(feature_window)
                print("wenet.shape",wenet_features.shape)

                # 发送wenet特征和对应音频
                self.feature_queue.put(("data", wenet_features.tolist(), audio_window.tolist()))
                logger.info(f"【{self.work_id_for_logging}】 发送wenet特征窗口: mel形状={feature_window.shape}, wenet形状={wenet_features.shape}, 音频={len(audio_window)}样本")

                # 移除已发送的帧，保留剩余特征和音频
                remaining_features = concatenated_features[win_size:]
                remaining_audio = concatenated_audio[audio_samples:]

                # 重新组织缓冲区
                self.valid_features_buffer = [remaining_features] if remaining_features.shape[0] > 0 else []
                self.valid_audio_buffer = [remaining_audio] if len(remaining_audio) > 0 else []

                # 重新计算总帧数
                total_frames = sum(mel.shape[0] for mel in self.valid_features_buffer)
            else:
                break

    def _process_remaining_overlap_windows(self, is_final=False):
        """
        处理剩余的重叠窗口
        """
        # 如果是最终处理且缓冲区不足一个完整窗口，用静音填充
        if is_final and len(self.overlap_buffer) > 0:
            if len(self.overlap_buffer) < self.window_size:
                padding_needed = self.window_size - len(self.overlap_buffer)
                padding = np.zeros(padding_needed, dtype=np.float32)
                self.overlap_buffer = np.concatenate([self.overlap_buffer, padding])
                logger.info(f"【{self.work_id_for_logging}】 最终处理：添加静音填充 {padding_needed} 样本")

        # 处理剩余的窗口
        self._process_overlap_windows()

    def _send_accumulated_features(self):
        """
        发送累积的有效特征
        """
        if len(self.valid_features_buffer) > 0:
            # 连接所有剩余特征和音频
            concatenated_features = np.concatenate(self.valid_features_buffer, axis=0)
            concatenated_audio = np.concatenate(self.valid_audio_buffer, axis=0)

            # 按20帧窗口发送剩余特征
            win_size = 20
            samples_per_frame = len(concatenated_audio) / concatenated_features.shape[0]

            for i in range(0, concatenated_features.shape[0], win_size):
                if i + win_size <= concatenated_features.shape[0]:
                    feature_window = concatenated_features[i:i + win_size]

                    # 计算对应的音频数据
                    audio_start = int(i * samples_per_frame)
                    audio_end = int((i + win_size) * samples_per_frame)
                    audio_window = concatenated_audio[audio_start:audio_end]

                    # 通过wenet模型处理mel特征得到wenet特征
                    wenet_features = self._process_mel_with_wenet(feature_window)

                    # 发送wenet特征和对应音频
                    self.feature_queue.put(("data", wenet_features.tolist(), audio_window.tolist()))
                    logger.info(f"【{self.work_id_for_logging}】 发送剩余wenet特征窗口: mel形状={feature_window.shape}, wenet形状={wenet_features.shape}, 音频={len(audio_window)}样本")
                else:
                    # 最后不足20帧的部分，如果超过10帧也发送
                    remaining_frames = concatenated_features.shape[0] - i
                    if remaining_frames >= 10:
                        feature_window = concatenated_features[i:]

                        # 计算对应的音频数据
                        audio_start = int(i * samples_per_frame)
                        audio_window = concatenated_audio[audio_start:]

                        # 通过wenet模型处理mel特征得到wenet特征
                        wenet_features = self._process_mel_with_wenet(feature_window)

                        # 发送wenet特征和对应音频
                        self.feature_queue.put(("data", wenet_features.tolist(), audio_window.tolist()))
                        logger.info(f"【{self.work_id_for_logging}】 发送最后的wenet特征窗口: mel形状={feature_window.shape}, wenet形状={wenet_features.shape}, 音频={len(audio_window)}样本")

            # 清空缓冲区
            self.valid_features_buffer = []
            self.valid_audio_buffer = []

    def finish(self, error=False):  # todo: 需要优化
        logger.info(f"【{self.work_id_for_logging}】 AudioExtractor 完成. 将结束信号放入特征队列.")
        self.feature_queue.put(("silence", None, None))
        self._is_running = False



def feature_extraction_wenet(audio_file, fps, wenet_model, mfccnorm=True, section=560000):
    """
    音频特征提取 (用于非流式)
    """
    rate = 16000
    win_size = 20
    if type(audio_file) == str:
        sig, rate = librosa.load(audio_file, sr=rate, duration=None)
    else:
        sig = audio_file
    time_duration = len(sig) / rate # 音频时长:秒
    cnts = range(int(time_duration * fps)) # 音频帧数
    indexs = []
    f_wenet_all = get_weget(audio_file, wenet_model, section)
    print(f"f_wenet_all.shape:{f_wenet_all.shape}")
    for cnt in cnts:
        c_count = int(cnt / cnts[-1] * (f_wenet_all.shape[0] - 20)) + win_size // 2
        indexs.append(f_wenet_all[(c_count - win_size // 2):c_count + win_size // 2, ...])
    return indexs


def get_aud_feat1(wav_fragment, fps, wenet_model):
    return feature_extraction_wenet(wav_fragment, fps, wenet_model)
