# uncompyle6 version 3.9.2
# Python bytecode version base 3.8.0 (3413)
# Decompiled from: Python 3.10.9 | packaged by Anaconda, Inc. | (main, Mar  1 2023, 18:18:15) [MSC v.1916 64 bit (AMD64)]
# Embedded file name: /code/service/trans_dh_service.py
# Compiled at: 2024-06-11 11:24:28
# Size of source mod 2**32: 68115 bytes
"""
@project : face2face_train
<AUTHOR> huyi
@file   : trans_dh_service.py
@ide    : PyCharm
@time   : 2023-12-06 14:47:11
"""
import gc, multiprocessing, os, subprocess, threading, time, traceback,sys
from enum import Enum
from multiprocessing import Process, set_start_method
from queue import Empty, Full
import cv2, librosa, numpy as np, torch
import json
from cv2box import CVImage
from cv2box.cv_gears import Linker, Queue, CVVideoWriterThread
from face_detect_utils.face_detect import FaceDetect, pfpld
from face_detect_utils.head_pose import Headpose
from face_lib.face_detect_and_align import FaceDetect5Landmarks
from face_lib.face_restore import GFPGAN
from h_utils.custom import CustomError
from h_utils.request_utils import download_file
from h_utils.sweep_bot import sweep
from landmark2face_wy.digitalhuman_interface import DigitalHumanModel
from preprocess_audio_and_3dmm import op
from wenet.compute_ctc_att_bnf import get_weget
from wenet.compute_ctc_att_bnf import load_ppg_model
from y_utils.config import GlobalConfig
from y_utils.logger import logger
from .server import register_host, repost_host
from lt_utils.wo_common import videocap_local_imgs
import glob



def feature_extraction_wenet(audio_file, fps, wenet_model, mfccnorm=True, section=560000):
    rate = 16000
    win_size = 20
    if type(audio_file) == str:
        sig, rate = librosa.load(audio_file, sr=rate, duration=None)
    else:
        sig = audio_file
    time_duration = len(sig) / rate
    cnts = range(int(time_duration * fps))
    indexs = []
    f_wenet_all = get_weget(audio_file, wenet_model, section)
    for cnt in cnts:
        c_count = int(cnt / cnts[-1] * (f_wenet_all.shape[0] - 20)) + win_size // 2
        indexs.append(f_wenet_all[(c_count - win_size // 2):c_count + win_size // 2, ...])
    return indexs


def get_aud_feat1(wav_fragment, fps, wenet_model):
    return feature_extraction_wenet(wav_fragment, fps, wenet_model)


def warp_imgs(imgs_data):
    caped_img2 = {idx: {'imgs_data':it,  'idx':idx} for it, idx in zip(imgs_data, range(len(imgs_data)))}
    return caped_img2


def get_complete_imgs(output_img_list, start_index, params):
    t1=time.time()
    out_shape, output_resize, drivered_imgs_data, Y1_list, Y2_list, X1_list, X2_list = params
    complete_imgs = []
    for i, mask_B_pre in enumerate(output_img_list):
        img_idx = start_index + i
        image = drivered_imgs_data[img_idx]
        y1, y2, x1, x2 = (Y1_list[img_idx], Y2_list[img_idx], X1_list[img_idx], X2_list[img_idx])
        mask_B_pre_resize = cv2.resize(mask_B_pre, (y2 - y1, x2 - x1))
        if y1 < 0:
            mask_B_pre_resize = mask_B_pre_resize[:, -y1:]
            y1 = 0
        if y2 > image.shape[1]:
            mask_B_pre_resize = mask_B_pre_resize[:, :-(y2 - image.shape[1])]
            y2 = image.shape[1]
        if x1 < 0:
            mask_B_pre_resize = mask_B_pre_resize[-x1:, :]
            x1 = 0
        if x2 > image.shape[0]:
            mask_B_pre_resize = mask_B_pre_resize[:-(x2 - image.shape[0]), :]
            x2 = image.shape[0]
        image[x1:x2, y1:y2] = mask_B_pre_resize
        image = cv2.resize(image, (out_shape[1] // output_resize, out_shape[0] // output_resize))
        complete_imgs.append(image)
    t2=time.time()
    logger.info(f"get_complete_imgs 耗时: {t2-t1:.2f}秒")
    return complete_imgs


def get_blend_imgs(batch_size, audio_data, face_data_dict, blend_dynamic, params, digital_human_model, frameId):
    result_img_list = []
    for idx in range(len(audio_data) // batch_size + 1):
        torch.cuda.empty_cache()
        print(("\r{}/{}".format((idx + 1) * batch_size, len(audio_data))), end="")
        if idx < len(audio_data) // batch_size:
            start_index = idx * batch_size
            output_img_list = digital_human_model.inference_notraining(audio_data, face_data_dict, batch_size, start_index, blend_dynamic, params, frameId)  # 3.推理函数
            complete_imgs = get_complete_imgs(output_img_list, start_index, params)
            result_img_list += complete_imgs

        this_batch = len(audio_data) % batch_size
        if this_batch > 0:
            start_index = idx * batch_size
            output_img_list = digital_human_model.inference_notraining(audio_data, face_data_dict, this_batch, start_index, blend_dynamic, params, frameId)
            complete_imgs = get_complete_imgs(output_img_list, start_index, params)
            result_img_list += complete_imgs
    return result_img_list


def drivered_video(code, drivered_queue, drivered_path, audio_wenet_feature, batch_size, wh=(0,)):
    """
    处理视频帧并发送到队列
    Args:
        code: 任务代码
        drivered_queue: 驱动队列
        drivered_path: 视频路径
        audio_wenet_feature: 音频特征
        batch_size: 批处理大小
        wh: 宽高信息，默认为(0,)表示不调整大小
    """
    # 以下代码是自己补全的部分
    logger.info(f'-> 开始处理视频: {drivered_path}')
    start_time = time.time()

    try:
        # 打开视频文件
        cap = cv2.VideoCapture(drivered_path)
        if not cap.isOpened():
            logger.error(f'[-> 无法打开视频文件: {drivered_path}')
            drivered_queue.put(None)
            return

        # 获取视频信息
        fps = int(cap.get(cv2.CAP_PROP_FPS))
        total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))

        logger.info(f'[-> 视频信息: 帧率={fps}, 总帧数={total_frames}, 分辨率={width}x{height}')

        # 读取所有帧
        imgs_data = []
        while True:
            ret, frame = cap.read()
            if not ret:
                break

            # 调整帧大小（如果需要）
            if len(wh) >= 2 and wh[0] > 0 and wh[1] > 0:
                frame = cv2.resize(frame, (wh[0], wh[1]))

            imgs_data.append(frame)

        # 关闭视频文件
        cap.release()

        # 检查是否成功读取帧
        if not imgs_data:
            logger.error(f'[-> 未能从视频中读取任何帧')
            drivered_queue.put(None)
            return

        logger.info(f'[-> 成功读取 {len(imgs_data)} 帧')

        # 将帧数据放入队列
        drivered_queue.put((imgs_data, audio_wenet_feature, {}))

        # 发送结束信号
        drivered_queue.put(None)

        logger.info(f'[-> 视频处理完成，耗时: {time.time() - start_time:.2f}秒')

    except Exception as e:
        logger.error(f'[-> 视频处理出错: {str(e)}')
        logger.error(traceback.format_exc())
        drivered_queue.put(None)
    finally:
        # 确保视频文件被关闭
        if 'cap' in locals() and cap.isOpened():
            cap.release()

# todo 检查
def drivered_video_pn(code, drivered_queue, drivered_path, audio_wenet_feature, batch_size, wh=0,use_npy=False,is_train="0",speaker_id=None):
    try:
        t1 = time.time()
        logger.info("[{}]任务视频驱动队列启动 batch_size:{}".format(code, batch_size))
        drivered_fnames_list = [] # Changed from drivered_list
        wenet_feature_list = []
        count_f = 0  # 视频一共有多少帧
        current_idx = 0
        _max_flag = False 
        _flag = True  # 是否正循环
        batch_img_idx_list =[]  # 视频帧索引,有可能是倒叙的，
        batch_aud_idx_list =[]  # 音频帧索引，只会是正序
        total_video_frame=-1
        suffix = "jpg" if not use_npy else "npy"
        models_dir = os.path.join('workspace', 'models', speaker_id)
        if is_train == "2":
            if os.path.exists(os.path.join(models_dir, '{}'.format('chaofen_frames_npy' if use_npy else 'chaofen_frames'))):
                img_dir = os.path.join(models_dir, '{}'.format('chaofen_frames_npy' if use_npy else 'chaofen_frames'))
            else:
                img_dir = os.path.join(models_dir, '{}'.format('drivered_frames'))
        else:
            img_dir = os.path.join(models_dir, '{}'.format('drivered_frames_npy' if use_npy else 'drivered_frames'))

        logger.info("【drivered_video 开始读取视频,将要读取{}，使用{}读取】".format("超分图片" if is_train == "2" else "驱动图片", "numpy" if use_npy else "opencv"))
        os.makedirs(img_dir, exist_ok=True)
        
        # 首先发送固定参数


        drivered_queue.put([code,wh,speaker_id,is_train,use_npy,total_video_frame], block=True, timeout=60)
        logger.info("drivered_video >>>>>>>>>>>>>>>>>>>> 发送固定参数")
        
        while current_idx < len(audio_wenet_feature):
            if _flag:
                if count_f == 0:  # 第一次读完视频
                    logger.info("drivered_video >>>>>>>>>>>>>>>>>>>> 开始第一次循环")
                    if is_train == "2" and len(os.listdir(img_dir)) > 0:
                        cap = videocap_local_imgs(img_dir,use_npy=use_npy)
                        total_video_frame = cap.get_frame_count()
                    else:
                        cap = cv2.VideoCapture(drivered_path)
                        try:
                            total_video_frame = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
                        except Exception as e:
                            logger.error(f"[ 获取视频总帧数失败: {str(e)}")
                            logger.error(traceback.format_exc())
                            total_video_frame = -1

                    while cap.isOpened():
                        ret, frame = cap.read()
                        if ret:
                            # drivered_list.append(frame) # Changed
                            
                            wenet_feature_list.append(audio_wenet_feature[current_idx])
                            current_idx += 1
                            batch_img_idx_list.append(current_idx)  # 从1开始
                            batch_aud_idx_list.append(current_idx)
                            if _max_flag is False:
                                count_f += 1
                                if is_train != "2": # 推理时，不保存图片
                                    f_file_name = os.path.join(img_dir, f"frame_{count_f:06d}.{suffix}")
                                    np.save(f_file_name, frame) if use_npy else cv2.imwrite(f_file_name, frame)
                                    drivered_fnames_list.append(f_file_name) # Added: store filename
                                else: # is_train == "2", 推理时，也需要文件名（如果从本地读取）
                                    # This case implies frames are already saved, construct filename
                                    f_file_name = os.path.join(img_dir, f"frame_{count_f:06d}.{suffix}")
                                    drivered_fnames_list.append(f_file_name) # Added: store filename
                            else: # _max_flag is True, means we are looping existing frames
                                f_file_name = os.path.join(img_dir, f"frame_{batch_img_idx_list[-1]:06d}.{suffix}") # Use current frame's index for filename
                                drivered_fnames_list.append(f_file_name)


                            if current_idx % batch_size == 0:
                                # 只发送变化的数据
                                wenet_feature_list = [w.tolist() for w in wenet_feature_list]
                                drivered_queue.put([drivered_fnames_list, wenet_feature_list,batch_img_idx_list,batch_aud_idx_list], block=True, timeout=60)
                                logger.info("drivered_video >>>>>>>>>>>>>>>>>>>> 发送数据大小:[{}]".format(len(drivered_fnames_list)))
                                drivered_fnames_list = []
                                wenet_feature_list = []
                                batch_img_idx_list =[]
                                batch_aud_idx_list =[]
                            if current_idx == len(audio_wenet_feature):
                                if len(drivered_fnames_list) > 0:
                                    if len(wenet_feature_list) > 0:
                                        # 只发送变化的数据
                                        wenet_feature_list = [w.tolist() for w in wenet_feature_list]
                                        drivered_queue.put([drivered_fnames_list, wenet_feature_list,batch_img_idx_list,batch_aud_idx_list], block=True, timeout=60)
                                        drivered_fnames_list = []
                                        wenet_feature_list = []
                                        batch_img_idx_list =[]
                                        batch_aud_idx_list =[]
                                logger.info("append imgs over")
                                cap.release()
                                break
                        else:
                            cap.release()
                elif current_idx == len(audio_wenet_feature):
                    cap.release()
                else:
                    _flag = False
                    if _max_flag is False:
                        _max_flag = True
                    else:
                        logger.info("drivered_video >>>>>>>>>>>>>>>>>>>> 开始正循环")
                    print(f"count_f: {count_f}")
                    t2 = time.time()
                    for frame_re_index in range(1, count_f + 1):
                        f_file_name = os.path.join(img_dir, f"frame_{frame_re_index:06d}.{suffix}")
                        drivered_fnames_list.append(f_file_name) # Changed
                        wenet_feature_list.append(audio_wenet_feature[current_idx])
                        current_idx += 1
                        batch_img_idx_list.append(frame_re_index)
                        batch_aud_idx_list.append(current_idx)
                        if current_idx % batch_size == 0:
                            while drivered_queue.full():
                                logger.info("drivered_video  队列满，等待队列空闲....")
                                time.sleep(0.05)

                            tt3 = time.time()
                            # 只发送变化的数据
                            
                            wenet_feature_list = [w.tolist() for w in wenet_feature_list]
                            drivered_queue.put([drivered_fnames_list, wenet_feature_list,batch_img_idx_list,batch_aud_idx_list], block=True, timeout=60)
                            tt4 = time.time()
                            queue_size = drivered_queue.qsize()

                            logger.info(f"drivered_video  队列大小:[{queue_size}],放入队列耗时：{tt4-tt3:.3f}s")
                            logger.info(f"drivered_video >>>>>>>>>>>>>>>>>>>> 发送数据大小:[{len(drivered_fnames_list)}],耗时：{time.time()-t2:.3f}s")
                            t2 = time.time()
                            drivered_fnames_list = []
                            wenet_feature_list = []
                            batch_img_idx_list =[]
                            batch_aud_idx_list =[]
                        if current_idx == len(audio_wenet_feature):
                            if len(drivered_fnames_list) > 0:
                                if len(wenet_feature_list) > 0:
                                    # 只发送变化的数据
                                    wenet_feature_list = [w.tolist() for w in wenet_feature_list]
                                    drivered_queue.put([drivered_fnames_list, wenet_feature_list,batch_img_idx_list,batch_aud_idx_list], block=True, timeout=60)
                                    drivered_fnames_list = []
                                    wenet_feature_list = []
                                    batch_img_idx_list =[]
                                    batch_aud_idx_list =[]
                            logger.info("append imgs over")
                            cap.release()
                            break
                    else:
                        _flag = False
            if current_idx == len(audio_wenet_feature):
                break
            logger.info("drivered_video >>>>>>>>>>>>>>>>>>>> 开始倒循环")
            for frame_re_index in range(count_f, 0, -1):
                print(f"frame_re_index: {frame_re_index}")
                f_file_name = os.path.join(img_dir, f"frame_{frame_re_index:06d}.{suffix}")
                drivered_fnames_list.append(f_file_name) # Changed
                wenet_feature_list.append(audio_wenet_feature[current_idx])
                current_idx += 1
                batch_img_idx_list.append(frame_re_index)
                batch_aud_idx_list.append(current_idx)
                
                if current_idx % batch_size == 0:
                    # 只发送变化的数据
                    wenet_feature_list = [w.tolist() for w in wenet_feature_list]
                    drivered_queue.put([drivered_fnames_list, wenet_feature_list,batch_img_idx_list,batch_aud_idx_list], block=True, timeout=60)
                    logger.info("drivered_video >>>>>>>>>>>>>>>>>>>> 发送数据大小:[{}]".format(len(drivered_fnames_list)))
                    drivered_fnames_list = []
                    wenet_feature_list = []
                    batch_img_idx_list =[]
                    batch_aud_idx_list =[]
                if current_idx == len(audio_wenet_feature):
                    if len(drivered_fnames_list) > 0:
                        if len(wenet_feature_list) > 0:
                            # 只发送变化的数据
                            wenet_feature_list = [w.tolist() for w in wenet_feature_list]
                            drivered_queue.put([drivered_fnames_list, wenet_feature_list,batch_img_idx_list,batch_aud_idx_list], block=True, timeout=60)
                            drivered_fnames_list = []
                            wenet_feature_list = []
                            batch_img_idx_list =[]
                            batch_aud_idx_list =[]
                    logger.info("append imgs over")
                    cap.release()
                    break
                _flag = True

        logger.info(f"drivered_video >>>>>>>>>>>>>>>>>>>> 发送数据结束,耗时：{time.time()-t1}s")
        drivered_queue.put([True, "success", code])
        logger.info("[{}]任务预处理进程结束".format(code))

    except Full:
        logger.error("[{}]任务视频驱动队列满，严重阻塞，下游队列异常".format(code))
    except Exception as e:
        try:
            traceback.format_exc()
            logger.error(locate_exception())
            logger.error("[{}]任务视频驱动队列异常，异常信息:[{}]".format(code, e.__str__()))
            drivered_queue.put([False,
             "[{}]任务视频驱动队列异常，异常信息:[{}]".format(code, e.__str__()), code])
        finally:
            e = None
            del e


def get_face_mask(mask_shape=(512, 512)):
    mask = np.zeros((512, 512)).astype(np.float32)
    cv2.ellipse(mask, (256, 256), (220, 160), 90, 0, 360, (255, 255, 255), -1)
    thres = 20
    mask[:thres, :] = 0
    mask[-thres:, :] = 0
    mask[:, :thres] = 0
    mask[:, -thres:] = 0
    mask = cv2.stackBlur(mask, (201, 201))
    mask = mask / 255
    mask = cv2.resize(mask, mask_shape)
    return mask[..., np.newaxis]


def get_single_face(bboxes, kpss, image, crop_size, mode='mtcnn_512', apply_roi=True):
    from face_lib.face_detect_and_align.face_align_utils import apply_roi_func, norm_crop
    assert mode in ('default', 'mtcnn_512', 'mtcnn_256', 'arcface_512', 'arcface', 'default_95')
    if bboxes.shape[0] == 0:
        return (None, None)
    det_score = bboxes[..., 4]
    best_index = np.argmax(det_score)
    new_kpss = None
    if kpss is not None:
        new_kpss = kpss[best_index]
    if apply_roi:
        roi, roi_box, roi_kpss = apply_roi_func(image, bboxes[best_index], new_kpss)
        align_img, mat_rev = norm_crop(roi, roi_kpss, crop_size, mode=mode)
        return (align_img, mat_rev, roi_box)
    align_img, M = norm_crop(image, new_kpss, crop_size, mode=mode)
    return (align_img, M)


face_mask = get_face_mask()
need_chaofen_flag = False
get_firstface_frame = False
def locate_exception():
    """
    locate error filename object line
    """
    exc_type, exc_obj, exc_tb = sys.exc_info()
    fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
    return "{},{},{}".format(exc_type, fname, exc_tb.tb_lineno)

#todo 检查
def chaofen_src(frame_list, gfpgan, fd, frame_id, face_blur_detect, code):
    global get_firstface_frame
    global need_chaofen_flag
    s_chao = time.time()
    if frame_id == 4: # 这里做了修改
        chaofen_flag = get_firstface_frame or False
        firstface_frame = False
        for frame in frame_list:
            if frame.shape[0] >= 3840 or frame.shape[1] >= 3840:
                chaofen_flag = False
                firstface_frame = True
                logger.info("[%s] -> video frame shape is 4k, skip chaofen")
                break

            else:
                bboxes_scrfd, kpss_scrfd = fd.get_bboxes(frame)
                if len(bboxes_scrfd) == 0:
                    continue
             
                face_image_, mat_rev_, roi_box_ = get_single_face(bboxes_scrfd, kpss_scrfd, frame, crop_size=512, mode="mtcnn_512",apply_roi=True)
                face_attr_res = face_blur_detect.forward(face_image_)
                blur_threshold = face_attr_res[0][-2]
                logger.info("[%s] -> frame_id:[%s] 模糊置信度:[%s]", code, frame_id, blur_threshold)
                if blur_threshold > GlobalConfig.instance().blur_threshold:
                    logger.info("[%s] -> need chaofen .", code)
                    chaofen_flag = True
                else:
                    chaofen_flag = False
                firstface_frame = True

                
            need_chaofen_flag = chaofen_flag
            get_firstface_frame = firstface_frame
            break

    if not need_chaofen_flag:
        return frame_list,need_chaofen_flag
    new_frame_list = []
    for i in range(len(frame_list)):
        frame = frame_list[i]
        bboxes_scrfd, kpss_scrfd = fd.get_bboxes(frame)
        if len(bboxes_scrfd) == 0:
            new_frame_list.append(frame)
            continue

        face_image_, mat_rev_, roi_box_ = get_single_face(bboxes_scrfd, kpss_scrfd, frame, crop_size=512, mode="mtcnn_512",apply_roi=True)
        face_restore_out_ = gfpgan.forward(face_image_)
        restore_roi = CVImage.recover_from_reverse_matrix(face_restore_out_, (frame[roi_box_[1]:roi_box_[3],roi_box_[0]:roi_box_[2]]),mat_rev_,img_fg_mask=face_mask)
        frame[roi_box_[1]:roi_box_[3], roi_box_[0]:roi_box_[2]] = restore_roi
        new_frame_list.append(frame)

    torch.cuda.empty_cache()
    logger.info("[] -> chaofen  cost:{:.3f}s" .format(time.time() - s_chao))
    return new_frame_list,need_chaofen_flag



def _get_chaofen_src_imgs(code,is_train,img_list, digital_human_model, scrfd_detector, frameId,batch_img_idx_list , batch_aud_idx_list, speaker_id,total_video_frame,use_npy=False):
    """
    本批次超分处理
    todo: 这个函数需要优化，将代码合并一下，有点重复
    """
    # chaofen_frames_dir = os.path.join('workspace', 'models', speaker_id, 'chaofen_frames')
    chaofen_frames_dir = os.path.join('workspace', 'models', speaker_id, '{}'.format('chaofen_frames_npy' if use_npy else 'chaofen_frames'))
    drivered_frames_dir = os.path.join('workspace', 'models', speaker_id, 'drivered_frames')
    suffix = "npy" if use_npy else "jpg"

    if is_train == '1':
        # 创建帧数据保存目录
        os.makedirs(chaofen_frames_dir, exist_ok=True)
        video_is_unfinish = False
        if min(batch_aud_idx_list) <= total_video_frame:
            video_is_unfinish = True
     
        if video_is_unfinish: # 只有第一次循环的时候需要保存超分的图片
            img_list,need_chaofen_flag = chaofen_src(img_list, digital_human_model.gfpgan, scrfd_detector, frameId, digital_human_model.face_attr, code)   # 1.超分处理  
            # logger.info(f"[use_npy: {use_npy}")

            # 保存所有帧的超分处理后图片
            for img,frame_number in zip(img_list,batch_img_idx_list):
                frame_path = os.path.join(chaofen_frames_dir, f'frame_{frame_number:06d}.{suffix}')
                cv2.imwrite(frame_path, img) if not use_npy else np.save(frame_path, img)
            # logger.info(f"[保存第{frameId}帧超分图像文件, frame_number: {frameId}")
        else: # 视频已经完成
            # 尝试从已有的chaofen_frames目录加载图像,注意，不是所有的帧都有人像
            new_img_list = []
            for src_img,frame_number in zip(img_list,batch_img_idx_list):
                frame_path = os.path.join(chaofen_frames_dir, f'frame_{frame_number:06d}{suffix}')
                if os.path.exists(frame_path):
                    img = cv2.imread(frame_path) if not use_npy else np.load(frame_path)
                    new_img_list.append(img)
                else:
                    new_img_list.append(src_img)  # 有些图片没有人脸，直接添加原图,或者没有超分（分辨率比较大）
                
    elif os.path.exists(drivered_frames_dir) or os.path.exists(chaofen_frames_dir):
        # logger.info(f"推理模式")
        return img_list  # 推理模式直接在drivered_video_pn中读取超分图片
    else:
        img_list,need_chaofen_flag = chaofen_src(img_list, digital_human_model.gfpgan, scrfd_detector, frameId, digital_human_model.face_attr, code)
    return img_list

def _get_drivered_face_dict(code,is_train,caped_drivered_img2,digital_human_model,scrfd_detector,scrfd_predictor,hp,history_data,save_data_dir,wh,batch_img_idx_list,batch_aud_idx_list,total_video_frame):
    # todo: crop_img 兼容npy 和 jpg
    # 训练模式：保存关键点和人脸框信息为npy文件
    face_data_dir = os.path.join(save_data_dir, 'face_data')  # 创建face_data目录保存人脸关键数据
    os.makedirs(face_data_dir, exist_ok=True)
    crop_img_dir = os.path.join(face_data_dir, 'crop_img')   # 创建crop_img目录保存裁剪的人脸图像
    wh_file = os.path.join(save_data_dir, 'wh_value.txt')
    os.makedirs(crop_img_dir, exist_ok=True)
    if is_train == '1':
        # 存在一种情况：假设说batch=4,视频一共10帧，也就是在第三个batch的时候，batch_img_idx_list=[9,10，10，9],batch_aud_idx_list=[9,10，11，12]，这种情况还是需要保存超分图片的
        # 所以两个列表只要有一张图片不一致，就需要保存超分图片
        #看看视频是否处理完成
        video_is_unfinish = False
        if min(batch_aud_idx_list) <=total_video_frame:
            video_is_unfinish = True
        if  video_is_unfinish:
            drivered_op = op(caped_drivered_img2, wh, scrfd_detector, scrfd_predictor, hp, None, digital_human_model.img_size, False)
            drivered_op.flow()  # 2.获取人脸目标框以及关键点
            drivered_face_dict = drivered_op.mp_dict
            
            # 没有人脸的部分

            no_face_indices = []
            if hasattr(drivered_op, 'no_face'):
                no_face_indices = drivered_op.no_face
            for (frame_idx, frame_data) in drivered_face_dict.items():
                temp_data = {}
                curr_frame_idx = batch_img_idx_list[frame_idx] # 获取当前的视频帧
                
                # 收集人脸数据数据
                for k in ['bounding_box','bounding_box_p','landmarks','crop_lm']:
                    if k in frame_data and isinstance(frame_data[k], np.ndarray):
                        temp_data[k] = frame_data[k].tolist()
                    if  frame_idx in no_face_indices:
                        temp_data['no_face'] = True
                    else:
                        temp_data['no_face'] = False
                # 如果没有人脸，为bounding_box_p设置默认值
                if frame_idx in no_face_indices and 'bounding_box_p' not in temp_data:
                    temp_data['bounding_box_p'] = [0.0, 0.0, 0.0, 0.0]

                history_data['{}'.format(curr_frame_idx)] = temp_data
                # 保存crop_img图像 (png格式)
                if 'crop_img' in frame_data and isinstance(frame_data['crop_img'], np.ndarray):
                    cv2.imwrite(os.path.join(crop_img_dir, f'frame_{curr_frame_idx:06d}.jpg'), frame_data['crop_img'])
                else:
                    logger.info(f"第{curr_frame_idx}帧crop_img图像文件不存在")

        else: # 从文件加载数据
            drivered_face_dict = {}
            no_face_indices = []
            if len(history_data) > 0:
                # logger.info(f"[从文件加载历史数据")
                for i,img_idx in enumerate(batch_img_idx_list):
                    drivered_face_dict[i] = history_data.get('{}'.format(img_idx),{})
                    
                        # tt1 = time.time()
                    for k in ['bounding_box','bounding_box_p','landmarks','crop_lm']:
                        if k in drivered_face_dict[i] and isinstance(drivered_face_dict[i][k], list):
                            drivered_face_dict[i][k] = np.array(drivered_face_dict[i][k])
                    if drivered_face_dict[i].get('no_face',True):
                        no_face_indices.append(i)
    
                    frame_path = os.path.join(face_data_dir, 'crop_img', f'frame_{img_idx:06d}.jpg')
                    if os.path.exists(frame_path):
                        img = cv2.imread(frame_path)
                        drivered_face_dict[i]['crop_img'] = img
                    else:
                        drivered_face_dict[i]['crop_img'] = None


    # 非训练模式：尝试从已保存的数据加载并使用
    elif os.path.exists(wh_file):
    # elif is_train == '2':
        drivered_face_dict = {}
        no_face_indices = []
        if len(history_data) > 0:
            # logger.info(f"[从文件加载历史数据")
            for i,img_idx in enumerate(batch_img_idx_list):
                drivered_face_dict[i] = history_data.get('{}'.format(img_idx),{})
                
                    # tt1 = time.time()
                for k in ['bounding_box','bounding_box_p','landmarks','crop_lm']:
                    if k in drivered_face_dict[i] and isinstance(drivered_face_dict[i][k], list):
                        drivered_face_dict[i][k] = np.array(drivered_face_dict[i][k])
                if drivered_face_dict[i].get('no_face',True):
                    no_face_indices.append(i)
 
                frame_path = os.path.join(face_data_dir, 'crop_img', f'frame_{img_idx:06d}.jpg')
                if os.path.exists(frame_path):
                    img = cv2.imread(frame_path)
                    drivered_face_dict[i]['crop_img'] = img
                else:
                    drivered_face_dict[i]['crop_img'] = None
        else:
            drivered_op = op(caped_drivered_img2, wh, scrfd_detector, scrfd_predictor, hp, None, digital_human_model.img_size, False)
            drivered_op.flow()
            drivered_face_dict = drivered_op.mp_dict
            no_face_indices = []
            if hasattr(drivered_op, 'no_face'):
                no_face_indices = drivered_op.no_face
            
    else:
        # 回退到检测模式
        logger.info(f"回退到检测模式")
        drivered_op = op(caped_drivered_img2, wh, scrfd_detector, scrfd_predictor, hp, None, digital_human_model.img_size, False)
        drivered_op.flow()
        drivered_face_dict = drivered_op.mp_dict
        no_face_indices = []
        if hasattr(drivered_op, 'no_face'):
            no_face_indices = drivered_op.no_face
        for i in no_face_indices:
            drivered_face_dict[i]['no_face'] = True
            
    return drivered_face_dict,no_face_indices

def _get_face_params_from_drivered_face_dict(drivered_face_dict,out_shape, output_resize, img_list):
    x1_list, x2_list, y1_list, y2_list = ([], [], [], [])
    get_face_params_time1 = time.time()
    for idx in range(len(drivered_face_dict)):
        facebox = drivered_face_dict[idx]['bounding_box'] # 注意，确定有没有默认值
        x1_list.append(facebox[0])
        x2_list.append(facebox[1])
        y1_list.append(facebox[2])
        y2_list.append(facebox[3])
    drivered_exceptlist = []
    get_face_params_time2 = time.time()
    frame_len = len(drivered_face_dict.keys())
    for i in range(frame_len):
        if len(drivered_face_dict[i]['bounding_box_p']) == 4:
            break
        drivered_exceptlist.append(i)
        print(drivered_exceptlist, '-------------------------------------')
    get_face_params_time3 = time.time()
    for i in drivered_exceptlist:
        drivered_face_dict[i]['bounding_box_p'] = drivered_face_dict[len(drivered_exceptlist)]['bounding_box_p']
        drivered_face_dict[i]['bounding_box'] = drivered_face_dict[len(drivered_exceptlist)]['bounding_box']
        drivered_face_dict[i]['crop_lm'] = drivered_face_dict[len(drivered_exceptlist)]['crop_lm']
        drivered_face_dict[i]['crop_img'] = drivered_face_dict[len(drivered_exceptlist)]['crop_img']
    get_face_params_time4 = time.time()
    keylist = list(drivered_face_dict.keys())
    keylist.sort()
    get_face_params_time5 = time.time()
    for it in keylist:
        if len(drivered_face_dict[it]['bounding_box_p']) != 4:
            print(it, '++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++')
            drivered_face_dict[it]['bounding_box_p'] = drivered_face_dict[it - 1]['bounding_box_p']
            drivered_face_dict[it]['bounding_box'] = drivered_face_dict[it - 1]['bounding_box']
            drivered_face_dict[it]['crop_lm'] = drivered_face_dict[it - 1]['crop_lm']
            drivered_face_dict[it]['crop_img'] = drivered_face_dict[it - 1]['crop_img']
    # get_face_params_time6 = time.time()
    face_params = [out_shape, output_resize, img_list, y1_list, y2_list, x1_list, x2_list]
    # get_face_params_time7 = time.time()
    # logger.info(f"获取人脸参数耗时: 7-6：{get_face_params_time7 - get_face_params_time6:.6f}s，6-5：{get_face_params_time6 - get_face_params_time5:.6f}s，5-4：{get_face_params_time5 - get_face_params_time4:.6f}s，4-3：{get_face_params_time4 - get_face_params_time3:.6f}s，3-2：{get_face_params_time3 - get_face_params_time2:.6f}s，2-1：{get_face_params_time2 - get_face_params_time1:.6f}s")
    return face_params

def audio_transfer(drivered_queue, output_imgs_queue, batch_size=8,use_npy=False):
    output_resize = 1
    # todo:训练和0样本才开启模型，推理时候不开启
    digital_human_model = DigitalHumanModel(GlobalConfig.instance().blend_dynamic, GlobalConfig.instance().chaofen_before, face_blur_detect=True,half=False)
    scrfd_detector,scrfd_predictor,hp = None,None,None
    # if is_train != '2':
    scrfd_detector = FaceDetect(cpu=False, model_path='face_detect_utils/resources/')
    scrfd_predictor = pfpld(cpu=False, model_path='face_detect_utils/resources/')
    hp = Headpose(cpu=False, onnx_path='face_detect_utils/resources/model_float32.onnx')
    logger.info('>>> 数字人图片处理进程启动')
    _init_history_data = {
            'bounding_box_list': [],
            'bounding_box_p_list': [],
            'landmarks_list': [],
            'crop_lm_list': [],
            'no_face_indices': [],
            'speaker_id': "nofind"
    }
    history_data = _init_history_data.copy()  # 整个视频的结果
    face_data_dir = os.path.join('workspace', 'models', history_data.get('speaker_id', "nofind"))
    is_train = '1'
    get_msg_time = []
    infer_time = []
    
    # 存储固定参数
    code,wh,speaker_id,is_train,use_npy,total_video_frame = None,0,"nofind",False,False,-1

    while True:
        try:
            t_get1 = time.time()
            queue_values = drivered_queue.get()
            t_get2 = time.time()
            get_msg_time.append(t_get2 - t_get1)
            logger.info(f"audio_transfer 获取数据耗时: {t_get2 - t_get1}s")
            
            s_au = time.time()
    
            if len(queue_values) == 3: #结束
                mean_get_msg_time = np.mean(np.array(get_msg_time[1:]))*1000
                logger.info(f"audio_transfer 获取数据平均耗时: {mean_get_msg_time}ms/{len(get_msg_time[1:])}")
                mean_infer_time = np.mean(np.array(infer_time[1:]))*1000
                logger.info(f"audio_transfer 推理平均耗时: {mean_infer_time}ms/{len(infer_time[1:])}")
                img_list, audio_feature_list, code = queue_values
                wh = -1
                frameId = -1
                logger.info('>>> audio_transfer get exception msg:%s', -1)
                if is_train == '1':
                    history_data_path = os.path.join(face_data_dir, 'face_data', 'face_data.json')
                    if not os.path.exists(history_data_path):
                        # 将所有 NumPy 数组转换为列表，使其可以被 JSON 序列化
                        history_data_json = {}
                    for key, value in history_data.items():
                        if isinstance(value, np.ndarray):
                            history_data_json[key] = value.tolist()
                        elif isinstance(value, dict):
                            history_data_json[key] = {}
                            for k, v in value.items():
                                if k == 'crop_img': # 不保存crop_img
                                    continue
                                if isinstance(v, np.ndarray):
                                    history_data_json[key][k] = v.tolist()
                                else:
                                    history_data_json[key][k] = v
                        elif isinstance(value, list):
                            history_data_json[key] = []
                            for item in value:
                                if isinstance(item, np.ndarray):
                                    history_data_json[key].append(item.tolist())
                                else:
                                    history_data_json[key].append(item)
                        else:
                            history_data_json[key] = value
                    
                    with open(history_data_path, 'w') as f:
                        json.dump(history_data_json, f)
                        logger.info(f"[保存history_data到{history_data_path}")
            elif len(queue_values) == 6: # 开始
                code,wh,speaker_id,is_train,use_npy,total_video_frame = queue_values
                logger.info(f"audio_transfer 开始处理视频: {code}, wh: {wh}, speaker_id: {speaker_id}, is_train: {is_train}, use_npy: {use_npy}, total_video_frame: {total_video_frame}")
                continue
            else:
                drivered_fnames_list, audio_feature_list,batch_img_idx_list,batch_aud_idx_list = queue_values
                ta1 =time.time()
                audio_feature_list = [np.array(w,dtype=np.float32) for w in audio_feature_list]
                ta2 = time.time()
                print('-------------------------------------',audio_feature_list[0].shape,(ta2-ta1)*1000, '-------------------------------------')
                img_list = [(np.load(f) if use_npy else cv2.imread(f)) for f in drivered_fnames_list]
                frameId = batch_aud_idx_list[-1] if len(batch_aud_idx_list)>0 else -1
                logger.info('>>> audio_transfer get message:%s', frameId)

            ###############################1.先判断是否有异常情况，或者已经结束状态##############################
            if type(img_list) == bool :
                if img_list == True:
                    logger.info('[{}]任务数字人图片处理已完成'.format(code))
                    output_imgs_queue.put([True, 'success', code])
                else:
                    logger.info('[{}]任务数字人图片处理异常结束'.format(code))
                    output_imgs_queue.put([False, audio_feature_list, code])
                torch.cuda.empty_cache()
                continue
            t_get22 = time.time()
            # logger.info(f"audio_transfer tget22-tget2: {t_get22 - t_get2}s")
            out_shape = img_list[0].shape
            ####################################4.保存/读取超分处理图片##############################
            if wh > 0:
                img_list = _get_chaofen_src_imgs(code,is_train,img_list, digital_human_model, scrfd_detector, frameId,batch_img_idx_list, batch_aud_idx_list, speaker_id,total_video_frame,use_npy=use_npy)
            if wh == 0 or wh == -1:
                wh = digital_human_model.drivered_wh

            ####################################5.保存/读取人脸数据 ##############################
            caped_drivered_img2 = warp_imgs(img_list)
            # tget3 = time.time()
            # logger.info(f"audio_transfer tget3-tget22: {tget3 - t_get22}s")
            #切换speaker_id时，重新加载人脸数据
            if speaker_id != history_data['speaker_id']:
                history_data = _init_history_data.copy()
                history_data['speaker_id'] = speaker_id
                face_data_dir = os.path.join('workspace', 'models', speaker_id)
                history_data_path = os.path.join(face_data_dir, 'face_data', 'face_data.json')
                if os.path.exists(history_data_path):
                    with open(history_data_path, 'r') as f:
                        history_data = json.load(f)
            # 打印batch_img_idx_list
            drivered_face_dict,no_face_indices = _get_drivered_face_dict(code,is_train,caped_drivered_img2,digital_human_model,scrfd_detector,scrfd_predictor,hp,history_data,face_data_dir,wh,batch_img_idx_list,batch_aud_idx_list,total_video_frame)
            face_params = _get_face_params_from_drivered_face_dict(drivered_face_dict,out_shape, output_resize, img_list) 

            ################################## 6.开始推理结果####################################################
            t6 = time.time()
            # logger.info(f"audio_transfer t6-tget4: {t6 - tget4}s")
            output_imgs = get_blend_imgs(batch_size, audio_feature_list, drivered_face_dict, GlobalConfig.instance().blend_dynamic, face_params, digital_human_model, frameId)   #3.开始推理
            logger.info(f"推理耗时: {time.time() - t6}s")
            
            if len(no_face_indices) != 0:
                for id in no_face_indices:
                    output_imgs[id] = img_list[id]
            # t_put1 = time.time()
            output_imgs_queue.put([0, 0, output_imgs])  #4.发送数据到视频处理进程
            # t_put2 = time.time()
            # logger.info(f"output_imgs_queue put耗时: {t_put2 - t_put1}s")
            infer_time.append(time.time() - t_get1)

            # torch.cuda.empty_cache()
            logger.info('audio_transfer >>>>>>>>>>> 发送完成数据大小:{}, frameId:{}, cost:{}s'.format(len(output_imgs), frameId, time.time() - t_get1))
        except Exception as e:
            print(traceback.format_exc())
            output_imgs_queue.put([False, '数字人处理失败，失败原因:[{}]'.format(e.__str__()), ''])
            time.sleep(1)
            torch.cuda.empty_cache()
            continue

# 合成视频处理
def write_video(output_imgs_queue, temp_dir, result_dir, work_id, audio_path, result_queue, width, height, fps, watermark_switch=0, digital_auth=0):
    output_mp4 = os.path.join(temp_dir, "{}-t.mp4".format(work_id))
    fourcc = (cv2.VideoWriter_fourcc)(*"mp4v")
    result_path = os.path.join(result_dir, "{}-r.mp4".format(work_id))
    video_write = cv2.VideoWriter(output_mp4, fourcc, fps, (
     width, height))

    try:
        while True:
            state, reason, value_ = output_imgs_queue.get()
            logger.info("write_video 接收到数据")
            if type(state) == bool and state == True:
                logger.info("[{}]视频帧队列处理已结束".format(work_id))
                break
            elif type(state) == bool and  state == False:
                    logger.error("[{}]任务视频帧队列 -> 异常原因:[{}]".format(work_id, reason))
                    raise CustomError(reason)
                    break
            for result_img in value_:
                video_write.write(result_img)

        video_write.release()
        if watermark_switch == 1:
            if digital_auth == 1:
                logger.info("[{}]任务需要水印和数字人标识".format(work_id))
                if width > height:
                    command = 'ffmpeg -y -i {} -i {} -i {} -i {} -filter_complex "overlay=(main_w-overlay_w)-10:(main_h-overlay_h)-10,overlay=(main_w-overlay_w)-10:10" -c:a aac -crf 15 -strict -2 {}'.format(audio_path, output_mp4, GlobalConfig.instance().watermark_path, GlobalConfig.instance().digital_auth_path, result_path)
                    logger.info("command:{}".format(command))
                else:
                    command = 'ffmpeg -y -i {} -i {} -i {} -i {} -filter_complex "overlay=(main_w-overlay_w)-10:(main_h-overlay_h)-10,overlay=(main_w-overlay_w)-10:10" -c:a aac -crf 15 -strict -2 {}'.format(audio_path, output_mp4, GlobalConfig.instance().watermark_path, GlobalConfig.instance().digital_auth_path, result_path)
                    logger.info("command:{}".format(command))
            else:
                logger.info("[{}]任务需要水印".format(work_id))
                command = 'ffmpeg -y -i {} -i {} -i {} -filter_complex "overlay=(main_w-overlay_w)-10:(main_h-overlay_h)-10" -c:a aac -crf 15 -strict -2 {}'.format(audio_path, output_mp4, GlobalConfig.instance().watermark_path, result_path)
                logger.info("command:{}".format(command))
        elif watermark_switch == 0:
            if digital_auth == 1:
                logger.info("[{}]任务需要数字人标识".format(work_id))
                if width > height:
                    command = 'ffmpeg -loglevel warning -y -i {} -i {} -i {} -filter_complex "overlay=(main_w-overlay_w)-10:10" -c:a aac -crf 15 -strict -2 {}'.format(audio_path, output_mp4, GlobalConfig.instance().digital_auth_path, result_path)
                    logger.info("command:{}".format(command))
                else:
                    command = 'ffmpeg -loglevel warning -y -i {} -i {} -i {} -filter_complex "overlay=(main_w-overlay_w)-10:10" -c:a aac -crf 15 -strict -2 {}'.format(audio_path, output_mp4, GlobalConfig.instance().digital_auth_path, result_path)
                    logger.info("command:{}".format(command))
            else:
                command = "ffmpeg -loglevel warning -y -i {} -i {} -c:a aac -c:v libx264 -crf 15 -strict -2 {}".format(audio_path, output_mp4, result_path)
                logger.info("command:{}".format(command))
        subprocess.call(command, shell=True)
        print("###### write over")
        result_queue.put([True, result_path])

    except Exception as e:
        try:
            logger.error("[{}]视频帧队列处理异常结束，异常原因:[{}]".format(work_id, e.__str__()))
            result_queue.put([False, "[{}]视频帧队列处理异常结束，异常原因:[{}]".format(work_id, e.__str__())])
        finally:
            e = None
            del e

    logger.info("后处理进程结束")


def save_video_ffmpeg(input_video_path, output_video_path):
    audio_file_path = input_video_path.replace(".mp4", ".aac")
    if not os.path.exists(audio_file_path):
        os.system('ffmpeg -y -hide_banner -loglevel error -i "' + str(input_video_path) + '" -f wav -vn  "' + str(audio_file_path) + '"')
    if os.path.exists(audio_file_path):
        os.rename(output_video_path, output_video_path.replace(".mp4", "_no_audio.mp4"))
        start = time.time()
        os.system('ffmpeg -y -hide_banner -loglevel error  -i "' + str(output_video_path.replace(".mp4", "_no_audio.mp4")) + '" -i "' + str(audio_file_path) + '" -c:v libx264 "' + str(output_video_path) + '"')
        print("add audio time cost", time.time() - start)
        os.remove(output_video_path.replace(".mp4", "_no_audio.mp4"))
        os.remove(audio_file_path)
    return output_video_path


class FaceDetectThread(Linker):

    def __init__(self, queue_list):
        super().__init__(queue_list, fps_counter=True)
        self.fd = FaceDetect5Landmarks(mode="scrfd_500m")

    def forward_func(self, something_in):
        frame = something_in
        bboxes_scrfd, kpss_scrfd = self.fd.get_bboxes(frame, min_bbox_size=64)
        if len(bboxes_scrfd) == 0:
            return [
             frame, None, None, None]
        face_image_, mat_rev_, roi_box_ = self.fd.get_single_face(crop_size=512, mode="mtcnn_512", apply_roi=True)
        return [frame, face_image_, mat_rev_, roi_box_]


class FaceRestoreThread(Linker):

    def __init__(self, queue_list):
        super().__init__(queue_list, fps_counter=True)
        self.gfp = GFPGAN(model_type="GFPGANv1.4", provider="gpu")

    def forward_func(self, something_in):
        src_face_image_ = something_in[1]
        if src_face_image_ is None:
            return [
             None] + something_in
        face_restore_out_ = self.gfp.forward(src_face_image_)
        torch.cuda.empty_cache()
        return [face_restore_out_] + something_in


class FaceParseThread(Linker):

    def __init__(self, queue_list):
        super().__init__(queue_list, fps_counter=True)
        self.face_mask_ = self.get_face_mask(mask_shape=(512, 512))

    def get_face_mask(self, mask_shape):
        mask = np.zeros((512, 512)).astype(np.float32)
        cv2.ellipse(mask, (256, 256), (220, 160), 90, 0, 360, (255, 255, 255), -1)
        thres = 20
        mask[:thres, :] = 0
        mask[-thres:, :] = 0
        mask[:, :thres] = 0
        mask[:, -thres:] = 0
        mask = cv2.stackBlur(mask, (201, 201))
        mask = mask / 255.0
        mask = cv2.resize(mask, mask_shape)
        return mask[..., np.newaxis]

    def forward_func(self, something_in):
        if something_in[0] is None:
            return something_in + [None]
        return something_in + [self.face_mask_]


class FaceReverseThread(Linker):

    def __init__(self, queue_list):
        super().__init__(queue_list, fps_counter=True)
        self.counter = 0
        self.start_time = time.time()

    def forward_func(self, something_in):
        face_restore_out = something_in[0]
        src_img_in = something_in[1]
        if face_restore_out is not None:
            mat_rev = something_in[3]
            roi_box = something_in[4]
            face_mask_ = something_in[5]
            restore_roi = CVImage.recover_from_reverse_matrix(face_restore_out, src_img_in[roi_box[1]:roi_box[3], roi_box[0]:roi_box[2]], mat_rev, face_mask_, img_fg_mask=face_mask_)
            src_img_in[roi_box[1]:roi_box[3], roi_box[0]:roi_box[2]] = restore_roi
        return [src_img_in]


def write_video_chaofen(output_imgs_queue, temp_dir, result_dir, work_id, audio_path, result_queue, width, height, fps, watermark_switch=0, digital_auth=0):
    output_mp4 = os.path.join(temp_dir, "{}-t.mp4".format(work_id))
    fourcc = (cv2.VideoWriter_fourcc)(*"mp4v")
    result_path = os.path.join(result_dir, "{}-r.mp4".format(work_id))
    video_write = cv2.VideoWriter(output_mp4, fourcc, fps, (width, height))
    try:
        q0 = Queue(2)
        q1 = Queue(2)
        q2 = Queue(2)
        q3 = Queue(2)
        q4 = Queue(2)
        fdt = FaceDetectThread([q0, q1])
        frt = FaceRestoreThread([q1, q2])
        fpt = FaceParseThread([q2, q3])
        fret = FaceReverseThread([q3, q4])
        cvvwt = CVVideoWriterThread(video_write, [q4])
        threads_list = [fdt, frt, fpt, fret, cvvwt]
        for thread_ in threads_list:
            thread_.start()
        state, reason, value_ = output_imgs_queue.get()
        if type(state) == bool and state == True:
            logger.info("[{}]视频帧队列处理已结束".format(work_id))
            q0.put(None)
            for thread_ in threads_list:
                    thread_.join()


        elif type(state) == bool and state == False:
            logger.error("[{}]任务视频帧队列 -> 异常原因:[{}]".format(work_id, reason))
            q0.put(None)
            for thread_ in threads_list:
                thread_.join()

            raise CustomError(reason)

        for result_img in value_:
            q0.put(result_img)

        video_write.release()
        if watermark_switch == 1:
            if digital_auth == 1:
                logger.info("[{}]任务需要水印和数字人标识".format(work_id))
                if width > height:
                    command = 'ffmpeg -y -i {} -i {} -i {} -i {} -filter_complex "overlay=(main_w-overlay_w)-10:(main_h-overlay_h)-10,overlay=(main_w-overlay_w)-10:10" -c:a aac -crf 15 -strict -2 {}'.format(audio_path, output_mp4, GlobalConfig.instance().watermark_path, GlobalConfig.instance().digital_auth_path, result_path)
                    logger.info("command:{}".format(command))
                else:
                    command = 'ffmpeg -y -i {} -i {} -i {} -i {} -filter_complex "overlay=(main_w-overlay_w)-10:(main_h-overlay_h)-10,overlay=(main_w-overlay_w)-10:10" -c:a aac -crf 15 -strict -2 {}'.format(audio_path, output_mp4, GlobalConfig.instance().watermark_path, GlobalConfig.instance().digital_auth_path, result_path)
                    logger.info("command:{}".format(command))
            else:
            
                logger.info("[{}]任务需要水印".format(work_id))
                command = 'ffmpeg -y -i {} -i {} -i {} -filter_complex "overlay=(main_w-overlay_w)-10:(main_h-overlay_h)-10" -c:a aac -crf 15 -strict -2 {}'.format(audio_path, output_mp4, GlobalConfig.instance().watermark_path, result_path)
                logger.info("command:{}".format(command))
        elif watermark_switch == 0:
            if digital_auth == 1:
                logger.info("[{}]任务需要数字人标识".format(work_id))
                if width > height:
                    command = 'ffmpeg -y -i {} -i {} -i {} -filter_complex "overlay=(main_w-overlay_w)-10:10" -c:a aac -crf 15 -strict -2 {}'.format(audio_path, output_mp4, GlobalConfig.instance().digital_auth_path, result_path)
                    logger.info("command:{}".format(command))
                else:
                    command = 'ffmpeg -y -i {} -i {} -i {} -filter_complex "overlay=(main_w-overlay_w)-10:10" -c:a aac -crf 15 -strict -2 {}'.format(audio_path, output_mp4, GlobalConfig.instance().digital_auth_path, result_path)
                    logger.info("command:{}".format(command))
            else:
                command = "ffmpeg -y -i {} -i {} -c:a aac -c:v libx264 -crf 15 -strict -2 {}".format(audio_path, output_mp4, result_path)
                logger.info("command:{}".format(command))
        subprocess.call(command, shell=True)
        print("###### write over")
        result_queue.put([True, result_path])

    except Exception as e:
        try:
            logger.error("[{}]视频帧队列处理异常结束，异常原因:[{}]".format(work_id, e.__str__()))
            result_queue.put([False, "[{}]视频帧队列处理异常结束，异常原因:[{}]".format(work_id, e.__str__())])
        finally:
            e = None
            del e

    else:
        logger.info("后处理进程结束")


def video_synthesis(output_imgs_queue):
    img_id = 0
    st = time.time()
    while True:
        if not output_imgs_queue.empty():
            et = time.time()
            print("表情迁移首次出现耗时======================:", et - st)
            output_imgs = output_imgs_queue.get()
            for img in output_imgs:
                time.sleep(0.03125)
                cv2.imshow("output_imgs", img)
                cv2.waitKey(1)
            else:
                st = time.time()


def hy_fun(wenet_model, audio_path, drivered_path, output_dir, work_id):
    drivered_queue = multiprocessing.Queue(10)
    output_imgs_queue = multiprocessing.Queue(10)
    result_queue = multiprocessing.Queue(1)
    process_list = []
    audio_wenet_feature = get_aud_feat1(audio_path, fps=30, wenet_model=wenet_model)
    process_list.append(Process(target=drivered_video, args=(drivered_queue, drivered_path, audio_wenet_feature)))
    process_list.append(Process(target=audio_transfer, args=(drivered_queue, output_imgs_queue, 8, '0')))
    process_list.append(Process(target=write_video, args=(output_imgs_queue, output_dir, output_dir, work_id, audio_path, result_queue)))
    [p.start() for p in process_list]
    [p.join() for p in process_list]
    print("主进程结束")
    try:
        result_path = result_queue.get(True, timeout=10)
        return (0, result_path)
    except Empty:
        return (1, 'generate error')
    finally:
        return None


class Status(Enum):
    run = 1
    success = 2
    error = 3


def init_wh_process(in_queue, out_queue):
    # 初始化人脸检测器和关键点检测器
    
    face_detector = FaceDetect('scrfd_500m', False, 'face_detect_utils/resources/')
    plfd = pfpld(False, 'face_detect_utils/resources/')
    logger.info('>>> init_wh_process进程启动')

    while True:
        try:
            logger.info('>>> init_wh_process进程等待任务...')
            code,drivered_path = in_queue.get()
            pre_process_dir = os.path.join(GlobalConfig.instance().temp_dir, "pre_process_dir")
            logger.info("init_wh_process进程收到数据: {} -- > {}".format(code, drivered_path))
            wh_list = []
            s = time.time()
            cap = cv2.VideoCapture(drivered_path)
            count = 0
            multi_face = False
            try:
                try:
                    if cap.isOpened():
                        while count < 100:
                            ret, frame = cap.read()
                            if not ret:
                                break
                            try:
                                bboxes, kpss = face_detector.get_bboxes(frame)
                            except Exception as e:
                                try:
                                    logger.error("[{}]init_wh exception: {}".format(code, e))
                                finally:
                                    e = None
                                    del e

                            if len(bboxes) > 0:
                                bbox = bboxes[0]
                                x1, y1, x2, y2, score = bbox.astype(np.int32)
                                x1 = max(x1 - int((x2 - x1) * 0.1), 0)
                                x2 = x2 + int((x2 - x1) * 0.1)
                                y2 = y2 + int((y2 - y1) * 0.1)
                                y1 = max(y1, 0)
                                face_img = frame[y1:y2, x1:x2,:]
                                pots = plfd.forward(face_img)[0]
                                landmarks = np.array([[x1 + x, y1 + y] for x, y in pots.astype(np.int32)])
                                xmin, ymin, w, h = cv2.boundingRect(np.array(landmarks))
                                wh_list.append(w / h)
                            if len(bboxes) > 1:
                                multi_face = True
                            count += 1
                except Exception as e1:
                    try:
                        logger.error("[{}]init_wh exception: {}".format(code, e1))
                    finally:
                        e1 = None
                        del e1

            finally:
                cap.release()
            if len(wh_list) == 0:
                wh = 0
            else:
                wh = np.mean(np.array(wh_list))

            logger.info("[%s]init_wh result :[%s]， cost: %s s" % (code, wh, time.time() - s))
            out_queue.put([code,wh,multi_face])
            torch.cuda.empty_cache()
        except Empty:
            # 当30秒内没有任务时，不要退出，只记录日志并继续等待
            logger.warning('>>> init_wh_process队列30秒内未收到任务，继续等待')
            continue
        except Exception as e:
            # 其他异常也记录但不退出
            logger.error('>>> init_wh_process处理任务时出错: %s', str(e))
            logger.error(traceback.format_exc())
            continue


def init_wh(code, drivered_path):
    s = time.time()
    face_detector = FaceDetect(cpu=False, model_path="face_detect_utils/resources/")
    plfd = pfpld(cpu=False, model_path="face_detect_utils/resources/")
    wh_list = []
    cap = cv2.VideoCapture(drivered_path)
    count = 0
    try:
        try:
            if cap.isOpened():
                while count < 100:
                    ret, frame = cap.read()
                    if not ret:
                        break
                    try:
                        bboxes, kpss = face_detector.get_bboxes(frame)
                    except Exception as e:
                        try:
                            logger.error("[{}]init_wh exception: {}".format(code, e))
                        finally:
                            e = None
                            del e

                    if len(bboxes) > 0:
                        bbox = bboxes[0]
                        x1, y1, x2, y2, score = bbox.astype(np.int32)
                        x1 = max(x1 - int((x2 - x1) * 0.1), 0)
                        x2 = x2 + int((x2 - x1) * 0.1)
                        y2 = y2 + int((y2 - y1) * 0.1)
                        y1 = max(y1, 0)
                        face_img = frame[y1:y2, x1:x2,:]
                        pots = plfd.forward(face_img)[0]
                        landmarks = np.array([[x1 + x, y1 + y] for x, y in pots.astype(np.int32)])
                        xmin, ymin, w, h = cv2.boundingRect(np.array(landmarks))
                        logger.info("xmin, ymin, w, h: {} {} {} {}".format(xmin, ymin, w, h))
                        wh_list.append(w / h)
                    count += 1

        except Exception as e1:
            try:
                logger.error("[{}]init_wh exception: {}".format(code, e1))
            finally:
                e1 = None
                del e1

    finally:
        cap.release()

    if len(wh_list) == 0:
        wh = 0
    else:
        wh = np.mean(np.array(wh_list))
    logger.info("[%s]init_wh result :[%s]， cost: %s s" % (code, wh, time.time() - s))
    torch.cuda.empty_cache()
    return wh


def get_video_info(video_file):
    cap = cv2.VideoCapture(video_file)
    fps = round(cap.get(cv2.CAP_PROP_FPS))
    width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
    height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
    fourcc = int(cap.get(cv2.CAP_PROP_FOURCC))
    cap.release()
    return (fps, width, height, fourcc)


def format_video_audio(code, video_path, audio_path, fourcc):

    if not fourcc == cv2.VideoWriter_fourcc("H", "2", "6", "4"):
        if fourcc == cv2.VideoWriter_fourcc("a", "v", "c", "1") or fourcc == cv2.VideoWriter_fourcc("h", "2", "6", "4"):
            ffmpeg_command = "ffmpeg -loglevel warning -i %s -crf 15 -vcodec copy -an -y %s"
        else:
            ffmpeg_command = "ffmpeg -loglevel warning -i %s -c:v libx264 -crf 15 -an -y %s"  #todo:这个是有问题的
    else:
        ffmpeg_command = "ffmpeg -loglevel warning -i %s -c:v libx264 -crf 15 -an -y %s"
    video_format = os.path.join(GlobalConfig.instance().temp_dir, code + "_format.mp4")
    ffmpeg_command = ffmpeg_command % (video_path, video_format)
    logger.info("[{}] -> ffmpeg video: {}".format(code, ffmpeg_command))
    os.system(ffmpeg_command)
    if not os.path.exists(video_format):
        raise Exception("format video error")
    ffmpeg_command = "ffmpeg -loglevel warning -i %s -ac 1 -ar 16000 -acodec pcm_s16le -y  %s"
    audio_format = os.path.join(GlobalConfig.instance().temp_dir, code + "_format.wav")
    ffmpeg_command = ffmpeg_command % (audio_path, audio_format)
    logger.info("[{}] -> ffmpeg audio: {}".format(code, ffmpeg_command))
    os.system(ffmpeg_command)
    if not os.path.exists(audio_format):
        raise Exception("format audio error")
    return (
     video_format, audio_format)


def get_license():
    logger.info("license check start ...")
    while True:
        if not check_lc():
            logger.info("license check failed")
        time.sleep(30)


def a():
    if GlobalConfig.instance().register_enable == 1:
        result = register_host()
        if not result:
            raise Exception("服务注册失败.")
        threading.Thread(target=repost_host).start()
    else:
        logger.warning(" -> 服务不进行注册")


class TransDhTask(object):

    def __init__(self, *args, **kwargs):
        logger.info("TransDhTask init")
        set_start_method("spawn", force=True)
        self.run_lock = threading.Lock()
        self.task_dic = {}
        
        self.run_flag = False
        self.use_npy = True
        self.is_train = '1'
        self.batch_size = int(GlobalConfig.instance().batch_size)
        self.drivered_queue = multiprocessing.Queue(16,)
        self.output_imgs_queue = multiprocessing.Queue(10)
        self.result_queue = multiprocessing.Queue(1)
        self.wenet_model = load_ppg_model("wenet/examples/aishell/aidata/conf/train_conformer_multi_cn.yaml", "wenet/examples/aishell/aidata/exp/conformer/wenetmodel.pt", "cuda")  # 音频特征提取256维
        
        self.audio_transfer_process = multiprocessing.Process(target=audio_transfer, args=(
         self.drivered_queue, self.output_imgs_queue,self.batch_size, self.use_npy),
          daemon=True)
        self.audio_transfer_process.start()    # 音频特征提取

        ################################################################ 获取wh值 ################################################################
        self.init_wh_queue = multiprocessing.Queue(2)
        self.init_wh_queue_output = multiprocessing.Queue(2)
        multiprocessing.Process(target=init_wh_process, args=(     
         self.init_wh_queue, self.init_wh_queue_output),
          daemon=True).start()  # 获取人脸长宽比
        


    @classmethod
    def instance(cls, *args, **kwargs):
        if not hasattr(TransDhTask, "_instance"):
            TransDhTask._instance = TransDhTask(*args, **kwargs)
        return TransDhTask._instance

    def work(self, audio_url, video_url, code, watermark_switch, speaker_id, is_train, digital_auth, chaofen, pn):
        logger.info("任务:{} -> audio_url:{}  video_url:{}".format(code,audio_url,video_url))

        st = time.time()
        self.run_flag = True
        try:
            try:
                self.change_task_status(code, Status.run, 0, "", "")
                try:
                    s1 = time.time()
                    fps, width, height, fourcc = get_video_info(video_url)
                    logger.info("[{}] -> 获取视频信息耗时:fps:{}, width:{}, height:{}, fourcc:{}".format(code, fps, width, height, fourcc))
                    _tmp_audio_path, _tmp_video_path = self.preprocess(audio_url, video_url, code)
                    _video_url, _audio_url = format_video_audio(code, _tmp_video_path, _tmp_audio_path, fourcc)
                    logger.info("[{}] -> 预处理耗时:{}s".format(code, time.time() - s1))
                except Exception as e:
                    try:
                        traceback.print_exc()
                        logger.error("[{}]预处理失败，异常信息:[{}]".format(code, e.__str__()))
                        raise CustomError("[{}]预处理失败，异常信息:[{}]".format(code, e.__str__()))
                    finally:
                        e = None
                        del e


                if not (os.path.exists(_video_url) and os.path.exists(_audio_url)):
                    raise Exception("Video input or audio input download processing exception")
                self.change_task_status(code, Status.run, 10, "", "文件下载完成")

                ######################################## 1.获取wh值 ########################################
                wh = 0
                wh_cache = -1 # 是否读取到wh值的flag
                wh_cal_flag = False # 是否计算wh值的flag
                if is_train == '2':
                    try:
                        # 1. 加载wh值
                        wh_file = os.path.join('workspace', 'models', speaker_id, 'wh_value.txt')
                        if  os.path.exists(wh_file):
                            with open(wh_file, 'r') as f:
                                loaded_wh = float(f.read().strip())
                                logger.info(f"成功加载wh值: {loaded_wh}")
                                wh = wh_cache = loaded_wh
                           
                    except Exception as e:
                        logger.error("[{}] 获取wh值失败，重新计算wh值".format(code))

                if wh_cache == -1 or is_train != '2':  # 如果wh_cache为-1，或者不是推理模式，则重新计算wh值
                    # 检查init_wh_process进程是否存活
                    if hasattr(self, 'init_wh_process') and not self.init_wh_process.is_alive():
                        logger.warning('>>> init_wh_process进程已退出，尝试重新启动')
                        # 重新创建并启动进程
                        self.init_wh_process = multiprocessing.Process(
                            target=init_wh_process, 
                            args=(self.init_wh_queue, self.init_wh_queue_output),
                            daemon=True
                        )
                        self.init_wh_process.start()
                        logger.info('>>> init_wh_process进程已重新启动，pid: %s', self.init_wh_process.pid)

                    logger.info("[{}] 重新计算wh值".format(code))
                    self.init_wh_queue.put([code, _video_url])
                    logger.info("[{}] 放入init_wh_queue".format(code))
                    wh_cal_flag = True
                ######################################## 2.音频特征提取 ########################################
                try:
                    print(">>> 777   {}".format(fps))
                    s = time.time()
                    audio_wenet_feature = get_aud_feat1(_audio_url, fps=fps, wenet_model=(self.wenet_model))
                    logger.info("[{}] -> get_aud_feat1 cost:{}s".format(code, time.time() - s))
                except Exception as e:
                    try:
                        traceback.print_exc()
                        logger.error("[{}]音频特征提取失败，异常信息:[{}]".format(code, e.__str__()))
                        raise CustomError("[{}]音频特征提取失败，异常信息:[{}]".format(code, e.__str__()))
                    finally:
                        e = None
                        del e

                self.change_task_status(code, Status.run, 20, "", "音频特征提取完成")
                process_list = []

                ######################################## 3。如果计算wh,获取wh值 ########################################
                if wh_cal_flag:  # 如果wh_cache为-1，或者不是训练模式，则重新计算wh值
                    try:
                        wh_output = self.init_wh_queue_output.get(timeout=10)
                        if wh_output[0] == code:
                            wh = wh_output[1]
                            if is_train == '1': # 训练模式，保存wh值
                                with open(os.path.join('workspace', 'models', speaker_id, 'wh_value.txt'), 'w') as f:
                                    f.write(str(wh))
                                    logger.info(f"训练模式，保存wh值: {wh}")
                        if wh_output[2]:
                            raise Exception("Multiple faces detected")
                    except Exception as e1:
                        try:
                            print(traceback.format_exc())
                            raise Exception(e1)
                        finally:
                            e1 = None
                            del e1
                logger.info("[{}] -> wh: [{}]".format(code, wh))
                ######################################## 4. 视频处理 ########################################   
                if pn == 1: # 正反循环模式
                    process_list.append(Process(target=drivered_video_pn, args=(
                    code, self.drivered_queue, _tmp_video_path, audio_wenet_feature,
                    self.batch_size, wh,self.use_npy,is_train,speaker_id),
                    daemon=True))
                else: # 单向循环模式
                    process_list.append(Process(target=drivered_video, args=(
                    code, self.drivered_queue, _tmp_video_path, audio_wenet_feature,
                    self.batch_size, wh),
                    daemon=True))
                ######################################## 5. 视频处理 ########################################   
                if chaofen == 1 and GlobalConfig.instance().chaofen_after == "1":
                    process_list.append(Process(target=write_video_chaofen, args=(
                        self.output_imgs_queue, GlobalConfig.instance().temp_dir,
                        GlobalConfig.instance().result_dir, code,
                        _tmp_audio_path,
                        self.result_queue, width, height, fps, watermark_switch, digital_auth),
                        daemon=True))
                else:
                    process_list.append(Process(target=write_video, args=(
                        self.output_imgs_queue, GlobalConfig.instance().temp_dir,
                        GlobalConfig.instance().result_dir, code,
                        _tmp_audio_path,
                        self.result_queue, width, height, fps, watermark_switch, digital_auth),
                        daemon=True))
                [p.start() for p in process_list]
                [p.join() for p in process_list]
                try:
                    state, result_path = self.result_queue.get(True, timeout=100)
                    print(">>>>>>>>>>>>>>1111 {} {}".format(state, result_path))
                    if state:
                        logger.info(">>>>>>>>>>>>>>算法任务耗时：{:.3f}s".format(time.time() - s1))
                        self.change_task_status(code, Status.run, 90, result_path, "视频处理完成")
                        _remote_file = os.path.join(GlobalConfig.instance().result_dir, "{}.mp4".format(code))
                        _final_url = result_path
                        logger.info("[{}]任务最终合成结果: {}".format(code, _final_url))
                        self.change_task_status(code, Status.success, 100, _final_url, "任务完成")
                        sweep([GlobalConfig.instance().result_dir], True if GlobalConfig.instance().result_clean_switch == "1" else False)
                    else:
                        self.change_task_status(code, Status.error, 0, "", result_path)
                except Empty:
                    self.change_task_status(code, Status.error, 0, "", "**生成视频失败")

                finally:
                    del audio_wenet_feature
                    gc.collect()

            except Exception as e:
                try:
                    traceback.print_exc()
                    logger.error("[{}]任务执行失败，异常信息:[{}]".format(code, e.__str__()))
                    self.change_task_status(code, Status.error, 0, "", e.__str__())
                finally:
                    e = None
                    del e

        finally:
            sweep([GlobalConfig.instance().temp_dir], True if GlobalConfig.instance().temp_clean_switch == "1" else False)
            self.drivered_queue.empty()
            self.output_imgs_queue.empty()
            self.result_queue.empty()
            torch.cuda.empty_cache()
            self.run_flag = False
            logger.info(">>> 任务:{} 耗时:{} ".format(code, time.time() - st))

    def preprocess(self, audio_url, video_url, code):
        s_pre = time.time()
        try:
            if audio_url.startswith("http:") or audio_url.startswith("https:"):
                _tmp_audio_path = os.path.join(GlobalConfig.instance().temp_dir, "{}.wav".format(code))
                download_file(audio_url, _tmp_audio_path)
            else:
                _tmp_audio_path = audio_url
        except Exception as e:
            try:
                traceback.print_exc()
                raise CustomError("[{}]音频下载失败，异常信息:[{}]".format(code, e.__str__()))
            finally:
                e = None
                del e


        try:
            if video_url.startswith("http:") or video_url.startswith("https:"):
                _tmp_video_path = os.path.join(GlobalConfig.instance().temp_dir, "{}.mp4".format(code))
                download_file(video_url, _tmp_video_path)
            else:
                _tmp_video_path = video_url
        except Exception as e:
            try:
                traceback.print_exc()
                raise CustomError("[{}]视频下载失败，异常信息:[{}]".format(code, e.__str__()))
            finally:
                e = None

        print("--------------------> download cost:{}s".format(time.time() - s_pre))
        return (_tmp_audio_path, _tmp_video_path)

    def change_task_status(self, code, status: Status, progress: int, result: str, msg=''):
        try:
            try:
                self.run_lock.acquire()
                if code in self.task_dic:
                    self.task_dic[code] = (
                     status, progress, result, msg)
            except Exception as e:
                try:
                    traceback.print_exc()
                    logger.error("[{}]修改任务状态异常，异常信息:[{}]".format(code, e.__str__()))
                finally:
                    e = None
                    del e

        finally:
            self.run_lock.release()



if __name__ == "__main__":
    set_start_method("spawn", force=True)
    wenet_model = load_ppg_model("wenet/examples/aishell/aidata/conf/train_conformer_multi_cn.yaml", "wenet/examples/aishell/aidata/exp/conformer/wenetmodel.pt", "cuda")
    st = time.time()
    result = hy_fun(wenet_model, "test_data/audio/driver_add_valume.wav", "./landmark2face_wy/checkpoints/hy/1.mp4", "./result", 1001)
    print(result, time.time() - st)