# uncompyle6 version 3.9.2
# Python bytecode version base 3.8.0 (3413)
# Decompiled from: Python 3.10.9 | packaged by Anaconda, Inc. | (main, Mar  1 2023, 18:18:15) [MSC v.1916 64 bit (AMD64)]
# Embedded file name: /code/service/trans_dh_service.py
# Compiled at: 2024-06-11 11:24:28
# Size of source mod 2**32: 68115 bytes
"""
@project : face2face_train
<AUTHOR> huyi
@file   : trans_dh_service.py
@ide    : PyCharm
@time   : 2023-12-06 14:47:11
"""
import gc, multiprocessing, os, subprocess, threading, time, traceback,sys
from enum import Enum
from multiprocessing import Process, set_start_method
from queue import Empty, Full,Queue
import cv2, librosa, numpy as np, torch
import json
from cv2box import CVImage
from cv2box.cv_gears import Linker, Queue, CVVideoWriterThread
from face_detect_utils.face_detect import FaceDetect, pfpld
from face_detect_utils.head_pose import Headpose
from face_lib.face_detect_and_align import FaceDetect5Landmarks
from face_lib.face_restore import GFPGAN
from h_utils.custom import CustomError
from h_utils.request_utils import download_file
from h_utils.sweep_bot import sweep
from landmark2face_wy.digitalhuman_interface import DigitalHumanModel
from preprocess_audio_and_3dmm import op
from wenet.compute_ctc_att_bnf import get_weget
from wenet.compute_ctc_att_bnf import load_ppg_model
from y_utils.config import GlobalConfig
from y_utils.logger import logger
from .server import register_host, repost_host
from lt_utils.wo_common import videocap_local_imgs
import glob
from utils.video_utils import get_video_info, read_image, save_image
from utils.flow_utils import locate_exception
from loguru import logger
from wenet.compute_ctc_att_bnf import get_weget, load_ppg_model, WenetStreamer



def feature_extraction_wenet(audio_file, fps, wenet_model, mfccnorm=True, section=560000):
    """
    音频特征提取
    10s音频：25帧，每帧20ms，一共500帧 *20 = 10000
    30s音频：75帧，每帧20ms，一共1500帧 *20 = 30000
    60s音频：150帧，每帧20ms，一共3000帧 *20 = 60000
    300s音频：750帧，每帧20ms，一共15000帧 *20 = 300000
    600s音频：1500帧，每帧20ms，一共30000帧 *20 = 600000
    也就是五分钟左右的音频查不多560000帧
    """
    rate = 16000
    win_size = 20
    if type(audio_file) == str:
        sig, rate = librosa.load(audio_file, sr=rate, duration=None)
    else:
        sig = audio_file
    time_duration = len(sig) / rate # 音频时长:秒
    cnts = range(int(time_duration * fps)) # 音频帧数
    indexs = []
    f_wenet_all = get_weget(audio_file, wenet_model, section)
    print(f"f_wenet_all.shape:{f_wenet_all.shape}")
    for cnt in cnts:
        c_count = int(cnt / cnts[-1] * (f_wenet_all.shape[0] - 20)) + win_size // 2
        indexs.append(f_wenet_all[(c_count - win_size // 2):c_count + win_size // 2, ...])
    return indexs


def get_aud_feat1(wav_fragment, fps, wenet_model):
    return feature_extraction_wenet(wav_fragment, fps, wenet_model)


def warp_imgs(imgs_data):
    caped_img2 = {idx: {'imgs_data':it,  'idx':idx} for it, idx in zip(imgs_data, range(len(imgs_data)))}
    return caped_img2


def get_one_complete_img(image, mask_B_pre, box_info,image_id):
    # 3. 融合
    image_copy = image.copy()
    y1, y2, x1, x2 = box_info # 裁剪人脸框
    face_box_width,face_box_height = y2 - y1,x2 - x1
    
    try:
        mask_B_pre_resize = cv2.resize(mask_B_pre, (face_box_width, face_box_height))  # 将人脸框resize到图片大小,因为这个目标框是从原图检测出来的。
        img_h, img_w = image_copy.shape[:2]
        
        img_paste_start_row = max(0, x1)
        img_paste_end_row = min(img_h, x2)
        img_paste_start_col = max(0, y1)
        img_paste_end_col = min(img_w, y2)

        src_start_row = 0
        if x1 < 0: src_start_row = -x1
        src_start_col = 0
        if y1 < 0: src_start_col = -y1

        paste_height = img_paste_end_row - img_paste_start_row
        paste_width = img_paste_end_col - img_paste_start_col
        
        src_end_row = src_start_row + paste_height
        src_end_col = src_start_col + paste_width

        if paste_height > 0 and paste_width > 0 and \
            src_end_row <= mask_B_pre_resize.shape[0] and \
            src_end_col <= mask_B_pre_resize.shape[1]:
            
            image_copy[img_paste_start_row:img_paste_end_row, img_paste_start_col:img_paste_end_col] = \
                mask_B_pre_resize[src_start_row:src_end_row, src_start_col:src_end_col]
            image = image_copy
        else:
            logger.warning(f"[Frame {image_id}: Invalid dimensions for pasting face mask or source/target mismatch. PasteH={paste_height}, PasteW={paste_width}. SrcShape={mask_B_pre_resize.shape}. Src Slice=({src_start_row}:{src_end_row}, {src_start_col}:{src_end_col}). Skipping blend.")

    except cv2.error as e_resize:
        logger.error(f"[ Frame {image_id}: cv2.resize error for mask_B_pre: {e_resize}. Skipping blend.")

    return image


def get_complete_imgs(output_img_list, start_index, params, no_face_indices_for_batch, work_id_for_logging):
    t1 = time.time()
    out_shape, output_resize, drivered_imgs_data, Y1_list, Y2_list, X1_list, X2_list = params
    complete_imgs = []

    if not drivered_imgs_data:
        logger.error(f"[{work_id_for_logging}] get_complete_imgs: drivered_imgs_data is empty. Cannot proceed.")
        return complete_imgs
        
    # 假设 output_img_list (processed_raw_face_regions) 应该与 drivered_imgs_data 长度相同
    # 如果不是，这个循环可能会有问题。它应该遍历到 len(drivered_imgs_data)
    # 并且只有在 i 不在 no_face_indices 中时才使用 output_img_list[i]
    # 目前，让我们基于 drivered_imgs_data 的长度进行遍历，这是帧的确定来源

    for i,image in enumerate(drivered_imgs_data):

        if i in no_face_indices_for_batch: # 没有人脸
            complete_imgs.append(image)
            continue

        image = image.copy()
        if i >= len(output_img_list) or output_img_list[i] is None:
            logger.warning(f"[{work_id_for_logging}] Frame {i}: Not in no_face_indices, but no valid raw face region found in output_img_list. Using driver frame.")
            # 'image' remains the driver frame.
        else:
            mask_B_pre = output_img_list[i]
            # Perform blending
            if not (i < len(Y1_list) and i < len(Y2_list) and \
                    i < len(X1_list) and i < len(X2_list)):
                logger.error(f"[{work_id_for_logging}] Frame {i}: Index out of bounds for bounding box lists (Y1,Y2,X1,X2). Skipping blend.")
            else:
                y1, y2, x1, x2 = (Y1_list[i], Y2_list[i], X1_list[i], X2_list[i])

                face_box_width = y2 - y1
                face_box_height = x2 - x1

                if face_box_width > 0 and face_box_height > 0:
                    try:
                        mask_B_pre_resize = cv2.resize(mask_B_pre, (face_box_width, face_box_height))
                    except cv2.error as e_resize:
                        logger.error(f"[{work_id_for_logging}] Frame {i}: cv2.resize error for mask_B_pre: {e_resize}. Skipping blend.")
                        # Continue to final resize with original image
                        image = cv2.resize(image, (out_shape[1] // output_resize, out_shape[0] // output_resize))
                        complete_imgs.append(image)
                        continue

                    img_h, img_w = image.shape[:2]
                    
                    # Define target region on 'image'
                    img_paste_start_row = max(0, x1)
                    img_paste_end_row = min(img_h, x2)
                    img_paste_start_col = max(0, y1)
                    img_paste_end_col = min(img_w, y2)

                    # Define source region from 'mask_B_pre_resize'
                    src_start_row = 0
                    if x1 < 0: src_start_row = -x1
                    
                    src_start_col = 0
                    if y1 < 0: src_start_col = -y1

                    # Calculate the height and width of the actual paste area
                    paste_height = img_paste_end_row - img_paste_start_row
                    paste_width = img_paste_end_col - img_paste_start_col
                    
                    src_end_row = src_start_row + paste_height
                    src_end_col = src_start_col + paste_width

                    if paste_height > 0 and paste_width > 0 and \
                        src_end_row <= mask_B_pre_resize.shape[0] and \
                        src_end_col <= mask_B_pre_resize.shape[1]:
                        
                        image[img_paste_start_row:img_paste_end_row, img_paste_start_col:img_paste_end_col] = \
                            mask_B_pre_resize[src_start_row:src_end_row, src_start_col:src_end_col]
                    else:
                        logger.warning(f"[{work_id_for_logging}] Frame {img_idx_in_batch}: Invalid dimensions for pasting face mask or source/target mismatch. PasteH={paste_height}, PasteW={paste_width}. SrcShape={mask_B_pre_resize.shape}. Src Slice=({src_start_row}:{src_end_row}, {src_start_col}:{src_end_col}). Skipping blend.")
                else:
                    logger.warning(f"[{work_id_for_logging}] Frame {img_idx_in_batch}: Bounding box for face has zero/negative width or height ({face_box_width}x{face_box_height}). Skipping blend.")
        
        # Final resize for the 'image' (whether blended or original driver)
        if out_shape and len(out_shape) >= 2 and output_resize > 0:
            final_target_width = out_shape[1] // output_resize
            final_target_height = out_shape[0] // output_resize
            if final_target_width > 0 and final_target_height > 0:
                image = cv2.resize(image, (final_target_width, final_target_height))
            else:
                logger.warning(f"[{work_id_for_logging}] Frame {img_idx_in_batch}: Skipping final resize due to invalid target dimensions ({final_target_width}x{final_target_height}). Original image shape: {image.shape}")
        else:
            logger.warning(f"[{work_id_for_logging}] Frame {img_idx_in_batch}: Skipping final resize due to invalid out_shape or output_resize. Original image shape: {image.shape}")

        complete_imgs.append(image)

    t2 = time.time()
    logger.info(f"[{work_id_for_logging}] get_complete_imgs (processed {len(complete_imgs)} frames out of {len(drivered_imgs_data)} drivers) 耗时: {t2-t1:.2f}秒")
    return complete_imgs


def get_blend_imgs(batch_size, audio_data, face_data_dict, blend_dynamic, params, digital_human_model, frameId):
    # 该函数现在仅用于执行神经网络推理并返回原始人脸区域。
    # `inference_notraining`使用`face_data_dict`来获取crop_img、crop_lm和`audio_data`。
    # print(f"face_data_dict.keys():{face_data_dict[0].keys()}")
    result_raw_face_regions = []
    actual_batch_size = batch_size # Default to user-provided batch_size

    # Original loop structure from get_blend_imgs:
    # It iterates through audio_data in chunks of batch_size.
    num_batches = len(audio_data) // actual_batch_size
    remaining_samples = len(audio_data) % actual_batch_size

    for idx in range(num_batches + (1 if remaining_samples > 0 else 0)):

        # torch.cuda.empty_cache() # 打开后推理时间会变长,但是显存会降低，在wsl2中增加了20ms

        start_index_in_audio_data = idx * actual_batch_size
        current_process_batch_size = 0

        if idx < num_batches: # Full batch
            current_process_batch_size = actual_batch_size
        elif remaining_samples > 0: # Last partial batch
            current_process_batch_size = remaining_samples
        
        if current_process_batch_size == 0:
            continue

        output_face_regions_list = digital_human_model.inference_notraining(
            audio_data, 
            face_data_dict, 
            current_process_batch_size,  # Number of items to process from audio_data starting at start_index_in_audio_data
            start_index_in_audio_data,   # Offset within the audio_data list passed to this function
            blend_dynamic, 
            params, # Passed through, inference_notraining might use parts of it or its callees.
            frameId)

        result_raw_face_regions.extend(output_face_regions_list)
        
    return result_raw_face_regions



class AudioStreamHandler(threading.Thread):
    """
    从WebSocket等来源处理输入的音频流
    它作为一个守护线程运行，并为消费者提供音频块。
    """
    def __init__(self):
        super().__init__(daemon=True)
        self.audio_queue = Queue()
        self.is_running = True

    def feed_audio(self, audio_chunk):
        """从外部源(如WebSocket)馈送音频数据。"""
        if audio_chunk and self.is_running:
            self.audio_queue.put(audio_chunk)

    def get_chunk(self, block=True, timeout=1):
        """获取一个音频块进行处理。"""
        try:
            return self.audio_queue.get(block=block, timeout=timeout)
        except Empty:
            return None

    def stop(self):
        """停止处理器并发出流结束信号。"""
        if self.is_running:
            self.is_running = False
            self.audio_queue.put(None)  # Sentinel to unblock any waiting get_chunk

    def run(self):
        """线程的运行循环。它会一直活动，直到调用stop()。"""
        while self.is_running:
            time.sleep(0.1)


    class AudioExtractor(object):
        def __init__(self, drivered_queue, audio_stream_queue, batch_size, use_npy):
            self.drivered_queue = drivered_queue
            self.audio_stream_queue = audio_stream_queue
            self.batch_size = batch_size
            self.use_npy = use_npy
            self.init_task()
            # Load model once
            self.wenet_model = load_ppg_model("wenet/examples/aishell/aidata/conf/train_conformer_multi_cn.yaml", "wenet/examples/aishell/aidata/exp/conformer/wenetmodel.pt", "cuda")
            self.wenet_streamer = WenetStreamer(self.wenet_model)
            logger.info("【AudioExtractor】初始化成功!")


        def init_task(self,task_info=None):

            # Task-specific attributes, will be set by process_task
            self.code = None
            self.drivered_path = None
            self.audio_path = None
            self.wh = 0
            self.fps = 25
            self.is_train = '0'
            self.speaker_id = None
            self.pn = True
            self.audio_queue = None
            self.suffix = "jpg"
            self.models_dir = None
            self.img_dir = None
            self.video_info_path = None
            self.width = 0
            self.height = 0
            self.fourcc = 0
            self.total_video_frame = 0
            self._frame_shape_provider = None
            self.cycle_img_idx_list = []
            self.cycle_img_len = 0
            self.current_idx = 0
            self.batch_num = 0
            self.start_time = 0
            self.suffix = "npy" if self.use_npy else "jpg"

            if task_info is not None:
                # Set all task-specific attributes from task_info
                self.code = task_info['code']
                self.drivered_path = task_info['video_url']
                self.audio_path = task_info['audio_url']
                self.wh = task_info['wh']
                self.fps = task_info['fps']
                self.is_train = task_info['is_train']
                self.speaker_id = task_info['speaker_id']
                self.pn = task_info['pn']
                self.width = task_info['width']
                self.height = task_info['height']
                self.fourcc = task_info['fourcc']
                self.total_video_frame = task_info['frame_count']
                self._frame_shape_provider = (self.height, self.width, 3)

        def process_task(self, task_info):
            """
            Initializes the extractor with parameters for a specific task and runs it.
            """
            self.init_task(task_info)
            self.audio_queue = self.audio_stream_queue if task_info['is_stream'] else None
            
            # Initialize path-dependent attributes
            self.models_dir = os.path.join('workspace', 'models', self.speaker_id)
            self.chaofen_frames_dir = os.path.join(self.models_dir, f'{"chaofen_frames_npy" if self.use_npy else "chaofen_frames"}')
            self.drivered_frames_dir = os.path.join(self.models_dir, 'drivered_frames')
            self.img_dir = self.chaofen_frames_dir if os.path.exists(self.chaofen_frames_dir) else self.drivered_frames_dir

            self.video_info_path = os.path.join(self.models_dir, 'video_info.json')
            # get_video_info is called in TransDhTask, here we just use the provided values


            self.cycle_img_idx_list = [i for i in range(1,self.total_video_frame+1)] + [i for i in range(1,self.total_video_frame+1)][::-1]  if self.pn else [i for i in range(1,self.total_video_frame+1)]
            self.cycle_img_len = self.total_video_frame*2 if self.pn else self.total_video_frame

            # Initialize/reset counters and data lists

            self.start_time = time.time()
            self.init_batch_data()
            
            # Now run the processing
            self.run()

        def run(self):
            """
            根据初始化参数，选择合适的处理方式并启动任务。
            """
            error_occurred = False
            try:
                self.start()
                if self.audio_queue is not None:
                    self.run_flow_audio_with_wenet()
                elif self.audio_path is not None:
                    self.run_local_audio()
                else:
                    logger.error(f"[{self.code}] No audio source provided (audio_path or audio_queue).")
                    error_occurred = True
            except Exception as e:
                logger.error(f"[{self.code}] An error occurred during AudioExtractor run: {e}")
                logger.error(traceback.format_exc())
                error_occurred = True
            finally:
                self.finish(error=error_occurred)

        # 本地音频处理
        def run_local_audio(self):  
            self.audio_wenet_feature = get_aud_feat1(self.audio_path, fps=self.fps, wenet_model=self.wenet_model)
            
            while self.current_idx < len(self.audio_wenet_feature):
                self.next_data()
                if len(self.drivered_fnames_list) == self.batch_size:
                    self.send_process_data()
            
            # Send any remaining data
            if self.drivered_fnames_list:
                self.send_process_data()

        def run_flow_audio_with_wenet(self):
            """
            以流式方式处理来自audio_queue的音频以生成驱动特征。
            """
            feature_buffer = []
            win_size = 20
            
            logger.info(f"【AudioExtractor】任务 [{self.code}] 开始从提供的音频队列进行流式音频处理。")

            # --- 主处理循环 ---
            while True:
                # 从跨进程队列中获取音频块
                message = self.audio_queue.get()
                status, chunk = message

                if status == 'start':
                    logger.info(f"【AudioExtractor】任务 [{self.code}] 流式音频处理开始。")
                    continue
                
                elif status == 'finish':  # 收到本任务结束的信号
                    logger.info(f"【AudioExtractor】任务 [{self.code}] 本任务流式音频处理结束。")
                    break
                elif status == 'end':  # 收到本次流结束信号
                    logger.info(f"【AudioExtractor】任务 [{self.code}] 本次流式音频处理结束。")
                    break

                elif status == 'data' and chunk:
                    # 将音频字节转换为numpy数组 
                    audio_segment = np.frombuffer(chunk, dtype=np.int16).astype(np.float32) / 32768.0

                    new_features = self.wenet_streamer.process(audio_segment)
                    if new_features is not None and len(new_features) > 0:
                        feature_buffer.append(new_features)
                        self._process_and_send_frames(feature_buffer, win_size)

            # --- 刷新并完成 ---
            logger.info(f"[ {self.code}] Audio stream finished. Flushing final features.")
            final_features = self.wenet_streamer.flush()
            if final_features is not None and len(final_features) > 0:
                feature_buffer.append(final_features)
                self._process_and_send_frames(feature_buffer, win_size, flush=True)

        def _process_and_send_frames(self, feature_buffer, win_size, flush=False):
            """
            Helper to process available features and send data.
            """
            f_wenet_all = np.concatenate(feature_buffer, axis=0)

            # Map audio feature rate (100Hz) to video frame rate (e.g., 25fps)
            audio_feature_rate = 100
            video_fps = self.fps
            
            # Determine the number of video frames we can generate
            # Leave a margin to ensure the feature window doesn't go out of bounds
            num_possible_video_frames = int(f_wenet_all.shape[0] / (audio_feature_rate / video_fps)) - (win_size // 2)

            while self.current_idx < num_possible_video_frames:
                center_feature_idx = int(self.current_idx * (audio_feature_rate / video_fps))
                
                start = center_feature_idx - win_size // 2
                end = center_feature_idx + win_size // 2

                if start < 0 or end > f_wenet_all.shape[0]:
                    # This check should be redundant due to the margin, but good for safety
                    break 

                self.audio_wenet_feature = f_wenet_all[start:end, ...]
                
                self.next_data()
                if len(self.drivered_fnames_list) == self.batch_size:
                    self.send_process_data()
            
            # On the final flush, send any remaining data that's been processed but not sent
            if flush and self.drivered_fnames_list:
                self.send_process_data()

        def finish(self, error=False):
            """Signal the end of processing."""
            state = "error" if error else "finish"
            self.drivered_queue.put([state, False, "任务完成", self.code])
            logger.info(f"[{self.code}] Task finished in {time.time() - self.start_time:.2f}s")
            
            
        def run_flow_audio(self):
            """
            根据音频流驱动，消费kafka中的音频
            """
            self.audio_reader.set_consumer(self.code, self.audio_path)
            last_get_time = time.time()
            no_data_time = 0
            while True:
                wav_fragment = self.audio_reader.get_fragment()
                if wav_fragment is not None:
                    if len(wav_fragment) > 0:
                        self.audio_wenet_feature = get_aud_feat1(wav_fragment,self.fps,self.wenet_model)
                        while self.current_idx < len(self.audio_wenet_feature):
                            self.next_data()
                            if self.current_idx % self.batch_size == 0 or self.current_idx == len(self.audio_wenet_feature):
                                self.send_process_data()
                            if self.current_idx == len(self.audio_wenet_feature):
                                self.finish()
                        no_data_time = 0
                    else:
                        no_data_time = time.time() - last_get_time
                        if no_data_time > 10:
                            self.finish()
                            break
                else:
                    time.sleep(0.01)
                    no_data_time = time.time() - last_get_time
                    if no_data_time > 10:
                        self.finish()
                        break
            pass
            
        
        # 清空batch的数据
        def init_batch_data(self):
            self.drivered_fnames_list = [] # Changed from drivered_list
            self.wenet_feature_list = []
            self.batch_img_idx_list =[]  # 视频帧索引,有可能是倒叙的，
            self.batch_aud_idx_list =[]  # 音频帧索引，只会是正序
        
        # 发送处理数据
        def send_process_data(self):
            self.state = "processing"
            self.drivered_queue.put([self.state,self.drivered_fnames_list, self.wenet_feature_list,self.batch_img_idx_list,self.batch_aud_idx_list], block=True, timeout=60)

            self.batch_num += 1
            if self.batch_num % 10 == 0:
                logger.info("【AudioExtractor】 【processing】>>>>>>>>>>>>>>>>>>>> 发送数据大小:[第{} :{}]".format(self.batch_num,len(self.drivered_fnames_list)))
            self.init_batch_data()
        
        # 发送初始化参数
        def start(self):
            # 发送初始化参数
            self.state = "start"
            self.drivered_queue.put([self.state,self.code,self.wh,self.speaker_id,self.is_train,self.use_npy,self.total_video_frame, self._frame_shape_provider], block=True, timeout=60)
            logger.info(f"【AudioExtractor】 【start】>>>>>>>>>>>>>>>>>>>> 发送固定参数 (含形状: {self._frame_shape_provider})")

        # 发送结束参数
        def finish(self, error=False):
            self.state = "finish" if not error else "error"
            logger.info(f"【AudioExtractor】 【{self.state}】>>>>>>>>>>>>>>>>>>>> 发送数据结束,耗时：{time.time()-self.start_time}s")
            self.drivered_queue.put([self.state,True if not error else False, "success" if not error else "error", self.code])
        
        def error(self):    
            pass

        # 获取下一帧参数
        def next_data(self):
            self.wenet_feature_list.append(self.audio_wenet_feature[self.current_idx])
            self.current_idx += 1
            img_idx = self.cycle_img_idx_list[self.current_idx % self.cycle_img_len]

            self.batch_img_idx_list.append(img_idx)  # 从1开始
            self.batch_aud_idx_list.append(self.current_idx)
            f_file_name = os.path.join(self.img_dir, f"frame_{img_idx:06d}.{self.suffix}")
            self.drivered_fnames_list.append(f_file_name) # Changed




def audio_processor(task_queue, drivered_queue, audio_stream_queue, batch_size=8, use_npy=False):
    """
    音频处理进程的主函数，通过队列接收任务。
    """
    # 1. Instantiate the extractor ONCE
    audio_extractor = AudioExtractor(
        drivered_queue=drivered_queue,
        audio_stream_queue=audio_stream_queue,
        batch_size=batch_size,
        use_npy=use_npy
    )
    
    task_info = {}
    while True:
        try:
            # 2. Wait for a new task
            task_info = task_queue.get()
            logger.info(f"【AudioExtractor】接收到任务 [{task_info.get('code')}] 开始处理。")
            
            # 3. Process the task
            audio_extractor.process_task(task_info)
            logger.info(f"【AudioExtractor】任务 [{task_info.get('code')}] 处理完成。")
            
        except Exception as e:
            logger.error(f"【AudioExtractor】任务 [{task_info.get('code', 'unknown')}] 处理失败: {str(e)}")
            logger.error(traceback.format_exc())
            # Notify downstream about the error
            if task_info:
                drivered_queue.put(["error", False, f"【AudioExtractor】任务 [{task_info.get('code', 'unknown')}] 处理失败: {str(e)}", task_info.get('code', 'unknown')])


def get_face_mask(mask_shape=(512, 512)):
    mask = np.zeros((512, 512)).astype(np.float32)
    cv2.ellipse(mask, (256, 256), (220, 160), 90, 0, 360, (255, 255, 255), -1)
    thres = 20
    mask[:thres, :] = 0
    mask[-thres:, :] = 0
    mask[:, :thres] = 0
    mask[:, -thres:] = 0
    mask = cv2.stackBlur(mask, (201, 201))
    mask = mask / 255
    mask = cv2.resize(mask, mask_shape)
    return mask[..., np.newaxis]


def get_single_face(bboxes, kpss, image, crop_size, mode='mtcnn_512', apply_roi=True):
    from face_lib.face_detect_and_align.face_align_utils import apply_roi_func, norm_crop
    assert mode in ('default', 'mtcnn_512', 'mtcnn_256', 'arcface_512', 'arcface', 'default_95')
    if bboxes.shape[0] == 0:
        return (None, None)
    det_score = bboxes[..., 4]
    best_index = np.argmax(det_score)
    new_kpss = None
    if kpss is not None:
        new_kpss = kpss[best_index]
    if apply_roi:
        roi, roi_box, roi_kpss = apply_roi_func(image, bboxes[best_index], new_kpss)
        align_img, mat_rev = norm_crop(roi, roi_kpss, crop_size, mode=mode)
        return (align_img, mat_rev, roi_box)
    align_img, M = norm_crop(image, new_kpss, crop_size, mode=mode)
    return (align_img, M)


face_mask = get_face_mask()
need_chaofen_flag = False
get_firstface_frame = False
def locate_exception():
    """
    locate error filename object line
    """
    exc_type, exc_obj, exc_tb = sys.exc_info()
    fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
    return "{},{},{}".format(exc_type, fname, exc_tb.tb_lineno)

#todo 检查
def chaofen_src(frame_list, gfpgan, fd, frame_id, face_blur_detect, code):
    global get_firstface_frame
    global need_chaofen_flag
    s_chao = time.time()
    if frame_id == 4: # 这里做了修改
        chaofen_flag = get_firstface_frame or False
        firstface_frame = False
        for frame in frame_list:
            if frame.shape[0] >= 3840 or frame.shape[1] >= 3840:
                chaofen_flag = False
                firstface_frame = True
                logger.info("[%s] -> video frame shape is 4k, skip chaofen")
                break

            else:
                bboxes_scrfd, kpss_scrfd = fd.get_bboxes(frame)
                if len(bboxes_scrfd) == 0:
                    continue
             
                face_image_, mat_rev_, roi_box_ = get_single_face(bboxes_scrfd, kpss_scrfd, frame, crop_size=512, mode="mtcnn_512",apply_roi=True)
                face_attr_res = face_blur_detect.forward(face_image_)
                blur_threshold = face_attr_res[0][-2]
                logger.info("[%s] -> frame_id:[%s] 模糊置信度:[%s]", code, frame_id, blur_threshold)
                if blur_threshold > GlobalConfig.instance().blur_threshold:
                    logger.info("[%s] -> need chaofen .", code)
                    chaofen_flag = True
                else:
                    chaofen_flag = False
                firstface_frame = True

                
            need_chaofen_flag = chaofen_flag
            get_firstface_frame = firstface_frame
            break

    if not need_chaofen_flag:
        return frame_list,need_chaofen_flag
    new_frame_list = []
    for i in range(len(frame_list)):
        frame = frame_list[i]
        bboxes_scrfd, kpss_scrfd = fd.get_bboxes(frame)
        if len(bboxes_scrfd) == 0:
            new_frame_list.append(frame)
            continue

        face_image_, mat_rev_, roi_box_ = get_single_face(bboxes_scrfd, kpss_scrfd, frame, crop_size=512, mode="mtcnn_512",apply_roi=True)
        face_restore_out_ = gfpgan.forward(face_image_)
        restore_roi = CVImage.recover_from_reverse_matrix(face_restore_out_, (frame[roi_box_[1]:roi_box_[3],roi_box_[0]:roi_box_[2]]),mat_rev_,img_fg_mask=face_mask)
        frame[roi_box_[1]:roi_box_[3], roi_box_[0]:roi_box_[2]] = restore_roi
        new_frame_list.append(frame)

    torch.cuda.empty_cache()
    logger.info("[] -> chaofen  cost:{:.3f}s" .format(time.time() - s_chao))
    return new_frame_list,need_chaofen_flag



def _get_chaofen_src_imgs(code,is_train,img_list, digital_human_model, scrfd_detector, frameId,batch_img_idx_list , batch_aud_idx_list, speaker_id,total_video_frame,use_npy=False):
    """
    本批次超分处理
    todo: 这个函数需要优化，将代码合并一下，有点重复
    """
    # chaofen_frames_dir = os.path.join('workspace', 'models', speaker_id, 'chaofen_frames')
    chaofen_frames_dir = os.path.join('workspace', 'models', speaker_id, '{}'.format('chaofen_frames_npy' if use_npy else 'chaofen_frames'))
    drivered_frames_dir = os.path.join('workspace', 'models', speaker_id, 'drivered_frames')
    suffix = "npy" if use_npy else "jpg"

    if is_train == '1':
        # 创建帧数据保存目录
        os.makedirs(chaofen_frames_dir, exist_ok=True)
        video_is_unfinish = False
        if min(batch_aud_idx_list) <= total_video_frame:
            video_is_unfinish = True
     
        if video_is_unfinish: # 只有第一次循环的时候需要保存超分的图片
            img_list,need_chaofen_flag = chaofen_src(img_list, digital_human_model.gfpgan, scrfd_detector, frameId, digital_human_model.face_attr, code)   # 1.超分处理  
            # logger.info(f"[use_npy: {use_npy}")

            # 保存所有帧的超分处理后图片
            for img,frame_number in zip(img_list,batch_img_idx_list):
                frame_path = os.path.join(chaofen_frames_dir, f'frame_{frame_number:06d}.{suffix}')
                cv2.imwrite(frame_path, img) if not use_npy else np.save(frame_path, img)
            # logger.info(f"[保存第{frameId}帧超分图像文件, frame_number: {frameId}")
        else: # 视频已经完成
            # 尝试从已有的chaofen_frames目录加载图像,注意，不是所有的帧都有人像
            new_img_list = []
            for src_img,frame_number in zip(img_list,batch_img_idx_list):
                frame_path = os.path.join(chaofen_frames_dir, f'frame_{frame_number:06d}{suffix}')
                if os.path.exists(frame_path):
                    img = cv2.imread(frame_path) if not use_npy else np.load(frame_path)
                    new_img_list.append(img)
                else:
                    new_img_list.append(src_img)  # 有些图片没有人脸，直接添加原图,或者没有超分（分辨率比较大）
                
    elif os.path.exists(drivered_frames_dir) or os.path.exists(chaofen_frames_dir):
        # logger.info(f"推理模式")
        return img_list  # 推理模式直接在drivered_video_pn中读取超分图片
    else:
        img_list,need_chaofen_flag = chaofen_src(img_list, digital_human_model.gfpgan, scrfd_detector, frameId, digital_human_model.face_attr, code)
    return img_list

def _get_drivered_face_dict(history_data,save_data_dir,batch_img_idx_list):
    # todo: crop_img 兼容npy 和 jpg
    # 训练模式：保存关键点和人脸框信息为npy文件
    face_data_dir = os.path.join(save_data_dir, 'face_data')  # 创建face_data目录保存人脸关键数据
    os.makedirs(face_data_dir, exist_ok=True)
    crop_img_dir = os.path.join(face_data_dir, 'crop_img')   # 创建crop_img目录保存裁剪的人脸图像
    wh_file = os.path.join(save_data_dir, 'wh_value.txt')
    os.makedirs(crop_img_dir, exist_ok=True)

    # 非训练模式：尝试从已保存的数据加载并使用
    if os.path.exists(wh_file):
        drivered_face_dict = {}
        no_face_indices = []
        if len(history_data) > 0: # 从文件加载历史数据
            for i,img_idx in enumerate(batch_img_idx_list):
                drivered_face_dict[i] = history_data.get('{}'.format(img_idx),{})
                
                for k in ['bounding_box','bounding_box_p','landmarks','crop_lm']:
                    if k in drivered_face_dict[i] and isinstance(drivered_face_dict[i][k], list):
                        drivered_face_dict[i][k] = np.array(drivered_face_dict[i][k])
                if drivered_face_dict[i].get('no_face',True):
                    no_face_indices.append(i)
 
                frame_path = os.path.join(face_data_dir, 'crop_img', f'frame_{img_idx:06d}.jpg')
                if os.path.exists(frame_path):
                    img = cv2.imread(frame_path)
                    drivered_face_dict[i]['crop_img'] = img
                else:
                    drivered_face_dict[i]['crop_img'] = None
        else:
            no_face_indices = [0,1,2,3]

            
    return drivered_face_dict,no_face_indices

def _get_face_params_from_drivered_face_dict(drivered_face_dict,out_shape, output_resize):
    x1_list, x2_list, y1_list, y2_list = ([], [], [], [])
    get_face_params_time1 = time.time()
    for idx in range(len(drivered_face_dict)):
        facebox = drivered_face_dict[idx]['bounding_box']
        x1_list.append(facebox[0])
        x2_list.append(facebox[1])
        y1_list.append(facebox[2])
        y2_list.append(facebox[3])
    drivered_exceptlist = []
    get_face_params_time2 = time.time()
    frame_len = len(drivered_face_dict.keys())
    for i in range(frame_len):
        if len(drivered_face_dict[i]['bounding_box_p']) == 4:
            break
        drivered_exceptlist.append(i)
        print(drivered_exceptlist, '-------------------------------------')
    get_face_params_time3 = time.time()
    for i in drivered_exceptlist:
        drivered_face_dict[i]['bounding_box_p'] = drivered_face_dict[len(drivered_exceptlist)]['bounding_box_p']
        drivered_face_dict[i]['bounding_box'] = drivered_face_dict[len(drivered_exceptlist)]['bounding_box']
        drivered_face_dict[i]['crop_lm'] = drivered_face_dict[len(drivered_exceptlist)]['crop_lm']
        drivered_face_dict[i]['crop_img'] = drivered_face_dict[len(drivered_exceptlist)]['crop_img']
    get_face_params_time4 = time.time()
    keylist = list(drivered_face_dict.keys())
    keylist.sort()
    get_face_params_time5 = time.time()
    for it in keylist:
        if len(drivered_face_dict[it]['bounding_box_p']) != 4:
            # print(it, '++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++')
            drivered_face_dict[it]['bounding_box_p'] = drivered_face_dict[it - 1]['bounding_box_p']
            drivered_face_dict[it]['bounding_box'] = drivered_face_dict[it - 1]['bounding_box']
            drivered_face_dict[it]['crop_lm'] = drivered_face_dict[it - 1]['crop_lm']
            drivered_face_dict[it]['crop_img'] = drivered_face_dict[it - 1]['crop_img']
    # get_face_params_time6 = time.time()
    face_params = [out_shape, output_resize,  y1_list, y2_list, x1_list, x2_list]
    # get_face_params_time7 = time.time()
    # logger.info(f"获取人脸参数耗时: 7-6：{get_face_params_time7 - get_face_params_time6:.6f}s，6-5：{get_face_params_time6 - get_face_params_time5:.6f}s，5-4：{get_face_params_time5 - get_face_params_time4:.6f}s，4-3：{get_face_params_time4 - get_face_params_time3:.6f}s，3-2：{get_face_params_time3 - get_face_params_time2:.6f}s，2-1：{get_face_params_time2 - get_face_params_time1:.6f}s")
    return face_params

def get_silence_blend_imgs(silence_path,img_name_list,use_npy=False): # 直接读取静音数据
    # 读取静音数据t
    is_find,blend_imgs = True,None
    silence_imgs_list = os.listdir(silence_path) # todo 可以不用每次遍历，后面想想办法优化

    for name in img_name_list:
        if not  name in silence_imgs_list:
            is_find = False
            break
    # 读取静音数据
    if is_find:
        blend_imgs = [np.load(os.path.join(silence_path,name)) for name in img_name_list] if use_npy else [cv2.imread(os.path.join(silence_path,name)) for name in img_name_list]
    return is_find,blend_imgs

class DhStreamProcessor(object):
    def __init__(self,drivered_queue, output_imgs_queue, batch_size=8,use_npy=False):
        self.drivered_queue = drivered_queue
        self.output_imgs_queue = output_imgs_queue
        self.batch_size = batch_size
        self.batch_num = 0
        self.use_npy = use_npy
        self.init()
        self.digital_human_model = DigitalHumanModel(GlobalConfig.instance().blend_dynamic, GlobalConfig.instance().chaofen_before, face_blur_detect=False,half=False)

        self.face_data_dir = os.path.join('workspace', 'models', self.history_data.get('speaker_id', "nofind"))
        self.get_msg_time = []
        self.infer_time = []
        self.tlast = time.time()
    def run(self):
        while True:
            try:
                t_get1 = time.time()
                queue_values = self.drivered_queue.get()
                t_get2 = time.time()
                self.get_msg_time.append(t_get2 - t_get1)
                state = queue_values[0]

                if state == "start":
                    self.start(queue_values)

                elif state == "finish" or state == "error": #结束信号 e.g. [True, "success", code]
                    self.finish(queue_values)

                else: # processing or silence?
                    self.next_data(queue_values)

            except Empty:  # 空数据
                logger.info(f"【audio_transfer】 等待数据 {self.code if self.code else 'Unknown'} 可能已经完成或等待.")
                time.sleep(0.1)
                continue
            except Exception as e:
                logger.error(f"【audio_transfer】 处理失败 {self.code if self.code else 'Unknown'}: {str(e)}")
                print(traceback.format_exc()) # 打印完整堆栈跟踪
                # Send error to output queue if a task code is known
                if self.code:
                    self.output_imgs_queue.put([False, f'数字人处理失败，任务{self.code}，原因:[{e.__str__()}]', self.code])
                else: # Generic error if no task context
                    self.output_imgs_queue.put([False, f'数字人处理失败，未知任务，原因:[{e.__str__()}]', ''])
                time.sleep(1)
                torch.cuda.empty_cache()
                continue

    def start(self,queue_values):
        self.state,self.code,self.wh,self.speaker_id,self.is_train_str,self.use_npy_flag,self.total_video_frame,self.original_frame_shape_init = queue_values

        logger.info(f"audio_transfer 开始处理视频: {self.code}, wh: {self.wh}, speaker_id: {self.speaker_id}, is_train: {self.is_train_str}, use_npy: {self.use_npy_flag}, total_video_frame: {self.total_video_frame}, shape_init: {self.original_frame_shape_init}")
        # 初始化wh
        if self.wh == 0 or self.wh == -1:
            self.wh = self.digital_human_model.drivered_wh
        # 如果 speaker_id 更改或是一个新的 code，则为新任务初始化/重置 history_data
        if self.speaker_id != self.history_data['speaker_id'] or not self.history_data: # if speaker changes or history is empty
            self.history_data = self._init_history_data.copy()
            self.face_data_dir = os.path.join('workspace', 'models', self.speaker_id) # 更新 face_data_dir
            self.history_data_path = os.path.join(self.face_data_dir, 'face_data', 'face_data.json')
            self.history_data['speaker_id'] = self.speaker_id

        if os.path.exists(self.history_data_path): # 如果存在历史数据文件，则加载历史数据
            logger.info(f"加载历史数据: {self.history_data_path}")
            try:
                with open(self.history_data_path, 'r') as f:
                    loaded_history = json.load(f)
                    # Basic validation before assigning
                    if isinstance(loaded_history, dict) and loaded_history.get('speaker_id') == self.speaker_id:
                        self.history_data = loaded_history
                        logger.info(f"成功加载历史数据: {self.speaker_id}")
                    else:
                        logger.warning(f"加载历史数据失败: {self.history_data_path}, 使用新历史数据.")
            except Exception as e_load: # todo 需要处理,找不到就返回失败
                logger.error(f"Error loading history data: {e_load}, using new history.")
                self.history_data['speaker_id'] = self.speaker_id # Ensure speaker_id is set
        else:
            logger.info(f"No existing history data found for {self.speaker_id} or not in training load mode, using new history.")
            self.history_data['speaker_id'] = self.speaker_id

    
    def finish(self,queue_values):
        try:
            # 计算平均耗时
            mean_get_msg_time = np.mean(np.array(self.get_msg_time[1:]))*1000 if len(self.get_msg_time) > 1 else 0
            mean_infer_time = np.mean(np.array(self.infer_time[1:]))*1000 if len(self.infer_time) > 1 else 0
            logger.info(f"【计算帧数】: {len(self.get_msg_time)} 【获取数据平均耗时】: {mean_get_msg_time:.2f}ms, 【推理平均耗时】: {mean_infer_time:.2f} ms, 【FPS】:{1000/max(1e-6,mean_infer_time+mean_get_msg_time)}")
            state, end_signal_status, end_signal_msg, end_code_val = queue_values # 结束信号
            logger.info(f'>>> audio_transfer get end msg: {end_signal_status}, {end_signal_msg} for code {end_code_val}')
            self.output_imgs_queue.put([end_signal_status, end_signal_msg, end_code_val]) # 传递结束信号
            torch.cuda.empty_cache() # 清空缓存
            # 这个结束信号是 drivered_video_pn 发送的，此时 audio_transfer 应该也发送结束信号给下游
            if self.code == end_code_val: # 重新初始化
                logger.info(f"任务 {self.code} 结束, audio_transfer 清理并等待新的初始化参数.")
                # Reset task-specific vars for next potential task if this process is reused
                self.face_data_dir = os.path.join('workspace', 'models', self.history_data.get('speaker_id', "nofind"))

            self.init()
            self.get_msg_time = []
            self.infer_time = []
        except Exception as e:
            pass

    def error(self):
        pass
    def silence(self):
        pass
    def next_data(self,queue_values):
        t_get1 = time.time()
        state,drivered_fnames_list, audio_feature_list,batch_img_idx_list,batch_aud_idx_list = queue_values
        audio_feature_list = [np.array(w,dtype=np.float32) for w in audio_feature_list]  # 将音频特征列表转换回 numpy 数组
        frameId = batch_aud_idx_list[-1] if len(batch_aud_idx_list)>0 else -1
        img_list = None 
        t_get2 = time.time()
        logger.info(f"【audio转numpy】: {(t_get2 - t_get1)*1000:.3f}ms")


        ####################################保存/读取人脸数据####################################################
        drivered_face_dict,no_face_indices = _get_drivered_face_dict(self.history_data,self.face_data_dir,batch_img_idx_list)
        # 如果  img_list 为 None。这将由后续加载帧的 write_video 函数处理。
        face_params = _get_face_params_from_drivered_face_dict(drivered_face_dict,self.original_frame_shape_init, self.output_resize) 
        t_get3 = time.time()
        logger.info(f"【获取人脸数据】: {(t_get3 - t_get2)*1000:.3f}ms")
        ################################## 开始推理结果####################################################
        t6 = time.time()
        silence_find = False  # TODO:兼容静音模式
        # if state == "silence": # 优选读取静音数据
        #     silence_find,processed_raw_face_regions = get_silence_blend_imgs(silence_path,img_name_list,use_npy)
        if not silence_find: # 正常推理
            processed_raw_face_regions = get_blend_imgs(
                self.batch_size, 
                audio_feature_list, 
                drivered_face_dict, 
                GlobalConfig.instance().blend_dynamic, 
                face_params,
                self.digital_human_model, 
                frameId) # 仅返回来自神经网络的原始人脸区域

            # TODO:写入静音数据
            # if not silence_find:
            #     # 写入静音数据
            #     pass
        ################################## 发送数据 ####################################################
        # 写入视频所需数据: 文件名列表、拼接参数、原始人脸区域、批次中无人脸的索引、是否使用npy格式
        t_get4 = time.time()
        logger.info(f"【推理】: {(t_get4 - t_get3)*1000:.3f}ms")
        output_package = (drivered_fnames_list, face_params, processed_raw_face_regions, no_face_indices, self.use_npy_flag)
        self.output_imgs_queue.put([0, 0, output_package]) # status_code, reason_placeholder, data_tuple
        t_get5 = time.time()
        logger.info(f"【发送数据】: {(t_get5 - t_get4)*1000:.3f}ms")
        real_fps = 1/(time.time() - self.tlast) 
        self.tlast = time.time()
        logger.info(f"【real FPS】:{real_fps:.2f}")

        self.infer_time.append(time.time() - t_get1)
        self.batch_num += 1
        if self.batch_num %5 == 0:
            logger.info(f'>>> audio_transfer 第 {self.batch_num} 批数据推理{len(drivered_fnames_list)}帧, 耗时 {time.time() - t_get1:.3f}s, FPS:{len(drivered_fnames_list)/(max(1e-6,time.time() - t_get1)):.2f}')


    def init(self):
        logger.info('>>> 数字人图片处理进程启动')
        self.output_resize = 1
        self.scrfd_detector,self.scrfd_predictor,self.hp = None,None,None

        self._init_history_data = {
            'bounding_box_list': [],
            'bounding_box_p_list': [],
            'landmarks_list': [],
            'crop_lm_list': [],
            'no_face_indices': [],
            'speaker_id': "nofind"
    }
        self.history_data = self._init_history_data.copy()  # 整个视频的结果

    # 存储固定参数, 匹配 drivered_video_pn 发送的7个参数
        self.code,self.wh,self.speaker_id,self.is_train_str,self.use_npy_flag,self.total_video_frame,self.original_frame_shape_init = None,0,"nofind","0",False,-1,None 


def dh_stream_processor(drivered_queue, output_imgs_queue, batch_size=8,use_npy=False):
    dh_stream_process = DhStreamProcessor(drivered_queue, output_imgs_queue, batch_size,use_npy)
    dh_stream_process.run()


# 合成视频处理
def write_video(output_imgs_queue, temp_dir, result_dir, work_id, audio_path, result_queue, width, height, fps, watermark_switch=0, digital_auth=0,use_npy=False):
    output_mp4 = os.path.join(temp_dir, "{}-t.mp4".format(work_id))
    fourcc = (cv2.VideoWriter_fourcc)(*"mp4v")
    result_path = os.path.join(result_dir, "{}-r.mp4".format(work_id))
    video_write = cv2.VideoWriter(output_mp4, fourcc, fps, (
     width, height))
    
    try:
        while True:
            state, reason, value_ = output_imgs_queue.get()
            logger.info("write_video 接收到数据")
            if type(state) == bool and state == True:
                logger.info("[{}]视频帧队列处理已结束".format(work_id))
                break
            elif type(state) == bool and  state == False:
                    logger.error("[{}]任务视频帧队列 -> 异常原因:[{}]".format(work_id, reason))
                    raise CustomError(reason)
                    break
            # If state is not bool, it means we have data.
            # Here, state is 0, reason is 0, value_ is output_package.
            # output_package = (drivered_fnames_list, face_params, processed_raw_face_regions, no_face_indices, use_npy_flag)
            
            drivered_fnames_list, face_params_tuple, processed_raw_face_regions, no_face_indices, use_npy_flag = value_

            # Unpack face_params_tuple for this batch
            out_shape, output_resize, y1_list, y2_list, x1_list, x2_list = face_params_tuple

            t_start_batch = time.time()
            for i, fname in enumerate(drivered_fnames_list):
                # 1. 加载图片
                try:
                    assert os.path.exists(fname), f"[{work_id}] Driver image file does not exist: {fname}"
                    image = np.load(fname) if use_npy_flag else cv2.imread(fname)
                    if image is None:
                        raise CustomError(f"Failed to load image or image is empty: {fname}")
                except Exception as e:
                    logger.error(f"[{work_id}] Exception during loading driver image {fname}: {str(e)}")
                    raise CustomError(f"[{work_id}] Exception loading driver image {fname}: {str(e)}")

                # 2. 处理图片
                if i in no_face_indices: # 没有人脸，直接写入
                    pass 
                else:  # 有人脸，进行融合
                    image = get_one_complete_img(image, processed_raw_face_regions[i], (y1_list[i], y2_list[i], x1_list[i], x2_list[i]), os.path.basename(fname))


                # Final resize for the 'image' (whether blended or original driver)
                if out_shape and len(out_shape) >= 2 and output_resize > 0:
                    final_target_width = out_shape[1] // output_resize
                    final_target_height = out_shape[0] // output_resize
                    if final_target_width > 0 and final_target_height > 0:
                        image = cv2.resize(image, (final_target_width, final_target_height))
                    else:
                        logger.warning(f"[{work_id}] Frame {i}: Skipping final resize due to invalid target dimensions ({final_target_width}x{final_target_height}). Original image shape: {image.shape}")
                else:
                    logger.warning(f"[{work_id}] Frame {i}: Skipping final resize due to invalid out_shape or output_resize. Original image shape: {image.shape}")

                video_write.write(image)
            
            logger.info(f"[{work_id}] 写入 {len(drivered_fnames_list)} 帧图片，耗时 {time.time() - t_start_batch:.3f}s, FPS:{len(drivered_fnames_list)/(max(1e-6,time.time() - t_start_batch))}")

        video_write.release()
        if watermark_switch == 1:
            if digital_auth == 1:
                logger.info("[{}]任务需要水印和数字人标识".format(work_id))
                if width > height:
                    command = 'ffmpeg -y -i {} -i {} -i {} -i {} -filter_complex "overlay=(main_w-overlay_w)-10:(main_h-overlay_h)-10,overlay=(main_w-overlay_w)-10:10" -c:a aac -crf 15 -strict -2 {}'.format(audio_path, output_mp4, GlobalConfig.instance().watermark_path, GlobalConfig.instance().digital_auth_path, result_path)
                    logger.info("command:{}".format(command))
                else:
                    command = 'ffmpeg -y -i {} -i {} -i {} -i {} -filter_complex "overlay=(main_w-overlay_w)-10:(main_h-overlay_h)-10,overlay=(main_w-overlay_w)-10:10" -c:a aac -crf 15 -strict -2 {}'.format(audio_path, output_mp4, GlobalConfig.instance().watermark_path, GlobalConfig.instance().digital_auth_path, result_path)
                    logger.info("command:{}".format(command))
            else:
                logger.info("[{}]任务需要水印".format(work_id))
                command = 'ffmpeg -y -i {} -i {} -i {} -filter_complex "overlay=(main_w-overlay_w)-10:(main_h-overlay_h)-10" -c:a aac -crf 15 -strict -2 {}'.format(audio_path, output_mp4, GlobalConfig.instance().watermark_path, result_path)
                logger.info("command:{}".format(command))
        elif watermark_switch == 0:
            if digital_auth == 1:
                logger.info("[{}]任务需要数字人标识".format(work_id))
                if width > height:
                    command = 'ffmpeg -loglevel warning -y -i {} -i {} -i {} -filter_complex "overlay=(main_w-overlay_w)-10:10" -c:a aac -crf 15 -strict -2 {}'.format(audio_path, output_mp4, GlobalConfig.instance().digital_auth_path, result_path)
                    logger.info("command:{}".format(command))
                else:
                    command = 'ffmpeg -loglevel warning -y -i {} -i {} -i {} -filter_complex "overlay=(main_w-overlay_w)-10:10" -c:a aac -crf 15 -strict -2 {}'.format(audio_path, output_mp4, GlobalConfig.instance().digital_auth_path, result_path)
                    logger.info("command:{}".format(command))
            else:
                command = "ffmpeg -loglevel warning -y -i {} -i {} -c:a aac -c:v libx264 -crf 15 -strict -2 {}".format(audio_path, output_mp4, result_path)
                logger.info("command:{}".format(command))
        subprocess.call(command, shell=True)
        print("###### write over")
        result_queue.put([True, result_path])

    except Exception as e:
        try:
            logger.error("[{}]视频帧队列处理异常结束，异常原因:[{}]".format(work_id, e.__str__()))
            result_queue.put([False, "[{}]视频帧队列处理异常结束，异常原因:[{}]".format(work_id, e.__str__())])
        finally:
            e = None
            del e

    logger.info("后处理进程结束")


def save_video_ffmpeg(input_video_path, output_video_path):
    audio_file_path = input_video_path.replace(".mp4", ".aac")
    if not os.path.exists(audio_file_path):
        os.system('ffmpeg -y -hide_banner -loglevel error -i "' + str(input_video_path) + '" -f wav -vn  "' + str(audio_file_path) + '"')
    if os.path.exists(audio_file_path):
        os.rename(output_video_path, output_video_path.replace(".mp4", "_no_audio.mp4"))
        start = time.time()
        os.system('ffmpeg -y -hide_banner -loglevel error  -i "' + str(output_video_path.replace(".mp4", "_no_audio.mp4")) + '" -i "' + str(audio_file_path) + '" -c:v libx264 "' + str(output_video_path) + '"')
        print("add audio time cost", time.time() - start)
        os.remove(output_video_path.replace(".mp4", "_no_audio.mp4"))
        os.remove(audio_file_path)
    return output_video_path


class FaceDetectThread(Linker):

    def __init__(self, queue_list):
        super().__init__(queue_list, fps_counter=True)
        self.fd = FaceDetect5Landmarks(mode="scrfd_500m")

    def forward_func(self, something_in):
        frame = something_in
        bboxes_scrfd, kpss_scrfd = self.fd.get_bboxes(frame, min_bbox_size=64)
        if len(bboxes_scrfd) == 0:
            return [
             frame, None, None, None]
        face_image_, mat_rev_, roi_box_ = self.fd.get_single_face(crop_size=512, mode="mtcnn_512", apply_roi=True)
        return [frame, face_image_, mat_rev_, roi_box_]


class FaceRestoreThread(Linker):

    def __init__(self, queue_list):
        super().__init__(queue_list, fps_counter=True)
        self.gfp = GFPGAN(model_type="GFPGANv1.4", provider="gpu")

    def forward_func(self, something_in):
        src_face_image_ = something_in[1]
        if src_face_image_ is None:
            return [
             None] + something_in
        face_restore_out_ = self.gfp.forward(src_face_image_)
        torch.cuda.empty_cache()
        return [face_restore_out_] + something_in


class FaceParseThread(Linker):

    def __init__(self, queue_list):
        super().__init__(queue_list, fps_counter=True)
        self.face_mask_ = self.get_face_mask(mask_shape=(512, 512))

    def get_face_mask(self, mask_shape):
        mask = np.zeros((512, 512)).astype(np.float32)
        cv2.ellipse(mask, (256, 256), (220, 160), 90, 0, 360, (255, 255, 255), -1)
        thres = 20
        mask[:thres, :] = 0
        mask[-thres:, :] = 0
        mask[:, :thres] = 0
        mask[:, -thres:] = 0
        mask = cv2.stackBlur(mask, (201, 201))
        mask = mask / 255.0
        mask = cv2.resize(mask, mask_shape)
        return mask[..., np.newaxis]

    def forward_func(self, something_in):
        if something_in[0] is None:
            return something_in + [None]
        return something_in + [self.face_mask_]


class FaceReverseThread(Linker):

    def __init__(self, queue_list):
        super().__init__(queue_list, fps_counter=True)
        self.counter = 0
        self.start_time = time.time()

    def forward_func(self, something_in):
        face_restore_out = something_in[0]
        src_img_in = something_in[1]
        if face_restore_out is not None:
            mat_rev = something_in[3]
            roi_box = something_in[4]
            face_mask_ = something_in[5]
            restore_roi = CVImage.recover_from_reverse_matrix(face_restore_out, src_img_in[roi_box[1]:roi_box[3], roi_box[0]:roi_box[2]], mat_rev, face_mask_, img_fg_mask=face_mask_)
            src_img_in[roi_box[1]:roi_box[3], roi_box[0]:roi_box[2]] = restore_roi
        return [src_img_in]


class AudioFactoryThread():

    def __init__(self,audio_data_queue=Queue(64),audio_path=None):
        self.audio_data_queue = audio_data_queue
        self.audio_path = audio_path
        self.is_running = False
        self.is_killed = False
        self.is_stopped = False
        self.is_init = False
        self.is_exit = False
        self.is_forward_func = False
        self.is_stop = False


        
    def run(self, ):
        pass

    def stop(self):
        pass

    def kill(self):
        pass

    def init(self):
        pass

        

def write_video_chaofen(output_imgs_queue, temp_dir, result_dir, work_id, audio_path, result_queue, width, height, fps, watermark_switch=0, digital_auth=0):
    output_mp4 = os.path.join(temp_dir, "{}-t.mp4".format(work_id))
    fourcc = (cv2.VideoWriter_fourcc)(*"mp4v")
    result_path = os.path.join(result_dir, "{}-r.mp4".format(work_id))
    video_write = cv2.VideoWriter(output_mp4, fourcc, fps, (width, height))
    try:
        q0 = Queue(2)
        q1 = Queue(2)
        q2 = Queue(2)
        q3 = Queue(2)
        q4 = Queue(2)
        fdt = FaceDetectThread([q0, q1])
        frt = FaceRestoreThread([q1, q2])
        fpt = FaceParseThread([q2, q3])
        fret = FaceReverseThread([q3, q4])
        cvvwt = CVVideoWriterThread(video_write, [q4])
        threads_list = [fdt, frt, fpt, fret, cvvwt]
        for thread_ in threads_list:
            thread_.start()
        state, reason, value_ = output_imgs_queue.get()
        if type(state) == bool and state == True:
            logger.info("[{}]视频帧队列处理已结束".format(work_id))
            q0.put(None)
            for thread_ in threads_list:
                    thread_.join()


        elif type(state) == bool and state == False:
            logger.error("[{}]任务视频帧队列 -> 异常原因:[{}]".format(work_id, reason))
            q0.put(None)
            for thread_ in threads_list:
                thread_.join()

            raise CustomError(reason)

        for result_img in value_:
            q0.put(result_img)

        video_write.release()
        if watermark_switch == 1:
            if digital_auth == 1:
                logger.info("[{}]任务需要水印和数字人标识".format(work_id))
                if width > height:
                    command = 'ffmpeg -y -i {} -i {} -i {} -i {} -filter_complex "overlay=(main_w-overlay_w)-10:(main_h-overlay_h)-10,overlay=(main_w-overlay_w)-10:10" -c:a aac -crf 15 -strict -2 {}'.format(audio_path, output_mp4, GlobalConfig.instance().watermark_path, GlobalConfig.instance().digital_auth_path, result_path)
                    logger.info("command:{}".format(command))
                else:
                    command = 'ffmpeg -y -i {} -i {} -i {} -i {} -filter_complex "overlay=(main_w-overlay_w)-10:(main_h-overlay_h)-10,overlay=(main_w-overlay_w)-10:10" -c:a aac -crf 15 -strict -2 {}'.format(audio_path, output_mp4, GlobalConfig.instance().watermark_path, GlobalConfig.instance().digital_auth_path, result_path)
                    logger.info("command:{}".format(command))
            else:
            
                logger.info("[{}]任务需要水印".format(work_id))
                command = 'ffmpeg -y -i {} -i {} -i {} -filter_complex "overlay=(main_w-overlay_w)-10:(main_h-overlay_h)-10" -c:a aac -crf 15 -strict -2 {}'.format(audio_path, output_mp4, GlobalConfig.instance().watermark_path, result_path)
                logger.info("command:{}".format(command))
        elif watermark_switch == 0:
            if digital_auth == 1:
                logger.info("[{}]任务需要数字人标识".format(work_id))
                if width > height:
                    command = 'ffmpeg -y -i {} -i {} -i {} -filter_complex "overlay=(main_w-overlay_w)-10:10" -c:a aac -crf 15 -strict -2 {}'.format(audio_path, output_mp4, GlobalConfig.instance().digital_auth_path, result_path)
                    logger.info("command:{}".format(command))
                else:
                    command = 'ffmpeg -y -i {} -i {} -i {} -filter_complex "overlay=(main_w-overlay_w)-10:10" -c:a aac -crf 15 -strict -2 {}'.format(audio_path, output_mp4, GlobalConfig.instance().digital_auth_path, result_path)
                    logger.info("command:{}".format(command))
            else:
                command = "ffmpeg -y -i {} -i {} -c:a aac -c:v libx264 -crf 15 -strict -2 {}".format(audio_path, output_mp4, result_path)
                logger.info("command:{}".format(command))
        subprocess.call(command, shell=True)
        print("###### write over")
        result_queue.put([True, result_path])

    except Exception as e:
        try:
            logger.error("[{}]视频帧队列处理异常结束，异常原因:[{}]".format(work_id, e.__str__()))
            result_queue.put([False, "[{}]视频帧队列处理异常结束，异常原因:[{}]".format(work_id, e.__str__())])
        finally:
            e = None
            del e

    else:
        logger.info("后处理进程结束")


def video_synthesis(output_imgs_queue):
    img_id = 0
    st = time.time()
    while True:
        if not output_imgs_queue.empty():
            et = time.time()
            print("表情迁移首次出现耗时======================:", et - st)
            output_imgs = output_imgs_queue.get()
            for img in output_imgs:
                time.sleep(0.03125)
                cv2.imshow("output_imgs", img)
                cv2.waitKey(1)
            else:
                st = time.time()


def hy_fun(wenet_model, audio_path, drivered_path, output_dir, work_id):
    drivered_queue = multiprocessing.Queue(10)
    output_imgs_queue = multiprocessing.Queue(10)
    result_queue = multiprocessing.Queue(1)
    process_list = []
    audio_wenet_feature = get_aud_feat1(audio_path, fps=30, wenet_model=wenet_model)
    process_list.append(Process(target=drivered_video, args=(drivered_queue, drivered_path, audio_wenet_feature)))
    process_list.append(Process(target=audio_transfer, args=(drivered_queue, output_imgs_queue, 8, '0')))
    process_list.append(Process(target=write_video, args=(output_imgs_queue, output_dir, output_dir, work_id, audio_path, result_queue)))
    [p.start() for p in process_list]
    [p.join() for p in process_list]
    print("主进程结束")
    try:
        result_path = result_queue.get(True, timeout=10)
        return (0, result_path)
    except Empty:
        return (1, 'generate error')
    finally:
        return None


class Status(Enum):
    run = 1
    success = 2
    error = 3


def init_wh_process(in_queue, out_queue):
    # 初始化人脸检测器和关键点检测器
    
    face_detector = FaceDetect('scrfd_500m', False, 'face_detect_utils/resources/')
    plfd = pfpld(False, 'face_detect_utils/resources/')
    logger.info('>>> init_wh_process进程启动')

    while True:
        try:
            logger.info('>>> init_wh_process进程等待任务...')
            code,drivered_path = in_queue.get()
            pre_process_dir = os.path.join(GlobalConfig.instance().temp_dir, "pre_process_dir")
            logger.info("init_wh_process进程收到数据: {} -- > {}".format(code, drivered_path))
            wh_list = []
            s = time.time()
            cap = cv2.VideoCapture(drivered_path)
            count = 0
            multi_face = False
            try:
                try:
                    if cap.isOpened():
                        while count < 100:
                            ret, frame = cap.read()
                            if not ret:
                                break
                            try:
                                bboxes, kpss = face_detector.get_bboxes(frame)
                            except Exception as e:
                                try:
                                    logger.error("[{}]init_wh exception: {}".format(code, e))
                                finally:
                                    e = None
                                    del e

                            if len(bboxes) > 0:
                                bbox = bboxes[0]
                                x1, y1, x2, y2, score = bbox.astype(np.int32)
                                x1 = max(x1 - int((x2 - x1) * 0.1), 0)
                                x2 = x2 + int((x2 - x1) * 0.1)
                                y2 = y2 + int((y2 - y1) * 0.1)
                                y1 = max(y1, 0)
                                face_img = frame[y1:y2, x1:x2,:]
                                pots = plfd.forward(face_img)[0]
                                landmarks = np.array([[x1 + x, y1 + y] for x, y in pots.astype(np.int32)])
                                xmin, ymin, w, h = cv2.boundingRect(np.array(landmarks))
                                wh_list.append(w / h)
                            if len(bboxes) > 1:
                                multi_face = True
                            count += 1
                except Exception as e1:
                    try:
                        logger.error("[{}]init_wh exception: {}".format(code, e1))
                    finally:
                        e1 = None
                        del e1

            finally:
                cap.release()
            if len(wh_list) == 0:
                wh = 0
            else:
                wh = np.mean(np.array(wh_list))

            logger.info("[%s]init_wh result :[%s]， cost: %s s" % (code, wh, time.time() - s))
            out_queue.put([code,wh,multi_face])
            torch.cuda.empty_cache()
        except Empty:
            # 当30秒内没有任务时，不要退出，只记录日志并继续等待
            logger.warning('>>> init_wh_process队列30秒内未收到任务，继续等待')
            continue
        except Exception as e:
            # 其他异常也记录但不退出
            logger.error('>>> init_wh_process处理任务时出错: %s', str(e))
            logger.error(traceback.format_exc())
            continue


def init_wh(code, drivered_path):
    s = time.time()
    face_detector = FaceDetect(cpu=False, model_path="face_detect_utils/resources/")
    plfd = pfpld(cpu=False, model_path="face_detect_utils/resources/")
    wh_list = []
    cap = cv2.VideoCapture(drivered_path)
    count = 0
    try:
        try:
            if cap.isOpened():
                while count < 100:
                    ret, frame = cap.read()
                    if not ret:
                        break
                    try:
                        bboxes, kpss = face_detector.get_bboxes(frame)
                    except Exception as e:
                        try:
                            logger.error("[{}]init_wh exception: {}".format(code, e))
                        finally:
                            e = None
                            del e

                    if len(bboxes) > 0:
                        bbox = bboxes[0]
                        x1, y1, x2, y2, score = bbox.astype(np.int32)
                        x1 = max(x1 - int((x2 - x1) * 0.1), 0)
                        x2 = x2 + int((x2 - x1) * 0.1)
                        y2 = y2 + int((y2 - y1) * 0.1)
                        y1 = max(y1, 0)
                        face_img = frame[y1:y2, x1:x2,:]
                        pots = plfd.forward(face_img)[0]
                        landmarks = np.array([[x1 + x, y1 + y] for x, y in pots.astype(np.int32)])
                        xmin, ymin, w, h = cv2.boundingRect(np.array(landmarks))
                        logger.info("xmin, ymin, w, h: {} {} {} {}".format(xmin, ymin, w, h))
                        wh_list.append(w / h)
                    count += 1

        except Exception as e1:
            try:
                logger.error("[{}]init_wh exception: {}".format(code, e1))
            finally:
                e1 = None
                del e1

    finally:
        cap.release()

    if len(wh_list) == 0:
        wh = 0
    else:
        wh = np.mean(np.array(wh_list))
    logger.info("[%s]init_wh result :[%s]， cost: %s s" % (code, wh, time.time() - s))
    torch.cuda.empty_cache()
    return wh


def get_video_info(video_file,video_info_path):
    if os.path.exists(video_info_path):
        with open(video_info_path, 'r', encoding='utf-8') as f:
            params_json = json.load(f)
            fps = params_json.get('fps', 25)
            width = params_json.get('width', 1920)
            height = params_json.get('height', 1080)
            fourcc = params_json.get('fourcc', 0)
            frame_count = params_json.get('frame_count', 0)
    else:
        cap = cv2.VideoCapture(video_file)
        fps = round(cap.get(cv2.CAP_PROP_FPS))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        fourcc = int(cap.get(cv2.CAP_PROP_FOURCC))
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        cap.release()
        params_json = {
            "fps": fps,
            "width": width,
            "height": height,
            "fourcc": fourcc,
            "frame_count": frame_count
        }
        with open(video_info_path, 'w', encoding='utf-8') as f:
            json.dump(params_json, f, ensure_ascii=False, indent=4)
            
        logger.info(f"视频信息保存到 {video_info_path}")
    return (fps, width, height, fourcc, frame_count)


def format_video_audio(code, video_path, audio_path, fourcc):

    if not fourcc == cv2.VideoWriter_fourcc("H", "2", "6", "4"):
        if fourcc == cv2.VideoWriter_fourcc("a", "v", "c", "1") or fourcc == cv2.VideoWriter_fourcc("h", "2", "6", "4"):
            ffmpeg_command = "ffmpeg -loglevel warning -i %s -crf 15 -vcodec copy -an -y %s"
        else:
            ffmpeg_command = "ffmpeg -loglevel warning -i %s -c:v libx264 -crf 15 -an -y %s"  #todo:这个是有问题的
    else:
        ffmpeg_command = "ffmpeg -loglevel warning -i %s -c:v libx264 -crf 15 -an -y %s"
    video_format = os.path.join(GlobalConfig.instance().temp_dir, code + "_format.mp4")
    ffmpeg_command = ffmpeg_command % (video_path, video_format)
    logger.info("[{}] -> ffmpeg video: {}".format(code, ffmpeg_command))
    os.system(ffmpeg_command)
    if not os.path.exists(video_format):
        raise Exception("format video error")
    ffmpeg_command = "ffmpeg -loglevel warning -i %s -ac 1 -ar 16000 -acodec pcm_s16le -y  %s"
    audio_format = os.path.join(GlobalConfig.instance().temp_dir, code + "_format.wav")
    ffmpeg_command = ffmpeg_command % (audio_path, audio_format)
    logger.info("[{}] -> ffmpeg audio: {}".format(code, ffmpeg_command))
    os.system(ffmpeg_command)
    if not os.path.exists(audio_format):
        raise Exception("format audio error")
    return (
     video_format, audio_format)


def get_license():
    logger.info("license check start ...")
    while True:
        if not check_lc():
            logger.info("license check failed")
        time.sleep(30)


def a():
    if GlobalConfig.instance().register_enable == 1:
        result = register_host()
        if not result:
            raise Exception("服务注册失败.")
        threading.Thread(target=repost_host).start()
    else:
        logger.warning(" -> 服务不进行注册")


class TransDhTask(object):

    def __init__(self, *args, **kwargs):
        logger.info("TransDhTask init")
        set_start_method("spawn", force=True)
        self.run_lock = threading.Lock()
        self.task_dic = {}
        
        self.run_flag = False
        self.use_npy = True
        self.is_train = '1'
        self.batch_size = int(GlobalConfig.instance().batch_size)
        self.drivered_queue = multiprocessing.Queue(16,)
        self.output_imgs_queue = multiprocessing.Queue(10)
        self.result_queue = multiprocessing.Queue(1)
        self.manager = multiprocessing.Manager()
        self.audio_stream_queue = self.manager.Queue(16)
        
        # 初始化音频处理进程
        self.audio_processor_process = multiprocessing.Process(
            target=audio_processor,
            args=(self.drivered_queue, self.audio_stream_queue, self.batch_size, self.use_npy),
            daemon=True
        )
        
        # 初始化视频处理进程
        self.dh_stream_process = multiprocessing.Process(
            target=dh_stream_processor,
            args=(self.drivered_queue, self.output_imgs_queue, self.batch_size, self.use_npy),
            daemon=True
        )
        
        # 启动进程
        self.audio_processor_process.start()
        self.dh_stream_process.start()


    @classmethod
    def instance(cls, *args, **kwargs):
        if not hasattr(TransDhTask, "_instance"):
            TransDhTask._instance = TransDhTask(*args, **kwargs)
        return TransDhTask._instance

    def work(self, audio_url, video_url, code, watermark_switch, speaker_id, is_train, digital_auth, chaofen, pn, is_stream=False):
        logger.info("任务:{} -> audio_url:{}  video_url:{} stream_mode:{}".format(code,audio_url,video_url,is_stream))

        st = time.time()
        self.run_flag = True
        try:
            try:
                self.change_task_status(code, Status.run, 0, "", "")
                try:
                    s1 = time.time()
                    #todo 改成训练的时候保存
                    video_info_path = os.path.join('workspace', 'models', speaker_id, 'video_info.json')
                    fps, width, height, fourcc, frame_count = get_video_info(video_url,video_info_path)
                    logger.info("[{}] -> 获取视频信息耗时:fps:{}, width:{}, height:{}, fourcc:{}".format(code, fps, width, height, fourcc))
                    
                    # For streaming, audio_url is None, but we might still need to download video
                    if is_stream:
                        # In streaming mode, only process the video URL
                        _tmp_audio_path, _tmp_video_path = self.preprocess(None, video_url, code)
                        _video_url = _tmp_video_path
                        _audio_url = None # No audio file to process
                        logger.info("[{}] -> 视频流媒体预处理耗时:{}s".format(code, time.time() - s1))
                    else:
                        _tmp_audio_path, _tmp_video_path = self.preprocess(audio_url, video_url, code)
                        _video_url, _audio_url = format_video_audio(code, _tmp_video_path, _tmp_audio_path, fourcc)
                        logger.info("[{}] -> 预处理耗时:{}s".format(code, time.time() - s1))

                except Exception as e:
                    try:
                        traceback.print_exc()
                        logger.error("[{}]预处理失败，异常信息:[{}]".format(code, e.__str__()))
                        raise CustomError("[{}]预处理失败，异常信息:[{}]".format(code, e.__str__()))
                    finally:
                        e = None
                        del e


                if not (os.path.exists(_video_url) and (is_stream or os.path.exists(_audio_url))):
                    raise Exception("Video input or audio input download processing exception")
                self.change_task_status(code, Status.run, 10, "", "文件下载完成")

                ######################################## 1.获取wh值 ########################################
                wh = 0
                wh_cache = -1 # 是否读取到wh值的flag
                wh_cal_flag = False # 是否计算wh值的flag

                try:
                    # 1. 加载wh值
                    wh_file = os.path.join('workspace', 'models', speaker_id, 'wh_value.txt')
                    if  os.path.exists(wh_file):
                        with open(wh_file, 'r') as f:
                            loaded_wh = float(f.read().strip())
                            logger.info(f"成功加载wh值: {loaded_wh}")
                            wh = wh_cache = loaded_wh
                except Exception as e: # todo 返回失败
                    logger.error("[{}] 获取wh值失败，重新计算wh值".format(code))

                ######################################## 2.音频特征提取 ########################################
                # 音频特征提取现在在AudioExtractor中完成，这里不再需要
                self.change_task_status(code, Status.run, 20, "", "音频特征提取完成")
                process_list = []

                ######################################## 4. 视频处理 ########################################   
                self.spk_2_code[speaker_id] = code
                
                # 创建一个长期运行的进程，并通过队列发送任务
                task_message = [
                    "start_task", code, _video_url, _audio_url, wh, speaker_id, is_train, pn, is_stream
                ]
                self.drivered_queue.put(task_message)


                ######################################## 5. 视频处理 ########################################   
                if chaofen == 1 and GlobalConfig.instance().chaofen_after == "1":
                    process_list.append(Process(target=write_video_chaofen, args=(
                        self.output_imgs_queue, GlobalConfig.instance().temp_dir,
                        GlobalConfig.instance().result_dir, code,
                        _audio_url if _audio_url else video_url, # Use video as dummy audio path for ffmpeg if streaming
                        self.result_queue, width, height, fps, watermark_switch, digital_auth),
                        daemon=True))
                else:
                    process_list.append(Process(target=write_video, args=(
                        self.output_imgs_queue, GlobalConfig.instance().temp_dir,
                        GlobalConfig.instance().result_dir, code,
                        _audio_url if _audio_url else video_url, # Use video as dummy audio path for ffmpeg if streaming
                        self.result_queue, width, height, fps, watermark_switch, digital_auth,
                        self.use_npy), # Pass self.use_npy here
                        daemon=True))
                [p.start() for p in process_list]
                [p.join() for p in process_list]
                try:
                    state, result_path = self.result_queue.get(True, timeout=100)
                    print(">>>>>>>>>>>>>>1111 {} {}".format(state, result_path))
                    if state:
                        logger.info(">>>>>>>>>>>>>>算法任务耗时：{:.3f}s".format(time.time() - s1))
                        self.change_task_status(code, Status.run, 90, result_path, "视频处理完成")
                        _remote_file = os.path.join(GlobalConfig.instance().result_dir, "{}.mp4".format(code))
                        _final_url = result_path
                        logger.info("[{}]任务最终合成结果: {}".format(code, _final_url))
                        self.change_task_status(code, Status.success, 100, _final_url, "任务完成")
                        sweep([GlobalConfig.instance().result_dir], True if GlobalConfig.instance().result_clean_switch == "1" else False)
                    else:
                        self.change_task_status(code, Status.error, 0, "", result_path)
                except Empty:
                    self.change_task_status(code, Status.error, 0, "", "**生成视频失败")

                finally:
                    # Clean up the queue from the central dictionary
                    if is_stream and code in self.stream_queues:
                        del self.stream_queues[code]
                    gc.collect()

            except Exception as e:
                try:
                    traceback.print_exc()
                    logger.error("[{}]任务执行失败，异常信息:[{}]".format(code, e.__str__()))
                    self.change_task_status(code, Status.error, 0, "", e.__str__())
                finally:
                    e = None
                    del e

        finally:
            sweep([GlobalConfig.instance().temp_dir], True if GlobalConfig.instance().temp_clean_switch == "1" else False)
            self.drivered_queue.empty()
            self.output_imgs_queue.empty()
            self.result_queue.empty()
            torch.cuda.empty_cache()
            self.run_flag = False
            logger.info(">>> 任务:{} 耗时:{} ".format(code, time.time() - st))

    def preprocess(self, audio_url, video_url, code):
        s_pre = time.time()
        try:
            if audio_url.startswith("http:") or audio_url.startswith("https:"):
                _tmp_audio_path = os.path.join(GlobalConfig.instance().temp_dir, "{}.wav".format(code))
                download_file(audio_url, _tmp_audio_path)
            else:
                _tmp_audio_path = audio_url
        except Exception as e:
            try:
                traceback.print_exc()
                raise CustomError("[{}]音频下载失败，异常信息:[{}]".format(code, e.__str__()))
            finally:
                e = None
                del e


        try:
            if video_url.startswith("http:") or video_url.startswith("https:"):
                _tmp_video_path = os.path.join(GlobalConfig.instance().temp_dir, "{}.mp4".format(code))
                download_file(video_url, _tmp_video_path)
            else:
                _tmp_video_path = video_url
        except Exception as e:
            try:
                traceback.print_exc()
                raise CustomError("[{}]视频下载失败，异常信息:[{}]".format(code, e.__str__()))
            finally:
                e = None

        print("--------------------> download cost:{}s".format(time.time() - s_pre))
        return (_tmp_audio_path, _tmp_video_path)

    def change_task_status(self, code, status: Status, progress: int, result: str, msg=''):
        try:
            try:
                self.run_lock.acquire()
                if code in self.task_dic:
                    self.task_dic[code] = (
                     status, progress, result, msg)
            except Exception as e:
                try:
                    traceback.print_exc()
                    logger.error("[{}]修改任务状态异常，异常信息:[{}]".format(code, e.__str__()))
                finally:
                    e = None
                    del e

        finally:
            self.run_lock.release()



if __name__ == "__main__":
    # set_start_method("spawn", force=True)
    wenet_model = load_ppg_model("wenet/examples/aishell/aidata/conf/train_conformer_multi_cn.yaml", "wenet/examples/aishell/aidata/exp/conformer/wenetmodel.pt", "cuda")
    st = time.time()
    # result = hy_fun(wenet_model, "test_data/audio/driver_add_valume.wav", "./landmark2face_wy/checkpoints/hy/1.mp4", "./result", 1001)
    # print(result, time.time() - st)
    audio_wenet_feature = get_aud_feat1("test_data/audio/driver_add_valume.wav", fps=25, wenet_model=wenet_model)
