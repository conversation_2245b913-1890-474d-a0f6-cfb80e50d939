# -*-coding: utf-8 -*-
# @Time :2025-08-12
# <AUTHOR> lish<PERSON>jing
# @Email : <EMAIL>
# @File : base_webrtc.py

import os, sys, time, fractions, json, uuid, threading, traceback, asyncio
import numpy as np
from abc import ABC, abstractmethod
from weakref import WeakSet
from typing import Dict, Optional, Set, Union

from loguru import logger
from aiohttp import web
from aiortc import RTCPeerConnection, RTCSessionDescription
from aiortc.contrib.media import MediaStreamTrack


class BaseWebRTCStreamer(ABC):
    """
    WebRTC推流器的基础类，提供通用的事件循环管理、连接管理和基础配置
    """
    
    def __init__(self, signaling_url: str, audio_sample_rate: int = 16000, video_fps: int = 25):
        """
        初始化基础WebRTC推流器
        
        Args:
            signaling_url: 信令服务器URL，格式为 'host:port'
            audio_sample_rate: 音频采样率
            video_fps: 视频帧率
        """
        # 解析信令URL
        self._host, self._port = self._parse_signaling_url(signaling_url)
        
        # 基础音视频参数
        self._audio_sample_rate = audio_sample_rate
        self._video_fps = video_fps
        
        # 连接管理
        self._pcs = WeakSet()
        self.is_connected = False
        
        # 事件循环管理
        self._thread = None
        self._loop = None
        self.is_running = False
        self._loop_started = threading.Event()
        
        # 媒体轨道（由子类实现）
        self._video_track = None
        self._audio_track = None
        
        # 性能监控
        self._drop_count = 0
        self._connection_count = 0
        
        logger.info(f"【BaseWebRTCStreamer】初始化完成: {self._host}:{self._port}")

    def _parse_signaling_url(self, signaling_url: str) -> tuple:
        """解析信令服务器URL"""
        try:
            host, port_str = signaling_url.split(":")
            port = int(port_str)
            return host, port
        except (ValueError, AttributeError):
            logger.error(f"【BaseWebRTCStreamer】无效的信令URL '{signaling_url}'。使用默认值 '0.0.0.0:8080'。")
            return "0.0.0.0", 8080

    def start(self) -> bool:
        """启动WebRTC推流器"""
        if self.is_running:
            logger.warning("【BaseWebRTCStreamer】推流器已在运行")
            return True
        
        logger.info("【BaseWebRTCStreamer】启动推流器...")
        self._thread = threading.Thread(target=self._run_event_loop, daemon=True)
        self._thread.start()
        
        # 等待事件循环准备就绪
        if not self._loop_started.wait(timeout=10):
            logger.error("【BaseWebRTCStreamer】事件循环启动超时")
            self.stop()
            return False
        
        logger.info(f"【BaseWebRTCStreamer】推流器启动成功。信令服务: http://{self._host}:{self._port}/offer")
        return True

    def stop(self):
        """停止WebRTC推流器"""
        if not self.is_running:
            return
        
        logger.info("【BaseWebRTCStreamer】停止推流器...")
        self.is_running = False
        
        if self._loop and self._loop.is_running():
            # 安排关闭协程并等待其完成
            future = asyncio.run_coroutine_threadsafe(self._shutdown(), self._loop)
            try:
                future.result(timeout=5)
            except asyncio.TimeoutError:
                logger.warning("【BaseWebRTCStreamer】关闭超时")

        if self._thread:
            self._thread.join(timeout=5)
        
        logger.info("【BaseWebRTCStreamer】推流器已停止")

    def _run_event_loop(self):
        """在后台线程中运行asyncio事件循环"""
        logger.info("【BaseWebRTCStreamer】启动事件循环...")
        self._loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self._loop)
        self.is_running = True
        
        try:
            # 创建媒体轨道（由子类实现）
            self._create_media_tracks()
            
            # 发出循环已准备就绪的信号
            self._loop_started.set()
            
            # 运行服务器
            self._loop.run_until_complete(self._run_server())
            
        except Exception as e:
            logger.error(f"【BaseWebRTCStreamer】事件循环崩溃: {traceback.format_exc()}")
        finally:
            self.is_running = False
            logger.info("【BaseWebRTCStreamer】事件循环已关闭")

    async def _run_server(self):
        """设置并运行aiohttp信令服务器"""
        app = web.Application()
        app.router.add_post("/offer", self._handle_offer)
        
        # 添加CORS支持
        app.router.add_options("/offer", self._handle_options)
        
        runner = web.AppRunner(app)
        await runner.setup()
        site = web.TCPSite(runner, self._host, self._port)
        await site.start()
        
        logger.info(f"【BaseWebRTCStreamer】信令服务器启动: http://{self._host}:{self._port}")

        # 保持服务器运行直到 stop() 被调用
        while self.is_running:
            await asyncio.sleep(0.1)

        await runner.cleanup()
        logger.info("【BaseWebRTCStreamer】信令服务器已关闭")

    async def _handle_options(self, request):
        """处理CORS预检请求"""
        return web.Response(
            headers={
                'Access-Control-Allow-Origin': '*',
                'Access-Control-Allow-Methods': 'POST, OPTIONS',
                'Access-Control-Allow-Headers': 'Content-Type',
            }
        )

    async def _handle_offer(self, request):
        """处理来自WebRTC客户端的SDP提议"""
        try:
            params = await request.json()
            offer = RTCSessionDescription(sdp=params["sdp"], type=params["type"])
            
            # 创建对等连接
            pc = self._create_peer_connection()
            self._pcs.add(pc)
            
            # 设置连接状态监听
            self._setup_peer_connection_handlers(pc)
            
            # 添加媒体轨道
            if self._video_track:
                pc.addTrack(self._video_track)
            if self._audio_track:
                pc.addTrack(self._audio_track)
            
            # 处理SDP
            await pc.setRemoteDescription(offer)
            answer = await pc.createAnswer()
            await pc.setLocalDescription(answer)
            
            self._connection_count += 1
            self.is_connected = True
            logger.info(f"【BaseWebRTCStreamer】新客户端连接。总连接数: {len(self._pcs)}")
            
            return web.Response(
                content_type="application/json",
                text=json.dumps({
                    "sdp": pc.localDescription.sdp, 
                    "type": pc.localDescription.type
                }),
                headers={
                    'Access-Control-Allow-Origin': '*',
                    'Access-Control-Allow-Methods': 'POST, OPTIONS',
                    'Access-Control-Allow-Headers': 'Content-Type',
                }
            )
            
        except Exception as e:
            logger.error(f"【BaseWebRTCStreamer】处理offer失败: {e}")
            return web.Response(status=500, text=str(e))

    def _create_peer_connection(self) -> RTCPeerConnection:
        """创建RTCPeerConnection"""
        pc = RTCPeerConnection()
        logger.info("【BaseWebRTCStreamer】创建新的对等连接")
        return pc

    def _setup_peer_connection_handlers(self, pc: RTCPeerConnection):
        """设置对等连接的事件处理器"""
        @pc.on("connectionstatechange")
        async def on_connectionstatechange():
            logger.info(f"【BaseWebRTCStreamer】连接状态: {pc.connectionState}")
            if pc.connectionState in ("failed", "closed", "disconnected"):
                await pc.close()
                self._pcs.discard(pc)
                # 检查是否还有活跃连接
                if len(self._pcs) == 0:
                    self.is_connected = False
                    logger.info("【BaseWebRTCStreamer】所有客户端已断开连接")

        @pc.on("iceconnectionstatechange")
        async def on_iceconnectionstatechange():
            logger.info(f"【BaseWebRTCStreamer】ICE连接状态: {pc.iceConnectionState}")

        @pc.on("icegatheringstatechange")
        async def on_icegatheringstatechange():
            logger.info(f"【BaseWebRTCStreamer】ICE收集状态: {pc.iceGatheringState}")

    async def _shutdown(self):
        """关闭所有对等连接"""
        logger.info("【BaseWebRTCStreamer】关闭所有连接...")
        tasks = [pc.close() for pc in self._pcs]
        await asyncio.gather(*tasks, return_exceptions=True)
        self._pcs.clear()
        self.is_connected = False
        logger.info("【BaseWebRTCStreamer】所有连接已关闭")

    def get_status(self) -> Dict:
        """获取推流器状态"""
        return {
            'is_running': self.is_running,
            'is_connected': self.is_connected,
            'connection_count': len(self._pcs),
            'total_connections': self._connection_count,
            'drop_count': self._drop_count,
            'host': self._host,
            'port': self._port,
            'video_fps': self._video_fps,
            'audio_sample_rate': self._audio_sample_rate
        }

    # 抽象方法，由子类实现
    @abstractmethod
    def _create_media_tracks(self):
        """创建媒体轨道（由子类实现）"""
        pass

    @abstractmethod
    def write_frame(self, video_frame, audio_data):
        """写入视频帧和音频数据（由子类实现）"""
        pass
