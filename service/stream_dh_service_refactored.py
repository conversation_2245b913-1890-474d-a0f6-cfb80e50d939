"""
@project : face2face_train
<AUTHOR> huyi
@file   : trans_dh_service.py
@ide    : PyCharm
@time   : 2023-12-06 14:47:11
"""
import gc, multiprocessing, os, subprocess, threading, time, traceback,sys
from enum import Enum
from multiprocessing import Process, set_start_method
from queue import Empty, Full,Queue
import cv2, librosa, numpy as np, torch
import json
from cv2box import CVImage
from cv2box.cv_gears import Linker, Queue, CVVideoWriterThread
from face_detect_utils.face_detect import FaceDetect, pfpld
from face_detect_utils.head_pose import Headpose
from face_lib.face_detect_and_align import FaceDetect5Landmarks
from face_lib.face_restore import GFPGAN
from h_utils.custom import CustomError
from h_utils.request_utils import download_file
from h_utils.sweep_bot import sweep
from landmark2face_wy.digitalhuman_interface import DigitalHumanModel
from preprocess_audio_and_3dmm import op
from y_utils.config import GlobalConfig
from y_utils.logger import logger
from .server import register_host, repost_host
from lt_utils.wo_common import videocap_local_imgs
import glob
from utils.video_utils import get_video_info, read_image, save_image
from utils.flow_utils import locate_exception
from loguru import logger
from wenet.compute_ctc_att_bnf import get_weget, load_ppg_model, WenetStreamer


# ==================================================================================================
# region: Helper Functions (Maintained from original file)
# ==================================================================================================

def feature_extraction_wenet(audio_file, fps, wenet_model, mfccnorm=True, section=560000):
    """
    音频特征提取 (用于非流式)
    """
    rate = 16000
    win_size = 20
    if type(audio_file) == str:
        sig, rate = librosa.load(audio_file, sr=rate, duration=None)
    else:
        sig = audio_file
    time_duration = len(sig) / rate # 音频时长:秒
    cnts = range(int(time_duration * fps)) # 音频帧数
    indexs = []
    f_wenet_all = get_weget(audio_file, wenet_model, section)
    print(f"f_wenet_all.shape:{f_wenet_all.shape}")
    for cnt in cnts:
        c_count = int(cnt / cnts[-1] * (f_wenet_all.shape[0] - 20)) + win_size // 2
        indexs.append(f_wenet_all[(c_count - win_size // 2):c_count + win_size // 2, ...])
    return indexs


def get_aud_feat1(wav_fragment, fps, wenet_model):
    return feature_extraction_wenet(wav_fragment, fps, wenet_model)


def warp_imgs(imgs_data):
    caped_img2 = {idx: {'imgs_data':it,  'idx':idx} for it, idx in zip(imgs_data, range(len(imgs_data)))}
    return caped_img2

def write_silence_blend_imgs(silence_path,img_name_list,blend_imgs,use_npy=False): # 保存静音数据
    silence_imgs_list = os.listdir(silence_path) # 获取所有的图片名称
    for i,name in enumerate(img_name_list):
        if not  name in silence_imgs_list:
            np.save(os.path.join(silence_path,name),blend_imgs[i]) if use_npy else cv2.imwrite(os.path.join(silence_path,name),blend_imgs[i])


def read_silence_blend_imgs(silence_path,img_name_list,use_npy=False): # 直接读取静音数据
    # 读取静音数据t
    is_find,blend_imgs = True,None
    silence_imgs_list = os.listdir(silence_path) # todo 可以不用每次遍历，后面想想办法优化

    for name in img_name_list:
        if not  name in silence_imgs_list:
            is_find = False
            break
    # 读取静音数据
    if is_find:
        blend_imgs = [np.load(os.path.join(silence_path,name)) for name in img_name_list] if use_npy else [cv2.imread(os.path.join(silence_path,name)) for name in img_name_list]
    return is_find,blend_imgs

def get_one_complete_img(image, mask_B_pre, box_info,image_id):
    # 3. 融合
    image_copy = image.copy()
    y1, y2, x1, x2 = box_info # 裁剪人脸框
    face_box_width,face_box_height = y2 - y1,x2 - x1
    
    try:
        mask_B_pre_resize = cv2.resize(mask_B_pre, (face_box_width, face_box_height))
        img_h, img_w = image_copy.shape[:2]
        
        img_paste_start_row = max(0, x1)
        img_paste_end_row = min(img_h, x2)
        img_paste_start_col = max(0, y1)
        img_paste_end_col = min(img_w, y2)

        src_start_row = -x1 if x1 < 0 else 0
        src_start_col = -y1 if y1 < 0 else 0

        paste_height = img_paste_end_row - img_paste_start_row
        paste_width = img_paste_end_col - img_paste_start_col
        
        src_end_row = src_start_row + paste_height
        src_end_col = src_start_col + paste_width

        if paste_height > 0 and paste_width > 0 and src_end_row <= mask_B_pre_resize.shape[0] and src_end_col <= mask_B_pre_resize.shape[1]:
            image_copy[img_paste_start_row:img_paste_end_row, img_paste_start_col:img_paste_end_col] = \
                mask_B_pre_resize[src_start_row:src_end_row, src_start_col:src_end_col]
            image = image_copy
        else:
            logger.warning(f"[Frame {image_id}: Invalid dimensions for pasting face mask. Skipping blend.")

    except cv2.error as e_resize:
        logger.error(f"[ Frame {image_id}: cv2.resize error for mask_B_pre: {e_resize}. Skipping blend.")

    return image


def get_complete_imgs(output_img_list, drivered_imgs_for_batch, params, no_face_indices_for_batch, work_id_for_logging):
    t1 = time.time()
    out_shape, output_resize, _, Y1_list, Y2_list, X1_list, X2_list = params
    complete_imgs = []

    if not drivered_imgs_for_batch:
        logger.error(f"[{work_id_for_logging}] get_complete_imgs: drivered_imgs_for_batch is empty.")
        return complete_imgs
    
    # The BBox lists in `params` are for the *entire* video, so we need to get the correct slice
    start_frame_index = -1 # We need this info. Let's assume it's passed somehow. For now, this is a bug.
                          # Correction: let's pass it in.
    
    # We will get the start_index from the call site.
    # Let's adjust the signature to be:
    # get_complete_imgs(output_img_list, drivered_imgs_for_batch, params, no_face_indices_for_batch, work_id_for_logging, start_frame_index)
    # Actually, the face_params are already sliced in the processor. We can assume the lists are correctly sized.
    
    for i, image in enumerate(drivered_imgs_for_batch):
        if i in no_face_indices_for_batch:
            complete_imgs.append(image)
            continue

        image = image.copy()
        if i >= len(output_img_list) or output_img_list[i] is None:
            logger.warning(f"[{work_id_for_logging}] Frame {i}: No valid raw face region found. Using driver frame.")
        else:
            mask_B_pre = output_img_list[i]
            if i < len(Y1_list):
                y1, y2, x1, x2 = (Y1_list[i], Y2_list[i], X1_list[i], X2_list[i])
                image = get_one_complete_img(image, mask_B_pre, (y1, y2, x1, x2), f"{work_id_for_logging}-frame{i}")
            else:
                 logger.error(f"[{work_id_for_logging}] Frame {i}: Index out of bounds for bounding box lists.")

        complete_imgs.append(image)

    t2 = time.time()
    logger.info(f"[{work_id_for_logging}] get_complete_imgs processed {len(complete_imgs)} frames, 耗时: {t2-t1:.2f}秒")
    return complete_imgs


def get_blend_imgs(batch_size, audio_data, face_data_dict, blend_dynamic, params, digital_human_model, frameId):
    # This function calls the NN model for inference.
    result_raw_face_regions = digital_human_model.inference_notraining(
        audio_data, 
        face_data_dict, 
        batch_size
    )
    return result_raw_face_regions

# endregion
# ==================================================================================================


# ==================================================================================================
# region: Refactored Core Classes and Processes
# ==================================================================================================

class AudioExtractor(object):
    """
    负责从音频源（流或文件）提取Wenet特征。
    它是一个纯粹的音频处理器，不关心视频帧或批处理。
    """
    def __init__(self, feature_queue, audio_stream_queue):
        self.work_id_for_logging = "AudioExtractor"
        self.feature_queue = feature_queue
        self.audio_stream_queue = audio_stream_queue
        self.wenet_model = load_ppg_model("wenet/examples/aishell/aidata/conf/train_conformer_multi_cn.yaml", "wenet/examples/aishell/aidata/exp/conformer/wenetmodel.pt", "cuda")
        self.streamer = WenetStreamer(self.wenet_model)
        self.task_info = None
        self.is_stream = False
        self.audio_path = None
        self._is_running = False

    def init_task(self, task_info):
        self.task_info = task_info
        self.is_stream = task_info.get('is_stream', False)
        self.audio_path = task_info.get('audio_path')
        logger.info(f"【{self.work_id_for_logging}】 AudioExtractor 初始化成功. 流式模式: {self.is_stream}")
        return self

    def run(self):
        self._is_running = True
        try:
            if self.is_stream:
                self.run_flow_audio_with_wenet()
            else:
                self.run_local_audio()
        except Exception as e:
            logger.error(f"【{self.work_id_for_logging}】 处理失败: {e}", exc_info=True)
        finally:
            # self.finish()
            pass

    def run_local_audio(self):
        logger.info(f"[{self.work_id_for_logging}] Processing local audio file: {self.audio_path}")
        f_wenet_all = get_weget(self.audio_path, self.wenet_model)
        logger.info(f"[{self.work_id_for_logging}] Extracted features from local audio, shape: {f_wenet_all.shape}")
        
        chunk_size = 100
        for i in range(0, f_wenet_all.shape[0], chunk_size):
            chunk = f_wenet_all[i:i + chunk_size, :]
            if self._is_running:
                self.feature_queue.put(chunk)
            else:
                break
    
    def run_flow_audio_with_wenet(self):
        logger.info(f"【{self.work_id_for_logging}】 开始流式音频处理.")
        self._is_running = True
        while self._is_running:
            try:
                status, audio_chunk = self.audio_stream_queue.get(block=True)

                if status == "data" and audio_chunk:
                    audio_segment = np.frombuffer(audio_chunk, dtype=np.int16).astype(np.float32) / 32768.0
                    features = self.streamer.process(audio_segment)
                    if features is not None and len(features) > 0:
                        self.feature_queue.put(("data", features.tolist()))
                elif status == "start":
                    self.streamer.reset()
                    self.feature_queue.put(("start", None))

                elif status == "silence":
                    logger.info(f"【{self.work_id_for_logging}】 音频段结束信号接收. Flushing segment...")
                    flushed_features = self.streamer.flush()
                    while flushed_features is not None and len(flushed_features) > 0:
                        self.feature_queue.put(("data", flushed_features.tolist()))
                        flushed_features = self.streamer.flush()

                    # 放入结束信号，并重置流式器
                    self.feature_queue.put(("silence", None))
                    self.streamer.reset()
                    logger.info(f"【{self.work_id_for_logging}】 Streamer reset, ready for next audio segment.")

                elif status == "end":
                    logger.info(f"【{self.work_id_for_logging}】 音频流结束信号接收. Flushing...")
                    flushed_features = self.streamer.flush()
                    while flushed_features is not None and len(flushed_features) > 0:
                        self.feature_queue.put(("data", flushed_features.tolist()))
                        flushed_features = self.streamer.flush()
                    self.feature_queue.put(("end", None))

                    self._is_running = False

            except Empty:
                logger.warning(f"【{self.work_id_for_logging}】 等待音频块超时. 假设流式处理结束. Flushing...")
                flushed_features = self.streamer.flush()
                if flushed_features is not None and len(flushed_features) > 0:
                    self.feature_queue.put(("data", flushed_features.tolist()))
                self._is_running = False
            except Exception as e:
                logger.error(f"【{self.work_id_for_logging}】 流式音频处理循环错误: {e}", exc_info=True)
                self._is_running = False

    def finish(self, error=False):  # todo: 需要优化
        logger.info(f"【{self.work_id_for_logging}】 AudioExtractor 完成. 将结束信号放入特征队列.")
        self.feature_queue.put(("silence", None))
        self._is_running = False

def audio_processor(task_queue, feature_queue, audio_stream_queue):
    extractor = AudioExtractor(feature_queue=feature_queue, audio_stream_queue=audio_stream_queue)
    while True:
        try:
            task_info = task_queue.get(block=True)
            if task_info is None:
                logger.info("【audio_processor】 结束信号接收, 退出.")
                break
            extractor.init_task(task_info).run()
        except Exception as e:
            work_id = task_info.get('work_id', 'unknown')
            logger.error(f"【{work_id}】 audio_processor 处理失败: {e}", exc_info=True)
            if extractor:
                extractor.finish(error=True)


class DhStreamProcessor(object):
    """
    数字人流式处理器。
    - 从feature_queue接收音频特征。
    - 根据音频特征，匹配驱动视频帧。
    - 构建批次数据，调用模型进行推理。
    - 将生成的图像放入output_imgs_queue。
    """
    def __init__(self, feature_queue, output_imgs_queue, batch_size=8, use_npy=False):
        self.work_id_for_logging = "DhStreamProcessor"
        self.feature_queue = feature_queue
        self.output_imgs_queue = output_imgs_queue
        self.batch_size = batch_size
        self.use_npy = use_npy
        self.digital_human_model = DigitalHumanModel(GlobalConfig.instance().blend_dynamic, GlobalConfig.instance().chaofen_before, face_blur_detect=False,half=False)
        self.params = None
        self.drivered_imgs_data = None
        self.total_video_frame = 0
        self.fps = 25
        self.face_data_dict = None
        self.win_size = 20
        self.current_frame_idx = 0
        self.task_info = None
        self._is_running = False
        self.output_resize = 1
        self.scrfd_detector,self.scrfd_predictor,self.hp = None,None,None
        self.work_id_for_logging = "DhStreamProcessor"
        self.silence_buffer_batch_num = 2 # 当静音状态的时候，最多缓存的批次数(2批*4帧*40ms=320ms)



    def init_task(self, task_info):
        self.task_info = task_info
        self.state,self.code,self.wh,self.speaker_id,self.is_train_str,self.use_npy_flag,self.total_video_frame,self.original_frame_shape_init = task_info
        self.init_batch_data()
        self.history_data = {
            'bounding_box_list': [],
            'bounding_box_p_list': [],
            'landmarks_list': [],
            'crop_lm_list': [],
            'no_face_indices': [],
            'speaker_id':self.speaker_id
    }   #初始化

        logger.info(f"【DhStreamProcessor】 开始处理视频: {self.code}, wh: {self.wh}, speaker_id: {self.speaker_id}, is_train: {self.is_train_str}, use_npy: {self.use_npy_flag}, total_video_frame: {self.total_video_frame}, shape_init: {self.original_frame_shape_init}")

        # 初始化wh
        if self.wh == 0 or self.wh == -1:
            self.wh = self.digital_human_model.drivered_wh

        # 初始化地址属性
        self.models_dir = os.path.join('workspace', 'models', self.speaker_id) # 模型文件地址
        self.chaofen_frames_dir = os.path.join(self.models_dir, f'{"chaofen_frames_npy" if self.use_npy else "chaofen_frames"}') # 超分帧地址
        self.drivered_frames_dir = os.path.join(self.models_dir, 'drivered_frames') # 驱动帧地址
        self.img_dir = self.chaofen_frames_dir if os.path.exists(self.chaofen_frames_dir) else self.drivered_frames_dir # 如果存在超分帧地址就优选
        self.silence_img_dir = os.path.join(self.models_dir, 'silence_frames') # 静音帧地址
        os.makedirs(self.silence_img_dir,exist_ok=True)
        self.video_info_path = os.path.join(self.models_dir, 'video_info.json') # 视频信息地址

        self.suffix = "jpg" if self.use_npy else "npy"

        self.history_data_path = os.path.join(self.models_dir, 'face_data', 'face_data.json') # 历史人脸数据地址

        # 图片循环id列表，用于循环播放驱动帧
        self.current_idx = 0
        self.cycle_img_idx_list = [i for i in range(1,self.total_video_frame+1)] + [i for i in range(1,self.total_video_frame+1)][::-1]  if self.pn else [i for i in range(1,self.total_video_frame+1)]
        self.cycle_img_len = self.total_video_frame*2 if self.pn else self.total_video_frame

        # 加载历史数据 
        # todo: 没有历史数据的情况没考虑
        self.history_data_path = os.path.join(self.models_dir, 'face_data', 'face_data.json')
        if os.path.exists(self.history_data_path): # 如果存在历史数据文件，则加载历史数据
            logger.info(f"加载历史人脸数据: {self.history_data_path}")
            try:
                with open(self.history_data_path, 'r') as f:
                    loaded_history = json.load(f)
                    # Basic validation before assigning
                    if isinstance(loaded_history, dict) and loaded_history.get('speaker_id') == self.speaker_id:
                        self.history_data = loaded_history
                        logger.info(f"成功加载历史人脸数据: {self.speaker_id}")
                    else:
                        logger.warning(f"加载历史人脸数据失败: {self.history_data_path}, 使用新历史数据.")
            except Exception as e_load: # todo 需要处理,找不到就返回失败
                logger.error(f"Error loading history data: {e_load}, using new history.")
                self.history_data['speaker_id'] = self.speaker_id # Ensure speaker_id is set
        else:
            logger.info(f"不存在历史人脸数据: {self.speaker_id}")

        self.start_time = time.time()

        logger.info(f"【{self.work_id_for_logging}】 初始化完成.")
        return self

    # 初始化batch的数据
    def init_batch_data(self):
        self.drivered_fnames_list = [] # Changed from drivered_list
        self.wenet_feature_list = []
        self.batch_img_idx_list =[]  # 视频帧索引,有可能是倒叙的，
        self.batch_aud_idx_list =[]  # 音频帧索引，只会是正序
        self.batch_status = [] # 批次中每一帧的状态，data,silence,start,end

    def process_state(self, data):
        """
         self._status: 当前状态 start,data:有音频流传入；silence:静音状态，本段音频结束，没有音频流输入但是还要有视频流输出；finish:结束状态，本次任务结束，等待下次任务开始
         data: 音频数据
         silence: 静音数据
         start: 开始数据，没有音频流输入但是还要有视频流输出
         finish: 结束数据
         """
        
        if self._status == "data":
            self.process_feature_chunk(data)
        elif self._status == "silence":
            logger.info(f"【{self.work_id_for_logging}】 开始进入静音阶段.")
            self.process_batch() # 处理当前批次存余数据
            self.silence_process() # 进入静音阶段

        elif self._status == "start":
            self.init_batch_data()
            
        elif self._status == "finish":
            self._is_running = False
            self.process_batch() # 处理当前批次存余数据

    def run(self):
        self._is_running = True
        logger.info(f"【{self.work_id_for_logging}】 开始循环推理.")
        while self._is_running:
            try:
                _status, data = self.feature_queue.get(block=True)
                self._status = _status
                self.process_state(data)
                
            except Exception as e:
                logger.error(f"[{self.work_id_for_logging}] Exception in DhStreamProcessor run loop: {e}", exc_info=True)
                self._is_running = False
        self.finish()

    def process_feature_chunk(self, audio_feature):
        self.wenet_feature_list.append(audio_feature)
        self.current_idx += 1
        img_idx = self.cycle_img_idx_list[self.current_idx % self.cycle_img_len]
        self.batch_img_idx_list.append(img_idx)  # 从1开始
        self.batch_aud_idx_list.append(self.current_idx)
        f_file_name = os.path.join(self.img_dir, f"frame_{img_idx:06d}.{self.suffix}")
        self.batch_status.append(self._status)
        self.drivered_fnames_list.append(f_file_name) # Changed

        # 这里有两个逻辑需要推理：
        # 1. 如果批次满了，则进行推理（包括全部都是silence的情况）
        # 2. 如果当前状态不是data，但是本批次中有data状态，则进行推理，清理完本批次数据 （这种情况基本就是刚刚切换到了静音状态，但是音频数据还在）
        if len(self.wenet_feature_list) == self.batch_size or (self._status != "data" and all(status == "data" for status in self.batch_status)):
            self.process_batch()


    # 批次推理
    def process_batch(self,):
        if len(self.wenet_feature_list) == 0:
            self.init_batch_data()
            return
        ####################################音频特征转numpy####################################################
        t_get1 = time.time()
        audio_feature_list = [np.array(w,dtype=np.float32) for w in self.wenet_feature_list]  # 将音频特征列表转换回 numpy 数组
        frameId = self.batch_aud_idx_list[-1] if len(self.batch_aud_idx_list)>0 else -1
        t_get2 = time.time()
        # logger.info(f"【audio转numpy】: {(t_get2 - t_get1)*1000:.3f}ms") 
        
        ####################################保存/读取人脸数据####################################################
        drivered_face_dict,no_face_indices = _get_drivered_face_dict(self.history_data,self.models_dir,self.batch_img_idx_list)
        # 如果  img_list 为 None。这将由后续加载帧的 write_video 函数处理。
        face_params = _get_face_params_from_drivered_face_dict(drivered_face_dict,self.original_frame_shape_init, self.output_resize) 
        t_get3 = time.time()
        logger.info(f"【获取人脸数据】: {(t_get3 - t_get2)*1000:.3f}ms")
        ################################## 开始推理结果####################################################
        t6 = time.time()
        silence_find = False  # 兼容静音模式
        img_name_list = []
        if all(status == "silence" for status in self.batch_status): # 优先读取静音数据，只有所有的数据都是静音状态的情况下才读取
            img_name_list = [os.path.basename(img_name) for img_name in self.drivered_fnames_list]
            silence_find,processed_raw_face_regions = read_silence_blend_imgs(self.silence_img_dir,img_name_list,self.use_npy)

        if not silence_find: # 正常推理
            processed_raw_face_regions = get_blend_imgs(
                self.batch_size, 
                audio_feature_list, 
                drivered_face_dict, 
                GlobalConfig.instance().blend_dynamic, 
                face_params,
                self.digital_human_model, 
                frameId) # 仅返回来自神经网络的原始人脸区域

            if not silence_find:
                # 写入静音数据
                write_silence_blend_imgs(self.silence_img_dir,img_name_list,processed_raw_face_regions,self.use_npy)

        ################################## 发送数据 ####################################################
        # 写入视频所需数据: 文件名列表、拼接参数、原始人脸区域、批次中无人脸的索引、是否使用npy格式
        t_get4 = time.time()
        # logger.info(f"【推理】: {(t_get4 - t_get3)*1000:.3f}ms")
        output_package = (self.drivered_fnames_list, face_params, processed_raw_face_regions, no_face_indices, self.use_npy_flag)
        self.output_imgs_queue.put([self._status, 0, output_package]) # status_code, reason_placeholder, data_tuple
        t_get5 = time.time()
        # logger.info(f"【发送数据】: {(t_get5 - t_get4)*1000:.3f}ms")
        real_fps = 1/(time.time() - self.tlast) 
        self.tlast = time.time()
        # logger.info(f"【real FPS】:{real_fps:.2f}")

        self.infer_time.append(time.time() - t_get1)
        self.batch_num += 1
        if self.batch_num %5 == 0:
            logger.info(f'>>> audio_transfer 第 {self.batch_num} 批数据推理{len(self.drivered_fnames_list)}帧, 耗时 {time.time() - t_get1:.3f}s, FPS:{len(self.drivered_fnames_list)/(max(1e-6,time.time() - t_get1)):.2f}')
        
        # 清空批次数据
        self.init_batch_data()

 
    def finish(self, error=None):
        logger.info(f"[{self.work_id_for_logging}] DhStreamProcessor finishing. Putting sentinel into output queue.")
        e = error if error is not None else 0
        self.output_imgs_queue.put([e,e,None])

    def silence_process(self):
        silence_num = 0
        silence_start_time = time.time()
        frame_time = int(1000/(self.fps+1))   #1000ms/25fps = 40ms
        silence_data = np.zeros((20, 256),dtype=np.float32)

        while self._status == "silence":
            # 如果有数据过来就更换状态
            if self.feature_queue.qsize() > 0:
                _status, data = self.feature_queue.get(timeout=1)
                logger.info(f"【静音阶段】 接收到新的状态: {_status}")
                self._status = _status
                self.process_state(data)

            # 如果输出队列的帧数小于缓存帧数-batch_size，就需要继续缓存
            elif self.output_imgs_queue.qsize() <self.silence_buffer_batch_num:
                while len(self.wenet_feature_list) < self.batch_size:
                    self.process_feature_chunk(silence_data)
  
            time.sleep(frame_time)

        logger.info(f"【静音阶段结束】 本次静音时长：{time.time() - silence_start_time:.3f}s")


def dh_stream_processor(task_queue, feature_queue, output_imgs_queue, batch_size=4, use_npy=False):
    processor = DhStreamProcessor(feature_queue, output_imgs_queue, batch_size, use_npy)
    while True:
        try:
            task_info = task_queue.get(block=True)
            if task_info is None:
                logger.info("【dh_stream_processor】 结束信号接收, 退出.")
                break
            processor.init_task(task_info).run()
        except Exception as e:
            work_id = task_info.get('work_id', 'unknown')
            logger.error(f"【dh_stream_processor】【{work_id}】 处理失败: {e}", exc_info=True)
            if processor:
                processor.finish(error=True)

# endregion
# ==================================================================================================


# ==================================================================================================
# region: Other Helper functions (Maintained)
# ==================================================================================================
def _get_chaofen_src_imgs(code,is_train,img_list, digital_human_model, scrfd_detector, frameId,batch_img_idx_list , batch_aud_idx_list, speaker_id,total_video_frame,use_npy=False):
    # This function seems complex and related to data preparation. Keeping as is.
    return digital_human_model.get_chaofen_src_imgs(code,is_train,img_list, scrfd_detector, frameId,batch_img_idx_list , batch_aud_idx_list, speaker_id,total_video_frame,use_npy)

def _get_drivered_face_dict(history_data,save_data_dir,batch_img_idx_list):
    # todo: crop_img 兼容npy 和 jpg
    # 训练模式：保存关键点和人脸框信息为npy文件
    face_data_dir = os.path.join(save_data_dir, 'face_data')  # 创建face_data目录保存人脸关键数据
    os.makedirs(face_data_dir, exist_ok=True)
    crop_img_dir = os.path.join(face_data_dir, 'crop_img')   # 创建crop_img目录保存裁剪的人脸图像
    wh_file = os.path.join(save_data_dir, 'wh_value.txt')
    os.makedirs(crop_img_dir, exist_ok=True)

    # 非训练模式：尝试从已保存的数据加载并使用
    if os.path.exists(wh_file):
        drivered_face_dict = {}
        no_face_indices = []
        if len(history_data) > 0: # 从文件加载历史数据
            for i,img_idx in enumerate(batch_img_idx_list):
                drivered_face_dict[i] = history_data.get('{}'.format(img_idx),{})
                
                for k in ['bounding_box','bounding_box_p','landmarks','crop_lm']:
                    if k in drivered_face_dict[i] and isinstance(drivered_face_dict[i][k], list):
                        drivered_face_dict[i][k] = np.array(drivered_face_dict[i][k])
                if drivered_face_dict[i].get('no_face',True):
                    no_face_indices.append(i)
 
                frame_path = os.path.join(face_data_dir, 'crop_img', f'frame_{img_idx:06d}.jpg')
                if os.path.exists(frame_path):
                    img = cv2.imread(frame_path)
                    drivered_face_dict[i]['crop_img'] = img
                else:
                    drivered_face_dict[i]['crop_img'] = None
        else:
            no_face_indices = [0,1,2,3]

            
    return drivered_face_dict,no_face_indices


def _get_face_params_from_drivered_face_dict(drivered_face_dict,out_shape, output_resize):
    x1_list, x2_list, y1_list, y2_list = ([], [], [], [])
    get_face_params_time1 = time.time()
    for idx in range(len(drivered_face_dict)):
        facebox = drivered_face_dict[idx]['bounding_box']
        x1_list.append(facebox[0])
        x2_list.append(facebox[1])
        y1_list.append(facebox[2])
        y2_list.append(facebox[3])
    drivered_exceptlist = []
    get_face_params_time2 = time.time()
    frame_len = len(drivered_face_dict.keys())
    for i in range(frame_len):
        if len(drivered_face_dict[i]['bounding_box_p']) == 4:
            break
        drivered_exceptlist.append(i)
        print(drivered_exceptlist, '-------------------------------------')
    get_face_params_time3 = time.time()
    for i in drivered_exceptlist:
        drivered_face_dict[i]['bounding_box_p'] = drivered_face_dict[len(drivered_exceptlist)]['bounding_box_p']
        drivered_face_dict[i]['bounding_box'] = drivered_face_dict[len(drivered_exceptlist)]['bounding_box']
        drivered_face_dict[i]['crop_lm'] = drivered_face_dict[len(drivered_exceptlist)]['crop_lm']
        drivered_face_dict[i]['crop_img'] = drivered_face_dict[len(drivered_exceptlist)]['crop_img']
    get_face_params_time4 = time.time()
    keylist = list(drivered_face_dict.keys())
    keylist.sort()
    get_face_params_time5 = time.time()
    for it in keylist:
        if len(drivered_face_dict[it]['bounding_box_p']) != 4:
            # print(it, '++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++')
            drivered_face_dict[it]['bounding_box_p'] = drivered_face_dict[it - 1]['bounding_box_p']
            drivered_face_dict[it]['bounding_box'] = drivered_face_dict[it - 1]['bounding_box']
            drivered_face_dict[it]['crop_lm'] = drivered_face_dict[it - 1]['crop_lm']
            drivered_face_dict[it]['crop_img'] = drivered_face_dict[it - 1]['crop_img']
    # get_face_params_time6 = time.time()
    face_params = [out_shape, output_resize,  y1_list, y2_list, x1_list, x2_list]
    # get_face_params_time7 = time.time()
    # logger.info(f"获取人脸参数耗时: 7-6：{get_face_params_time7 - get_face_params_time6:.6f}s，6-5：{get_face_params_time6 - get_face_params_time5:.6f}s，5-4：{get_face_params_time5 - get_face_params_time4:.6f}s，4-3：{get_face_params_time4 - get_face_params_time3:.6f}s，3-2：{get_face_params_time3 - get_face_params_time2:.6f}s，2-1：{get_face_params_time2 - get_face_params_time1:.6f}s")
    return face_params


def write_video(output_imgs_queue, temp_dir, result_dir, work_id, audio_path, result_queue, width, height, fps, watermark_switch=0, digital_auth=0,use_npy=False):
    # This function is for video writing, seems independent and can be kept.
    out_path = os.path.join(temp_dir, f'{work_id}.mp4')
    video_writer = CVVideoWriterThread(out_path, (width, height), fps)
    video_writer.start()
    img_count = 0
    while True:
        try:
            img = output_imgs_queue.get(timeout=60)
            if img is None:
                break
            video_writer.write(img)
            img_count += 1
        except Empty:
            logger.error(f"[{work_id}] Timeout waiting for image from queue. Video might be incomplete.")
            break
    video_writer.release()
    logger.info(f"[{work_id}] Wrote {img_count} frames to temporary video file.")
    
    final_path = os.path.join(result_dir, f'{work_id}.mp4')
    cmd = f'ffmpeg -y -i "{out_path}" -i "{audio_path}" -c:v copy -c:a aac -strict experimental "{final_path}"'
    subprocess.call(cmd, shell=True)
    
    result_queue.put(final_path)

# endregion
# ==================================================================================================


# ==================================================================================================
# region: Main Task Class
# ==================================================================================================
class Status(Enum):
    run = 1
    success = 2
    error = 3

class TransDhTask(object):
    _instance = None
    
    @classmethod
    def instance(cls, *args, **kwargs):
        if not cls._instance:
            cls._instance = cls(*args, **kwargs)
        return cls._instance

    def __init__(self, *args, **kwargs):
        logger.info("TransDhTask instance creating...")
        self.is_working = False
        self.current_work_id = None
        self.batch_size = kwargs.get('batch_size', 8)
        self.use_npy = kwargs.get('use_npy', False)
        
        # Load models once

        self.gfpgan = None # Lazy load if needed
        self.fd = None # Lazy load if needed
        self.face_blur_detect = None # Lazy load if needed

    def work(self, audio_url, video_url, code, watermark_switch, speaker_id, is_train, digital_auth, chaofen, pn, is_stream=False):
        work_id = code
        if self.is_working:
            logger.warning(f"Task {self.current_work_id} is already running. Rejecting new task {work_id}.")
            return
        
        self.is_working = True
        self.current_work_id = work_id
        
        try:
            temp_dir = os.path.join('data/temp', work_id)
            os.makedirs(temp_dir, exist_ok=True)
            result_dir = 'data/result'
            os.makedirs(result_dir, exist_ok=True)
            self.change_task_status(code, Status.run, 1, '')
            logger.info(f"[{work_id}] 任务开始")
            
            audio_path, drivered_path, fourcc = self.preprocess(audio_url, video_url, code)
            video_info = get_video_info(drivered_path, os.path.join(os.path.dirname(drivered_path), 'video_info.json'))
            width, height, fps = video_info['width'], video_info['height'], video_info['fps']

            # --- Setup Queues and Processes ---
            self.task_queue_audio = multiprocessing.Queue(1)
            self.task_queue_dh = multiprocessing.Queue(1)
            self.feature_queue = multiprocessing.Queue(self.batch_size * 8)  # 32
            self.output_imgs_queue = multiprocessing.Queue(self.batch_size * 8) # 32
            self.audio_stream_queue = multiprocessing.Queue(100) if is_stream else None
            self.result_queue = multiprocessing.Queue(1)

            task_info = {
                'work_id': work_id, 'audio_path': audio_path, 'drivered_path': drivered_path,
                'is_stream': is_stream, 'wenet_model': self.wenet_model, 'digital_human_model': self.digital_human_model,
                'gfpgan': self.gfpgan, 'fd': self.fd, 'face_blur_detect': self.face_blur_detect,
                'is_train': is_train, 'speaker_id': speaker_id, 'code': code, 'fps': fps
            }

            self.audio_process = Process(target=audio_processor, args=(self.task_queue_audio, self.feature_queue, self.audio_stream_queue))
            self.dh_process = Process(target=dh_stream_processor, args=(self.task_queue_dh, self.feature_queue, self.output_imgs_queue, self.batch_size, self.use_npy))
            self.write_video_process = Process(target=write_video, args=(self.output_imgs_queue, temp_dir, result_dir, work_id, audio_path, self.result_queue, width, height, fps, watermark_switch, digital_auth, self.use_npy))

            self.audio_process.start()
            self.dh_process.start()
            self.write_video_process.start()

            self.task_queue_audio.put(task_info)
            self.task_queue_dh.put(task_info)

            # --- Wait for result ---
            final_result_url = self.result_queue.get()
            logger.info(f'[{work_id}] result_queue.get() successfully')

            # --- Cleanup ---
            self.audio_process.join(timeout=5)
            self.dh_process.join(timeout=5)
            self.write_video_process.join(timeout=5)
            
            if final_result_url and 'error' not in final_result_url:
                self.change_task_status(code, Status.success, 100, final_result_url)
            else:
                raise CustomError(final_result_url or "未知错误导致任务失败")
                
        except Exception as e:
            logger.error(f"[{work_id}] 任务异常: {e}", exc_info=True)
            self.change_task_status(code, Status.error, 100, '', msg=str(e))
        finally:
            self.is_working = False
            self.current_work_id = None
            # Terminate any lingering processes
            for p_name in ['audio_process', 'dh_process', 'write_video_process']:
                p = getattr(self, p_name, None)
                if p and p.is_alive():
                    p.terminate()
            repost_host()
            logger.info(f"[{work_id}] 任务结束")

    def feed_audio_chunk(self, work_id, audio_chunk):
        """
        外部调用，用于喂入音频流数据块。
        如果 audio_chunk is None，表示流结束。
        """
        if not self.is_working or work_id != self.current_work_id:
            logger.warning(f"Task {work_id} is not the current running task. Ignoring audio chunk.")
            return

        if hasattr(self, 'audio_stream_queue') and self.audio_stream_queue is not None:
            try:
                if audio_chunk:
                    # 发送数据块
                    self.audio_stream_queue.put(("data", audio_chunk), timeout=1)
                else:
                    # 发送结束信号
                    logger.info(f"[{work_id}] Feeding end-of-stream signal to audio queue.")
                    self.audio_stream_queue.put(("silence", None), timeout=1)
            except Full:
                logger.warning(f"[{work_id}] Audio stream queue is full. Dropping chunk.")
        else:
            logger.error(f"[{work_id}] audio_stream_queue is not initialized or task is not in stream mode.")

    def preprocess(self, audio_url, video_url, code):
        temp_dir = os.path.join('data/temp', code)
        os.makedirs(temp_dir, exist_ok=True)
        # Download files
        audio_path = download_file(audio_url, temp_dir)
        video_path = download_file(video_url, temp_dir)
        
        # This part seems to format/prepare video, let's assume it's correct
        formatted_video_path, fourcc = format_video_audio(code, video_path, audio_path, "mp4v")
        
        # Here, it seems driver data (landmarks etc) should be prepared if not exists.
        # This logic was complex in original file, let's assume a helper `init_wh` does this.
        init_wh(code, formatted_video_path) # This will prepare lm.npy etc.

        return audio_path, formatted_video_path, fourcc

    def change_task_status(self, code, status: Status, progress: int, result: str, msg=''):
        logger.info(f"[{code}] Status Change: {status.name}, Progress: {progress}%, Result: {result}, Msg: {msg}")
        # Placeholder for actual status reporting logic (e.g., API call, DB update)

def format_video_audio(code, video_path, audio_path, fourcc):
    # This function seems important for preprocessing
    return op.format_video_audio(code, video_path, audio_path, fourcc)

def init_wh(code, drivered_path):
    # This function seems important for preprocessing
    return op.init_wh(code, drivered_path)

# endregion
# ================================================================================================== 