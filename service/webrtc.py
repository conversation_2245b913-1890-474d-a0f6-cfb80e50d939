# -*-coding: utf-8 -*-
# @Time :2025-08-06
# <AUTHOR> lishengjing
# @Email : <EMAIL>
# @File : webrtc.py


import os,sys,time,fractions,json,uuid,threading,traceback,cv2,asyncio

import numpy as np
from weakref import WeakSet
from av import VideoFrame, AudioFrame
from av.frame import Frame
from av.packet import Packet
from typing import Tuple, Dict, Optional, Set, Union

from loguru import logger
from aiohttp import web
from aiortc import RTCPeerConnection, RTCSessionDescription
from aiortc.contrib.media import MediaStreamTrack

# 导入基础类
from .base_webrtc import BaseWebRTCStreamer





def logger_error(E):
    def locate_exception():
        """
        locate error filename object line
        """
        exc_type, exc_obj, exc_tb = sys.exc_info()
        if exc_tb is not None:
            fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
            return f"{exc_type.__name__}, in {fname}, line {exc_tb.tb_lineno}"
        else:
            return "No active exception"
    
    error_location = locate_exception()
    logger.error(f"【Error Location】: {error_location}")
    logger.error(f"【Error Details】: {E}")



class ImprovedMediaRelayTrack(MediaStreamTrack):
    """
    基于PlayerStreamTrack改进的媒体轨道，具有精确的时间戳控制和帧率管理
    """
    def __init__(self, kind, maxsize=8, fps=25, sample_rate=16000):
        super().__init__()
        self.kind = kind
        self._queue = asyncio.Queue(maxsize=maxsize)
        self._drop_count = 0  # 丢帧计数

        # 时间戳管理 - 基于PlayerStreamTrack的设计
        self._start = None
        self._timestamp = None
        self.timelist = []  # 记录最近包的时间戳

        # 帧率和采样率配置
        self._fps = fps
        self._sample_rate = sample_rate

        # 时间基准设置
        # 性能监控
        self.framecount = 0
        self.lasttime = time.perf_counter()
        self.totaltime = 0
        if kind == 'video':
            self.VIDEO_CLOCK_RATE = 90000
            self.VIDEO_PTIME = 1 / fps
            self.VIDEO_TIME_BASE = fractions.Fraction(1, self.VIDEO_CLOCK_RATE)

        else:  # audio
            self.AUDIO_PTIME = 0.020  # 20ms audio packetization
            self.AUDIO_TIME_BASE = fractions.Fraction(1, sample_rate)

    async def next_timestamp(self):
        """基于PlayerStreamTrack的时间戳生成逻辑"""
        if self.readyState != "live":
            raise Exception("Track not live")

        if self.kind == 'video':
            if hasattr(self, "_timestamp") and self._timestamp is not None:
                # 增量时间戳计算
                self._timestamp += int(self.VIDEO_PTIME * self.VIDEO_CLOCK_RATE)
                # 计算等待时间以控制帧率
                wait = self._start + (self._timestamp / self.VIDEO_CLOCK_RATE) - time.time()
                if wait > 0:
                    await asyncio.sleep(wait)
            else:
                # 初始化时间戳
                self._start = time.time()
                self._timestamp = 0
                self.timelist.append(self._start)
                logger.info(f"【ImprovedMediaRelayTrack】视频轨道开始时间: {self._start}")
            return self._timestamp, self.VIDEO_TIME_BASE
        else:  # audio
            if hasattr(self, "_timestamp") and self._timestamp is not None:
                # 增量时间戳计算
                self._timestamp += int(self.AUDIO_PTIME * self._sample_rate)
                # 计算等待时间以控制音频包发送速率
                wait = self._start + (self._timestamp / self._sample_rate) - time.time()
                if wait > 0:
                    await asyncio.sleep(wait)
            else:
                # 初始化时间戳
                self._start = time.time()
                self._timestamp = 0
                self.timelist.append(self._start)
                logger.info(f"【ImprovedMediaRelayTrack】音频轨道开始时间: {self._start}")
            return self._timestamp, self.AUDIO_TIME_BASE

    async def recv(self):
        """aiortc调用此方法来获取下一帧以发送给客户端。"""
        logger.info(f"【{self.kind}轨道】recv被调用，队列大小: {self._queue.qsize()}")
        
        frame = await self._queue.get()

        # 设置精确的时间戳
        pts, time_base = await self.next_timestamp()
        frame.pts = pts
        frame.time_base = time_base
        
        # 性能监控（仅视频）
        if self.kind == 'video':
            self.totaltime += (time.perf_counter() - self.lasttime)
            self.framecount += 1
            self.lasttime = time.perf_counter()
            if self.framecount == 100:
                actual_fps = self.framecount / self.totaltime
                logger.info(f"【ImprovedMediaRelayTrack】实际视频FPS: {actual_fps:.2f}")
                self.framecount = 0
                self.totaltime = 0
        if self.kind == 'audio':
            self.totaltime += (time.perf_counter() - self.lasttime)
            self.framecount += 1
            self.lasttime = time.perf_counter()
            if self.framecount == 100:
                actual_fps = self.framecount / self.totaltime
                logger.info(f"【ImprovedMediaRelayTrack】实际音频FPS: {actual_fps:.2f}")
                self.framecount = 0
                self.totaltime = 0

        return frame


    def stop(self):
        super().stop()
        if self._player is not None:
            self._player._stop(self)
            self._player = None



class WebRTCStreamer(BaseWebRTCStreamer):
    """
    WebRTC推流器实现类，继承自BaseWebRTCStreamer
    提供具体的媒体处理和推流功能
    """
    def __init__(self, signaling_url, audio_sample_rate=16000, video_fps=25, target_audio_sample_rate=48000, target_audio_channels=2):
        # 调用父类初始化
        super().__init__(signaling_url, audio_sample_rate, video_fps)

        # 音频参数配置
        self._input_audio_sample_rate = audio_sample_rate
        self._output_audio_sample_rate = target_audio_sample_rate
        self.target_audio_channels = target_audio_channels

        # 音频分片配置
        self.audio_chunk_size = int(self._input_audio_sample_rate * 0.02)  # 输入侧20ms
        self.output_audio_chunk_size = int(self._output_audio_sample_rate * 0.02)  # 输出侧20ms

        # 音频缓冲区
        self._audio_buffer = []
        self._audio_buffer_debug = []

        # 性能优化参数
        self._max_video_bitrate = 10000000  # 10Mbps 视频码率
        self._max_audio_bitrate = 128000

        # 未连接时的帧率控制
        self._start_time = time.time()
        self._timestamp = 0
        self.VIDEO_CLOCK_RATE = 90000
        self.VIDEO_PTIME = 1 / self._video_fps
        self.VIDEO_TIME_BASE = fractions.Fraction(1, self.VIDEO_CLOCK_RATE)
        self.not_connected_count = 0

        logger.info(f"【WebRTCStreamer】初始化完成: 输入音频{self._input_audio_sample_rate}Hz -> 输出音频{self._output_audio_sample_rate}Hz")

        # 连接状态检测增强
        self._last_connection_check = time.time()
        self._connection_check_interval = 5.0  # 每5秒主动检查一次连接状态
        self._connection_timeout = 30.0  # 30秒无活动则认为连接超时
        self._last_frame_time = {}  # 记录每个连接的最后活动时间

    def _create_media_tracks(self):
        """创建媒体轨道（实现基类的抽象方法）"""
        self._video_track = ImprovedMediaRelayTrack(
            kind="video",
            maxsize=8,
            fps=self._video_fps
        )
        self._audio_track = ImprovedMediaRelayTrack(
            kind="audio",
            maxsize=16,  # 音频每帧是20ms，一帧视频对应2帧音频
            sample_rate=self._output_audio_sample_rate
        )
        logger.info(f"【WebRTCStreamer】媒体轨道创建完成: 视频{self._video_fps}fps, 音频{self._output_audio_sample_rate}Hz")

    def _check_connection_health(self):
        """主动检查连接健康状态"""
        current_time = time.time()

        # 检查是否需要进行连接状态检查
        if current_time - self._last_connection_check < self._connection_check_interval:
            return

        self._last_connection_check = current_time

        # 检查所有连接的状态
        active_connections = []
        stale_connections = []

        for pc in list(self._pcs):  # 使用list()避免在迭代时修改WeakSet
            try:
                pc_id = id(pc)  # 使用对象ID作为连接标识

                # 检查连接状态
                is_connection_active = (
                    pc.connectionState == "connected" and
                    pc.iceConnectionState in ["connected", "completed"]
                )

                # 检查是否有最近的活动
                last_activity = self._last_frame_time.get(pc_id, 0)
                time_since_activity = current_time - last_activity

                if is_connection_active:
                    # 连接状态正常，更新活动时间
                    self._last_frame_time[pc_id] = current_time
                    active_connections.append(pc)
                    logger.debug(f"【WebRTC健康检查】连接 {pc_id} 状态正常")
                elif pc.connectionState in ["failed", "closed", "disconnected"]:
                    # 连接明确断开
                    logger.warning(f"【WebRTC健康检查】连接 {pc_id} 已断开: {pc.connectionState}")
                    stale_connections.append(pc)
                elif time_since_activity > self._connection_timeout:
                    # 连接超时
                    logger.warning(f"【WebRTC健康检查】连接 {pc_id} 超时: {time_since_activity:.1f}s 无活动")
                    stale_connections.append(pc)
                else:
                    # 连接状态不确定，但还没超时，暂时保留
                    logger.debug(f"【WebRTC健康检查】连接 {pc_id} 状态待定: {pc.connectionState}/{pc.iceConnectionState}")
                    active_connections.append(pc)

            except Exception as e:
                logger.error(f"【WebRTC健康检查】检查连接时出错: {e}")
                stale_connections.append(pc)

        # 清理无效连接
        for pc in stale_connections:
            pc_id = id(pc)
            self._pcs.discard(pc)
            self._last_frame_time.pop(pc_id, None)
            try:
                if not pc.connectionState in ["closed"]:
                    asyncio.run_coroutine_threadsafe(pc.close(), self._loop)
            except Exception as e:
                logger.debug(f"【WebRTC健康检查】关闭连接时出错: {e}")

        # 更新连接状态
        old_connected = self.is_connected
        self.is_connected = len(active_connections) > 0

        if old_connected != self.is_connected:
            if self.is_connected:
                logger.info(f"【WebRTC健康检查】✅ 检测到活跃连接: {len(active_connections)}个")
            else:
                logger.warning(f"【WebRTC健康检查】❌ 所有连接已断开，清理缓冲区")
                # 清理音频缓冲区
                self._audio_buffer.clear()
                self._audio_buffer_debug.clear()

        logger.debug(f"【WebRTC健康检查】活跃连接: {len(active_connections)}, 清理连接: {len(stale_connections)}")
        
    def get_total_drop_count(self):
        """获取总的丢帧统计，包括视频和音频轨道的丢帧"""
        total_drops = self._drop_count
        
        # 加上视频轨道的丢帧
        if self._video_track and hasattr(self._video_track, '_drop_count'):
            total_drops += self._video_track._drop_count
            
        # 加上音频轨道的丢帧
        if self._audio_track and hasattr(self._audio_track, '_drop_count'):
            total_drops += self._audio_track._drop_count
            
        return total_drops
    
    def get_queue_status(self):
        """获取队列状态信息，用于背压控制"""
        status = {
            'video_queue_size': 0,
            'video_queue_maxsize': 0,
            'audio_queue_size': 0,
            'audio_queue_maxsize': 0,
            'video_full': False,
            'audio_full': False
        }
        
        if self._video_track and hasattr(self._video_track, '_queue'):
            status['video_queue_size'] = self._video_track._queue.qsize()
            status['video_queue_maxsize'] = self._video_track._queue.maxsize
            status['video_full'] = self._video_track._queue.full()
            
        if self._audio_track and hasattr(self._audio_track, '_queue'):
            status['audio_queue_size'] = self._audio_track._queue.qsize()
            status['audio_queue_maxsize'] = self._audio_track._queue.maxsize
            status['audio_full'] = self._audio_track._queue.full()
            
        return status
    
    def is_queue_full(self):
        """检查推流队列是否已满"""
        if not self.is_running:
            return False
            
        status = self.get_queue_status()
        # 如果视频或音频队列中任何一个满了，就认为队列满了
        # print(status)
        return status['video_full'] or status['audio_full']
    
    def get_connection_status(self):
        """获取连接状态信息"""
        return {
            'is_connected': self.is_connected,
            'peer_connections': len(self._pcs),
            'is_running': self.is_running,
            'signaling_url': f"http://{self._host}:{self._port}/offer"
        }

    # start() 和 stop() 方法由基类 BaseWebRTCStreamer 提供

    def write_frame(self, bgr_frame, audio_data):
        if not self.is_running or not self._loop:
            return

        # 主动检查连接健康状态
        self._check_connection_health()
        
        # 没有连接时候的处理，主要是为了控制帧率
        if not self.is_connected:
            self._not_connected_process()
            return
        else:
            # 有数据，删除这个属性，保证下次写入帧时，不会因为时间戳问题导致帧率不准确
            delattr(self, "_timestamp") if hasattr(self, "_timestamp") else None
            
        # 基于队列状态的背压控制：在放入帧之前检查队列状态
        wait_count = 0
        while self.is_queue_full() and self.is_running and self.is_connected:
            # 队列满时等待，实现背压机制
            time.sleep(0.02) 
            wait_count += 1
            if not self.is_running:
                logger.warning("【WebRTC】推流器已停止，跳过帧处理")
                break
            
            # 每等待1秒输出一次日志
            if wait_count % 50 == 0:  # 50 * 20ms = 1秒
                status = self.get_queue_status()
                logger.info(f"【WebRTC背压】等待队列空间: V:{status['video_queue_size']}/{status['video_queue_maxsize']}, "
                          f"A:{status['audio_queue_size']}/{status['audio_queue_maxsize']}, "
                          f"连接状态: {self.is_connected}")
        

        if self._audio_track and audio_data is not None:
            print("1111111111111111111_audio_buffer",len(self._audio_buffer),len(audio_data),self.audio_chunk_size,max(audio_data),min(audio_data),self._audio_sample_rate)
            self._audio_buffer.extend(audio_data)

            while len(self._audio_buffer) >= self.audio_chunk_size and self.is_connected:
                # 在每次循环开始时检查连接状态
                if not self.is_connected:
                    logger.warning("【WebRTC】音频处理中检测到连接断开，停止处理音频数据")
                    break
                    
                audio_chunk = self._audio_buffer[:self.audio_chunk_size]
                # self._audio_buffer_debug.extend(audio_chunk)

                # 1) 将输入16k(或其它输入采样率)的20ms数据重采样到目标输出采样率（默认48k）的20ms长度
                #    使用线性插值避免引入新依赖
                resampled_audio = audio_resample(audio_chunk, self._input_audio_sample_rate, self._output_audio_sample_rate, self.target_audio_channels)
                
                # 检查重采样结果
                if len(resampled_audio) == 0:
                    logger.warning("【WebRTC】重采样结果为空，跳过此帧")
                    self._audio_buffer = self._audio_buffer[self.audio_chunk_size:]
                    continue

                # 2) 浮点[-1,1] -> int16
                audio_int16 = np.clip(resampled_audio, -32768, 32767).astype(np.int16)
                audio_int16 = audio_padding(audio_int16,self.output_audio_chunk_size*self.target_audio_channels)

                # print("!!!!!!!!!!!!!!!!!!!!!!!audio_int16",audio_int16.shape,audio_int16.dtype,audio_int16.max(),audio_int16.min())
                # 3) 推进输入缓冲
                self._audio_buffer = self._audio_buffer[self.audio_chunk_size:]

                # 4) 构造输出AudioFrame（目标48k/立体声）
                layout = 'stereo' if self.target_audio_channels == 2 else 'mono'
                audio_frame = AudioFrame(format='s16', layout=layout, samples=self.output_audio_chunk_size)
                audio_frame.planes[0].update(audio_int16.tobytes())
                audio_frame.sample_rate = self._output_audio_sample_rate
                try_time = 0
                max_try_time = 5  # 100ms，超过一帧的速度
                # 出现过这种情况：当队列满了已经进去循环，而这时候is_connected已经为False，导致队列一直满，一直卡在这里
                while  self._audio_track._queue.full() and max_try_time > try_time and self.is_connected: # 队列未满时，放入队列
                    try_time += 1
                    logger.warning(f"【WebRTC背压】音频队列已满，等待队列空间: {self._audio_track._queue.qsize()}/{self._audio_track._queue.maxsize}")
                    time.sleep(0.02)
                    
                    # 如果连接断开，立即退出等待循环
                    if not self.is_connected:
                        logger.warning("【WebRTC】音频处理中检测到连接断开，停止等待队列空间")
                        break
            
                # 只有在连接仍然有效时才放入队列
                if self.is_connected:
                    asyncio.run_coroutine_threadsafe(self._audio_track._queue.put(audio_frame), self._loop)
                else:
                    logger.warning("【WebRTC】连接已断开，丢弃音频帧")
                    

        if self._video_track and bgr_frame is not None:
            video_frame = VideoFrame.from_ndarray(bgr_frame, format="bgr24")
            # 注意：时间戳现在由ImprovedMediaRelayTrack的recv()方法自动设置
            try_time = 0
            max_try_time = 5  # 100ms
            while  self._video_track._queue.full() and max_try_time > try_time and self.is_connected: # 队列未满时，放入队列
                try_time += 1
                logger.warning(f"【WebRTC背压】视频队列已满，等待队列空间: {self._video_track._queue.qsize()}/{self._video_track._queue.maxsize}")
                time.sleep(0.04)
            if self.is_connected:
                asyncio.run_coroutine_threadsafe(self._video_track._queue.put(video_frame), self._loop)
            else:
                logger.warning("【WebRTC】连接已断开，丢弃视频帧")
            # asyncio.run_coroutine_threadsafe(self._video_track._queue.put_nowait(video_frame), self._loop)  #这样好像不会到下面的循环，好奇怪

        # if  len(self._audio_buffer_debug) > self._input_audio_sample_rate*20:
        #     import soundfile as sf
        #     _audio_buffer_debug = np.array(self._audio_buffer_debug)
        #     sf.write("audio_buffer_debug.wav",_audio_buffer_debug,self._input_audio_sample_rate)
        #     self._audio_buffer_debug = []

    def _validate_connection_status(self):
        """主动验证连接状态，防止事件处理器没有正确触发"""
        try:
            # 检查所有对等连接的状态
            active_connections = []
            for pc in list(self._pcs):  # 使用list()避免在迭代时修改WeakSet
                try:
                    # 检查连接状态和ICE状态
                    # connectionState 应该是 "connected"
                    # iceConnectionState 可以是 "connected" 或 "completed"（都是正常状态）
                    if (pc.connectionState == "connected" and 
                        pc.iceConnectionState in ["connected", "completed"]):
                        active_connections.append(pc)
                    else:
                        # 只有当连接状态明确不是connected，或者ICE状态是明确的断开状态时才标记为无效
                        if (pc.connectionState in ["failed", "closed", "disconnected"] or
                            pc.iceConnectionState in ["failed", "closed", "disconnected"]):
                            logger.warning(f"【WebRTC】检测到无效连接: connectionState={pc.connectionState}, iceConnectionState={pc.iceConnectionState}")
                            # 从集合中移除无效连接
                            self._pcs.discard(pc)
                        else:
                            # 其他状态（如checking、gathering等）暂时保留，不立即移除
                            logger.debug(f"【WebRTC】连接状态待定: connectionState={pc.connectionState}, iceConnectionState={pc.iceConnectionState}")
                            active_connections.append(pc)
                except Exception as e:
                    logger.error(f"【WebRTC】检查连接状态时出错: {e}")
                    # 移除出错的连接
                    self._pcs.discard(pc)
            
            # 更新连接状态
            old_connected = self.is_connected
            self.is_connected = len(active_connections) > 0
            
            if old_connected != self.is_connected:
                if not self.is_connected:
                    logger.warning(f"【WebRTC】主动验证发现连接已断开，连接状态更新为: {self.is_connected}")
                    # 如果连接断开，清空缓冲区
                    self._clear_buffers_on_disconnect()
                else:
                    logger.info(f"【WebRTC】主动验证发现连接已恢复，连接状态更新为: {self.is_connected}")
                    
        except Exception as e:
            logger.error(f"【WebRTC】验证连接状态失败: {e}")

    def _clear_buffers_on_disconnect(self):
        """当连接断开时清空音频和视频缓冲区，避免数据积压"""
        try:
            # 清空音频缓冲区
            if hasattr(self, '_audio_buffer'):
                self._audio_buffer.clear()
                logger.info("【WebRTC】连接断开，已清空音频缓冲区")
            
            # 清空音频调试缓冲区
            if hasattr(self, '_audio_buffer_debug'):
                self._audio_buffer_debug.clear()
                logger.info("【WebRTC】连接断开，已清空音频调试缓冲区")
                
        except Exception as e:
            logger.error(f"【WebRTC】清空缓冲区失败: {e}")

    def _not_connected_process(self):
        # 检查是否有客户端连接，没有连接就直接跳过帧处理
        # todo : 这里需要优化，因为如果客户端连接了，但是没有数据，就会导致帧率不准确
        # todo : 这里需要优化，如果没有连接，应该要删除完队列中的数据
        if hasattr(self, "_timestamp") and self._timestamp is not None:
            # 增量时间戳计算
            self._timestamp += int(self.VIDEO_PTIME * self.VIDEO_CLOCK_RATE)
            # 计算等待时间以控制帧率
            wait = self._start_time + (self._timestamp / self.VIDEO_CLOCK_RATE) - time.time()
            if wait > 0:
                time.sleep(wait)
            self.not_connected_count += 1
        else:
            # 初始化时间戳
            self._start_time = time.time()
            self._timestamp = 0
            self.not_connected_count = 1
            logger.info(f"【WebRTC】暂时没有连接")

            # 清空音视频队列且只是清一次
            def _clear_queue():
                if self._video_track and hasattr(self._video_track, '_queue'):
                    try:
                        while not self._video_track._queue.empty():
                            self._video_track._queue.get_nowait()
                    except Exception as e:
                        logger.error(f"【WebRTC】清空视频队列失败: {e}")

                if self._audio_track and hasattr(self._audio_track, '_queue'):  
                    try:
                        while not self._audio_track._queue.empty():
                            self._audio_track._queue.get_nowait()
                    except Exception as e:
                        logger.error(f"【WebRTC】清空音频队列失败: {e}")

            _clear_queue()

        if self.not_connected_count % 250 == 0:
            logger.warning(f"【WebRTC】没有客户端连接，跳过帧处理:{self.not_connected_count}")

        
    # _run_event_loop() 方法由基类 BaseWebRTCStreamer 提供

    # _run_server() 方法由基类 BaseWebRTCStreamer 提供
    def _setup_peer_connection_handlers(self, pc: RTCPeerConnection):
        """重写基类方法，添加特定的连接状态处理逻辑"""
        @pc.on("connectionstatechange")
        async def on_connectionstatechange():
            logger.info(f"【WebRTCStreamer】对等连接状态: {pc.connectionState}")
            if pc.connectionState in ("failed", "closed", "disconnected"):
                await pc.close()
                self._pcs.discard(pc)

            # 更新连接状态 - 这是WebRTCStreamer特有的逻辑
            old_connected = self.is_connected
            self.is_connected = any(p.connectionState == "connected" for p in self._pcs)

            if old_connected != self.is_connected:
                if self.is_connected:
                    logger.info(f"【WebRTCStreamer】✅ 客户端已连接！当前连接数: {len(self._pcs)}")
                else:
                    logger.warning(f"【WebRTCStreamer】❌ 客户端已断开！当前连接数: {len(self._pcs)}")
                    self.is_connected = False

        @pc.on("iceconnectionstatechange")
        async def on_iceconnectionstatechange():
            logger.info(f"【WebRTCStreamer】ICE连接状态: {pc.iceConnectionState}")
            
            # 当ICE连接状态变为closed、failed、disconnected时，检查连接状态
            # 注意：checking和completed都是正常状态，不应该触发断开检查
            if pc.iceConnectionState in ("closed", "failed", "disconnected"):
                old_connected = self.is_connected
                
                # 更合理的连接状态检查：当连接状态是connected且ICE状态是connected或completed时认为连接有效
                self.is_connected = any(
                    p.connectionState == "connected" and 
                    p.iceConnectionState in ["connected", "completed"]
                    for p in self._pcs
                )
                
                if old_connected != self.is_connected:
                    if not self.is_connected:
                        logger.warning(f"【WebRTCStreamer】❌ ICE连接断开，连接状态更新为: {self.is_connected}")
                        # 如果连接断开，清空音频和视频缓冲区，避免数据积压
                        self._clear_buffers_on_disconnect()
                    else:
                        logger.info(f"【WebRTCStreamer】✅ ICE连接恢复，连接状态更新为: {self.is_connected}")

        @pc.on("icegatheringstatechange")
        async def on_icegatheringstatechange():
            logger.info(f"【WebRTCStreamer】ICE收集状态: {pc.iceGatheringState}")

    # _handle_offer(), _create_peer_connection(), _shutdown() 方法由基类提供



def audio_resample(src, src_sample_rate, dst_sample_rate, dst_channels=1):
    """
    音频重采样和声道转换
    Args:
        src: 输入音频数据 (float32, [-1, 1])
        src_sample_rate: 输入采样率
        dst_sample_rate: 输出采样率
        dst_channels: 输出声道数 (1=单声道, 2=双声道)
    Returns:
        重采样后的音频数据 (float32, [-1, 1])
    """
    src = np.asarray(src, dtype=np.float32)
    if len(src) == 0:
        return np.array([], dtype=np.float32)
    
    # 计算目标长度
    dst_len = int(len(src) * dst_sample_rate / src_sample_rate)
    
    # 如果长度相同，直接进行声道转换
    if len(src) == dst_len:
        resampled = src
    else:
        # 重采样
        src_idx = np.linspace(0, len(src) - 1, num=len(src), dtype=np.float32)
        dst_idx = np.linspace(0, len(src) - 1, num=dst_len, dtype=np.float32)
        resampled = np.interp(dst_idx, src_idx, src).astype(np.float32)
    
    # 声道转换
    if dst_channels == 2:
        # 双声道：复制单声道数据到左右声道，生成交错格式 [L, R, L, R, ...]
        stereo_data = np.column_stack((resampled, resampled))
        return stereo_data.reshape(-1)  # 展平为交错格式
    else:
        # 单声道：直接返回
        return resampled
    
def audio_padding(audio_int16,expected_samples):
      # 验证数据长度

    if len(audio_int16) != expected_samples:
        logger.warning(f"【WebRTC】音频数据长度不匹配: 期望{expected_samples}, 实际{len(audio_int16)}")
        # 如果长度不匹配，进行截断或填充
        if len(audio_int16) > expected_samples:
            audio_int16 = audio_int16[:expected_samples]
        else:
            # 填充零
            padding = np.zeros(expected_samples - len(audio_int16), dtype=np.int16)
            audio_int16 = np.concatenate([audio_int16, padding])
    return audio_int16
    