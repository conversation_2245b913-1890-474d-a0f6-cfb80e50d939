# -*-coding: utf-8 -*-
# @Time :2025-08-06
# <AUTHOR> lishengjing
# @Email : <EMAIL>
# @File : webrtc.py


import os,sys,time,fractions,json,uuid,threading,traceback,cv2,asyncio

import numpy as np
from weakref import WeakSet
from av import VideoFrame, AudioFrame
from av.frame import Frame
from av.packet import Packet
from typing import Tuple, Dict, Optional, Set, Union

from loguru import logger
from aiohttp import web
from aiortc import RTCPeerConnection, RTCSessionDescription
from aiortc.contrib.media import MediaStreamTrack

# 导入基础类
from .base_webrtc import BaseWebRTCStreamer


class ImprovedMediaRelayTrack(MediaStreamTrack):
    """
    基于PlayerStreamTrack改进的媒体轨道，具有精确的时间戳控制和帧率管理
    """
    def __init__(self, kind, maxsize=8, fps=25, sample_rate=16000):
        super().__init__()
        self.kind = kind
        self._queue = asyncio.Queue(maxsize=maxsize)
        self._drop_count = 0  # 丢帧计数
        
        # 时间戳管理 - 基于PlayerStreamTrack的设计
        self._start = None
        self._timestamp = None
        self.timelist = []  # 记录最近包的时间戳
        
        # 帧率和采样率配置
        self._fps = fps
        self._sample_rate = sample_rate
        
        # 时间基准设置
        if kind == 'video':
            self.VIDEO_CLOCK_RATE = 90000
            self.VIDEO_PTIME = 1 / fps
            self.VIDEO_TIME_BASE = fractions.Fraction(1, self.VIDEO_CLOCK_RATE)
            # 性能监控
            self.framecount = 0
            self.lasttime = time.perf_counter()
            self.totaltime = 0
        else:  # audio
            self.AUDIO_PTIME = 0.020  # 20ms audio packetization
            self.AUDIO_TIME_BASE = fractions.Fraction(1, sample_rate)

    async def next_timestamp(self):
        """基于PlayerStreamTrack的时间戳生成逻辑"""
        if self.readyState != "live":
            raise Exception("Track not live")

        if self.kind == 'video':
            if self._timestamp is not None:
                # 增量时间戳计算
                self._timestamp += int(self.VIDEO_PTIME * self.VIDEO_CLOCK_RATE)
                # 计算等待时间以控制帧率
                wait = self._start + (self._timestamp / self.VIDEO_CLOCK_RATE) - time.time()
                if wait > 0:
                    await asyncio.sleep(wait)
            else:
                # 初始化时间戳
                self._start = time.time()
                self._timestamp = 0
                self.timelist.append(self._start)
                logger.info(f"【ImprovedMediaRelayTrack】视频轨道开始时间: {self._start}")
            return self._timestamp, self.VIDEO_TIME_BASE
        else:  # audio
            if self._timestamp is not None:
                # 增量时间戳计算
                self._timestamp += int(self.AUDIO_PTIME * self._sample_rate)
                # 计算等待时间以控制音频包发送速率
                wait = self._start + (self._timestamp / self._sample_rate) - time.time()
                if wait > 0:
                    await asyncio.sleep(wait)
            else:
                # 初始化时间戳
                self._start = time.time()
                self._timestamp = 0
                self.timelist.append(self._start)
                logger.info(f"【ImprovedMediaRelayTrack】音频轨道开始时间: {self._start}")
            return self._timestamp, self.AUDIO_TIME_BASE

    async def recv(self):
        """aiortc调用此方法来获取下一帧以发送给客户端。"""
        frame = await self._queue.get()
        
        # 设置精确的时间戳
        pts, time_base = await self.next_timestamp()
        frame.pts = pts
        frame.time_base = time_base
        
        # 性能监控（仅视频）
        if self.kind == 'video':
            self.totaltime += (time.perf_counter() - self.lasttime)
            self.framecount += 1
            self.lasttime = time.perf_counter()
            if self.framecount == 100:
                actual_fps = self.framecount / self.totaltime
                logger.info(f"【ImprovedMediaRelayTrack】实际视频FPS: {actual_fps:.2f}")
                self.framecount = 0
                self.totaltime = 0
        
        return frame

    async def queue_frame(self, frame):
        """生产者（外部代码）调用此方法将帧放入队列。"""
        try:
            # 非阻塞方式放入队列，如果满了就丢弃
            self._queue.put_nowait(frame)
        except asyncio.QueueFull:
            self._drop_count += 1
            # 每丢弃10帧记录一次日志
            if self._drop_count % 10 == 0:
                logger.warning(f"【ImprovedMediaRelayTrack】{self.kind}队列已满，已丢弃{self._drop_count}帧")
            # 尝试移除最老的帧，为新帧腾出空间
            try:
                self._queue.get_nowait()  # 移除最老的帧
                self._queue.put_nowait(frame)  # 放入新帧
            except asyncio.QueueEmpty:
                pass  # 队列为空，直接忽略

# 兼容性别名，以防其他地方还在使用旧名称
MediaRelayTrack = ImprovedMediaRelayTrack


class WebRTCStreamer(BaseWebRTCStreamer):
    """
    WebRTC推流器实现类，继承自BaseWebRTCStreamer
    提供具体的媒体处理和推流功能
    """
    def __init__(self, signaling_url, audio_sample_rate=16000, video_fps=25, target_audio_sample_rate=48000, target_audio_channels=2):
        # 调用父类初始化
        super().__init__(signaling_url, audio_sample_rate, video_fps)
        
        # 音频参数配置
        self._input_audio_sample_rate = audio_sample_rate
        self._output_audio_sample_rate = target_audio_sample_rate
        self.target_audio_channels = target_audio_channels
        
        # 音频分片配置
        self.audio_chunk_size = int(self._input_audio_sample_rate * 0.02)  # 输入侧20ms
        self.output_audio_chunk_size = int(self._output_audio_sample_rate * 0.02)  # 输出侧20ms
        
        # 音频缓冲区
        self._audio_buffer = []
        self._audio_buffer_debug = []
        
        # 性能优化参数
        self._max_video_bitrate = 10000000  # 10Mbps 视频码率
        self._max_audio_bitrate = 128000
        
        # 未连接时的帧率控制
        self._start_time = time.time()
        self._timestamp = 0
        self.VIDEO_CLOCK_RATE = 90000
        self.VIDEO_PTIME = 1 / self._video_fps
        self.VIDEO_TIME_BASE = fractions.Fraction(1, self.VIDEO_CLOCK_RATE)
        self.not_connected_count = 0
        
        logger.info(f"【WebRTCStreamer】初始化完成: 输入音频{self._input_audio_sample_rate}Hz -> 输出音频{self._output_audio_sample_rate}Hz")

    def _create_media_tracks(self):
        """创建媒体轨道（实现基类的抽象方法）"""
        self._video_track = ImprovedMediaRelayTrack(
            kind="video",
            maxsize=8,
            fps=self._video_fps
        )
        self._audio_track = ImprovedMediaRelayTrack(
            kind="audio",
            maxsize=16,  # 音频每帧是20ms，一帧视频对应2帧音频
            sample_rate=self._output_audio_sample_rate
        )
        logger.info(f"【WebRTCStreamer】媒体轨道创建完成: 视频{self._video_fps}fps, 音频{self._output_audio_sample_rate}Hz")

    # start() 和 stop() 方法由基类 BaseWebRTCStreamer 提供

    def _setup_peer_connection_handlers(self, pc: RTCPeerConnection):
        """重写基类方法，添加特定的连接状态处理逻辑"""
        @pc.on("connectionstatechange")
        async def on_connectionstatechange():
            logger.info(f"【WebRTCStreamer】对等连接状态: {pc.connectionState}")
            if pc.connectionState in ("failed", "closed", "disconnected"):
                await pc.close()
                self._pcs.discard(pc)
            
            # 更新连接状态 - 这是WebRTCStreamer特有的逻辑
            old_connected = self.is_connected
            self.is_connected = any(p.connectionState == "connected" for p in self._pcs)
            
            if old_connected != self.is_connected:
                if self.is_connected:
                    logger.info(f"【WebRTCStreamer】✅ 客户端已连接！当前连接数: {len(self._pcs)}")
                else:
                    logger.warning(f"【WebRTCStreamer】❌ 客户端已断开！当前连接数: {len(self._pcs)}")
                    self.is_connected = False

        @pc.on("iceconnectionstatechange")
        async def on_iceconnectionstatechange():
            logger.info(f"【WebRTCStreamer】ICE连接状态: {pc.iceConnectionState}")

        @pc.on("icegatheringstatechange")
        async def on_icegatheringstatechange():
            logger.info(f"【WebRTCStreamer】ICE收集状态: {pc.iceGatheringState}")

    # _handle_offer(), _create_peer_connection(), _shutdown() 方法由基类提供

    def write_frame(self, bgr_frame, audio_data):
        """写入视频帧和音频数据（实现基类的抽象方法）"""
        if not self.is_running or not self._loop:
            return

        # 没有连接时候的处理，主要是为了控制帧率
        if not self.is_connected:
            self._not_connected_process()
            return
        else:
            # 有数据，删除这个属性，保证下次写入帧时，不会因为时间戳问题导致帧率不准确
            delattr(self, "_timestamp") if hasattr(self, "_timestamp") else None

        # 基于队列状态的背压控制：在放入帧之前检查队列状态
        wait_count = 0
        while self.is_queue_full() and self.is_running and self.is_connected:
            # 队列满时等待，实现背压机制
            time.sleep(0.02)
            wait_count += 1
            if not self.is_running:
                logger.warning("【WebRTC】推流器已停止，跳过帧处理")
                break

            # 每等待1秒输出一次日志
            if wait_count % 50 == 0:  # 50 * 20ms = 1秒
                status = self.get_queue_status()
                logger.info(f"【WebRTC背压】等待队列空间: V:{status['video_queue_size']}/{status['video_queue_maxsize']}, "
                          f"A:{status['audio_queue_size']}/{status['audio_queue_maxsize']}, "
                          f"连接状态: {self.is_connected}")

        # 处理音频数据
        if self._audio_track and audio_data is not None:
            self._audio_buffer.extend(audio_data)

            # 当缓冲区有足够数据时，处理音频
            while len(self._audio_buffer) >= self.audio_chunk_size:
                # 提取一个音频块
                chunk = np.array(self._audio_buffer[:self.audio_chunk_size], dtype=np.float32)
                self._audio_buffer = self._audio_buffer[self.audio_chunk_size:]

                # 音频重采样和格式转换
                if self._input_audio_sample_rate != self._output_audio_sample_rate:
                    chunk = self.audio_resample(chunk, self._input_audio_sample_rate, self._output_audio_sample_rate)

                # 转换为立体声（如果需要）
                if self.target_audio_channels == 2:
                    chunk = np.column_stack([chunk, chunk])  # 单声道转立体声
                else:
                    chunk = chunk.reshape(-1, 1)  # 保持单声道

                # 转换为int16格式
                audio_int16 = (chunk * 32767).astype(np.int16)

                # 构造输出AudioFrame
                layout = 'stereo' if self.target_audio_channels == 2 else 'mono'
                audio_frame = AudioFrame(format='s16', layout=layout, samples=self.output_audio_chunk_size)
                audio_frame.planes[0].update(audio_int16.tobytes())
                audio_frame.sample_rate = self._output_audio_sample_rate

                try_time = 0
                max_try_time = 5  # 100ms，超过一帧的速度
                while self._audio_track._queue.full() and max_try_time > try_time:
                    try_time += 1
                    logger.warning(f"【WebRTC背压】音频队列已满，等待队列空间: {self._audio_track._queue.qsize()}/{self._audio_track._queue.maxsize}")
                    time.sleep(0.02)

                asyncio.run_coroutine_threadsafe(self._audio_track._queue.put(audio_frame), self._loop)

        # 处理视频数据
        if self._video_track and bgr_frame is not None:
            video_frame = VideoFrame.from_ndarray(bgr_frame, format="bgr24")

            try_time = 0
            max_try_time = 5  # 100ms
            while self._video_track._queue.full() and max_try_time > try_time and self.is_connected:
                try_time += 1
                logger.warning(f"【WebRTC背压】视频队列已满，等待队列空间: {self._video_track._queue.qsize()}/{self._video_track._queue.maxsize}")
                time.sleep(0.04)

            asyncio.run_coroutine_threadsafe(self._video_track._queue.put(video_frame), self._loop)

    def _not_connected_process(self):
        """没有连接时的处理，主要是为了控制帧率"""
        if not hasattr(self, "_timestamp"):
            self._timestamp = 0
            self._start_time = time.time()

        self._timestamp += int(self.VIDEO_PTIME * self.VIDEO_CLOCK_RATE)
        wait = self._start_time + (self._timestamp / self.VIDEO_CLOCK_RATE) - time.time()
        if wait > 0:
            time.sleep(wait)

        self.not_connected_count += 1
        if self.not_connected_count % 100 == 0:
            logger.info(f"【WebRTC】等待客户端连接... 已处理{self.not_connected_count}帧")

    def is_queue_full(self):
        """检查队列是否已满"""
        video_full = self._video_track and self._video_track._queue.full()
        audio_full = self._audio_track and self._audio_track._queue.full()
        return video_full or audio_full

    def get_queue_status(self):
        """获取队列状态"""
        return {
            'video_queue_size': self._video_track._queue.qsize() if self._video_track else 0,
            'video_queue_maxsize': self._video_track._queue.maxsize if self._video_track else 0,
            'audio_queue_size': self._audio_track._queue.qsize() if self._audio_track else 0,
            'audio_queue_maxsize': self._audio_track._queue.maxsize if self._audio_track else 0,
        }

    def audio_resample(self, src, src_rate, dst_rate):
        """简单的音频重采样"""
        if src_rate == dst_rate:
            return src

        # 简单的线性插值重采样
        src_len = len(src)
        dst_len = int(src_len * dst_rate / src_rate)

        # 创建新的时间轴
        src_time = np.linspace(0, src_len - 1, src_len)
        dst_time = np.linspace(0, src_len - 1, dst_len)

        # 线性插值
        resampled = np.interp(dst_time, src_time, src)
        return resampled

    def get_connection_status(self):
        """获取连接状态（兼容性方法）"""
        status = self.get_status()
        return {
            'is_connected': status['is_connected'],
            'signaling_url': f"http://{status['host']}:{status['port']}/offer"
        }
