"""
@project : face2face_train
<AUTHOR> huyi
@file   : trans_dh_service.py
@ide    : PyCharm
@time   : 2023-12-06 14:47:11
"""
import gc, multiprocessing, os, subprocess, threading, time, traceback,sys
from enum import Enum
from multiprocessing import Process, set_start_method
from queue import Empty, Full,Queue
import cv2, librosa, numpy as np, torch
import json
# from cv2box import CVImage
# from cv2box.cv_gears import Linker, Queue, CVVideoWriterThread
# from face_detect_utils.face_detect import FaceDetect, pfpld
# from face_detect_utils.head_pose import Headpose
# from face_lib.face_detect_and_align import FaceDetect5Landmarks
# from face_lib.face_restore import GFPGAN
from h_utils.custom import CustomError
from h_utils.request_utils import download_file
from h_utils.sweep_bot import sweep

from preprocess_audio_and_3dmm import op
from y_utils.config import GlobalConfig
from y_utils.logger import logger
from .server import register_host, repost_host
from lt_utils.wo_common import videocap_local_imgs
import glob
from loguru import logger
from .aud_extractor import SegmentAudioProcessor  # 音频特征提取
from .dh_processor import DhStreamProcessor  # 数字人处理
from .vid_encoder import VideoEncoder  # 视频编码



def logger_error(E):
    def locate_exception():
        """
        locate error filename object line
        """
        exc_type, exc_obj, exc_tb = sys.exc_info()
        fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
        return "{},{},{}".format(exc_type, fname, exc_tb.tb_lineno)
    logger.error(locate_exception())
    logger.error("error {}".format(E))

def get_video_info(video_file,video_info_path):
    if os.path.exists(video_info_path):
        with open(video_info_path, 'r', encoding='utf-8') as f:
            params_json = json.load(f)
            fps = params_json.get('fps', 25)
            width = params_json.get('width', 1920)
            height = params_json.get('height', 1080)
            fourcc = params_json.get('fourcc', 0)
            frame_count = params_json.get('frame_count', 0)
    else:
        cap = cv2.VideoCapture(video_file)
        fps = round(cap.get(cv2.CAP_PROP_FPS))
        width = int(cap.get(cv2.CAP_PROP_FRAME_WIDTH))
        height = int(cap.get(cv2.CAP_PROP_FRAME_HEIGHT))
        fourcc = int(cap.get(cv2.CAP_PROP_FOURCC))
        frame_count = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
        cap.release()
        params_json = {
            "fps": fps,
            "width": width,
            "height": height,
            "fourcc": fourcc,
            "frame_count": frame_count
        }
        with open(video_info_path, 'w', encoding='utf-8') as f:
            json.dump(params_json, f, ensure_ascii=False, indent=4)
            
        logger.info(f"视频信息保存到 {video_info_path}")
    return (fps, width, height, fourcc, frame_count)

# ==================================================================================================
# region: Refactored Core Classes and Processes
# ==================================================================================================

def audio_processor(task_queue, feature_queue, audio_stream_queue):
    extractor = SegmentAudioProcessor(feature_queue=feature_queue, audio_stream_queue=audio_stream_queue)
    while True:
        try:
            task_info = task_queue.get(block=True)
            if task_info is None:
                logger.info("【audio_processor】 结束信号接收, 退出.")
                break
            extractor.init_task(task_info).run()
        except Exception as e:
            work_id = task_info.get('work_id', 'unknown')
            logger_error(f"【{work_id}】 audio_processor 处理失败: {e}")
            if extractor:
                extractor._handle_end_state(error=True)


def dh_stream_processor(task_queue, feature_queue, output_imgs_queue, batch_size=4, use_npy=False):
    processor = DhStreamProcessor(feature_queue, output_imgs_queue, batch_size, use_npy)
    while True:
        try:
            task_info = task_queue.get(block=True)
            if task_info is None:
                logger.info("【dh_stream_processor】 结束信号接收, 退出.")
                break
            processor.init_task(task_info)
            processor.run()
        except Exception as e:
            work_id = task_info.get('work_id', 'unknown')
            logger_error(f"【dh_stream_processor】【{work_id}】 处理失败: {e}")
            if processor:
                processor.process_end_state(error=True)

# endregion
# ==================================================================================================


# ==================================================================================================
# region: Other Helper functions (Maintained)


def video_encoder(output_imgs_queue, result_queue):
    """
    进程入口函数，实例化并运行 VideoEncoder。
    """
    encoder = VideoEncoder(output_imgs_queue, result_queue)
    encoder.run()

# ==================================================================================================
# region: Main Task Class
# ==================================================================================================
class Status(Enum):
    run = 1
    success = 2
    error = 3

class TransDhTask(object):
    _instance = None
    

    def __init__(self, *args, **kwargs):
        logger.info("TransDhTask instance creating...")
        set_start_method("spawn", force=True)
        self.run_lock = threading.Lock()
        self.current_work_id = None
        self.task_dic = {}
        self.batch_size = kwargs.get('batch_size', 4)
        self.use_npy = kwargs.get('use_npy', True)
        self.run_flag = False
        self.spk_2_code = {}

        # --- Setup Queues and Processes ---
        self.task_queue_audio = multiprocessing.Queue(1)
        self.task_queue_dh = multiprocessing.Queue(1)
        self.feature_queue = multiprocessing.Queue(self.batch_size * 2)  # 32
        self.output_imgs_queue = multiprocessing.Queue(self.batch_size * 2) # 32
        self.audio_stream_queue = multiprocessing.Queue(64) 
        self.result_queue = multiprocessing.Queue(1)

        # Load models once
        self.audio_process = Process(target=audio_processor, args=(self.task_queue_audio, self.feature_queue, self.audio_stream_queue))
        self.dh_process = Process(target=dh_stream_processor, args=(self.task_queue_dh, self.feature_queue, self.output_imgs_queue, self.batch_size, self.use_npy))
        self.write_video_process = Process(target=video_encoder, args=(self.output_imgs_queue, self.result_queue))

        self.audio_process.start()
        self.dh_process.start()
        self.write_video_process.start()


        self.gfpgan = None # Lazy load if needed
        self.fd = None # Lazy load if needed
        self.face_blur_detect = None # Lazy load if needed

    @classmethod
    def instance(cls, *args, **kwargs):
        if not cls._instance:
            cls._instance = cls(*args, **kwargs)
        return cls._instance
    
    def work(self, audio_url, video_url, code, watermark_switch, speaker_id, is_train, digital_auth, chaofen, pn, is_stream=False, stream_url=None):
        logger.info("任务:{} -> audio_url:{}  video_url:{}".format(code,audio_url,video_url))
        work_id = code
        # if self.is_working: # 后续设置这里是切换任务
        #     logger.warning(f"Task {self.current_work_id} is already running. Rejecting new task {work_id}.")
        #     return

        st = time.time()
        self.run_flag = True

        self.current_work_id = work_id
        self.spk_2_code[speaker_id] = code # 保存speaker_id和code的映射关系
        try:

            self.change_task_status(code, Status.run, 1, '')
            logger.info(f"[{work_id}] 任务开始")
            try:
                video_info_path = os.path.join('workspace', 'models', speaker_id, 'video_info.json')
                (fps, width, height, fourcc, total_frames) = get_video_info(video_url,video_info_path)
                logger.info("[{}] -> 获取视频信息耗时:fps:{}, width:{}, height:{}, fourcc:{}, total_frames:{}".format(code, fps, width, height, fourcc, total_frames))
                #todo 这里省略了视频格式转换，不确定有没有问题
            except Exception as e:
                logger_error(f"【{work_id}】 获取视频信息失败: {e}")
                self.change_task_status(code, Status.error, 100, '', msg=str(e))
                return

            wh = 0
            wh_cache = -1 # 是否读取到wh值的flag
            # 1. 加载wh值
            wh_file = os.path.join('workspace', 'models', speaker_id, 'wh_value.txt')
            if  os.path.exists(wh_file):
                with open(wh_file, 'r') as f:
                    loaded_wh = float(f.read().strip())
                    logger.info(f"成功加载wh值: {loaded_wh}")
                    wh = wh_cache = loaded_wh
                        
            else:
                logger_error(f"【{work_id}】 wh_value.txt 文件不存在")

            task_info = {
                'work_id': work_id, 'audio_path': audio_url, 'drivered_path': video_url,
                'is_stream': is_stream,'gfpgan': self.gfpgan, 'fd': self.fd, 'face_blur_detect': self.face_blur_detect,
                'is_train': is_train, 'speaker_id': speaker_id, 'code': code, 'fps': fps, 'wh': wh,
                'total_video_frame': total_frames, 'original_frame_shape_init': (height, width), 'pn': pn,'use_npy': self.use_npy,'batch_size': self.batch_size,
                'stream_url': stream_url  # 添加推流URL
            }
            logger.info(f"【{work_id}】 任务信息: {task_info}")

            # 将任务放进去，音频和视频处理进程会从这里获取任务
            self.task_queue_audio.put(task_info)
            self.task_queue_dh.put(task_info)

            # --- Wait for result ---
            success, message = self.result_queue.get(block=True)
            logger.info(f'[{work_id}] result_queue.get() successfully')

            # --- Cleanup ---
            if success:
                self.change_task_status(code, Status.success, 100, message)
            else:
                raise CustomError(message or "未知错误导致任务失败")
                
        except Exception as e:
            logger_error(f"【{work_id}】 任务异常: {e}")
            self.change_task_status(code, Status.error, 100, '', msg=str(e))
        finally:
            # sweep([temp_dir], True if GlobalConfig.instance().temp_clean_switch == "1" else False)
            self.output_imgs_queue.empty()
            self.result_queue.empty()
            torch.cuda.empty_cache()
            self.run_flag = False
            logger.info(">>> 任务:{} 耗时:{} ".format(code, time.time() - st))
            self.current_work_id = None
            # Terminate any lingering processes
            for p_name in ['audio_process', 'dh_process', 'write_video_process']:
                p = getattr(self, p_name, None)
                if p and p.is_alive():
                    p.terminate()
            repost_host()
            logger.info(f"[{work_id}] 任务结束")


    def preprocess(self, audio_url, video_url, code):
        temp_dir = os.path.join('data/temp', code)
        os.makedirs(temp_dir, exist_ok=True)
        # Download files
        audio_path = download_file(audio_url, temp_dir)
        video_path = download_file(video_url, temp_dir)
        
        # This part seems to format/prepare video, let's assume it's correct
        formatted_video_path, fourcc = format_video_audio(code, video_path, audio_path, "mp4v")
        
        # Here, it seems driver data (landmarks etc) should be prepared if not exists.
        # This logic was complex in original file, let's assume a helper `init_wh` does this.
        init_wh(code, formatted_video_path) # This will prepare lm.npy etc.

        return audio_path, formatted_video_path, fourcc

    def change_task_status(self, code, status: Status, progress: int, result: str, msg=''):
        try:
            try:
                self.run_lock.acquire()
                if code in self.task_dic:
                    self.task_dic[code] = (
                     status, progress, result, msg)
            except Exception as e:
                logger_error(f"【{code}】 修改任务状态异常，异常信息:[{e.__str__()}]")
        finally:
            self.run_lock.release()

def format_video_audio(code, video_path, audio_path, fourcc):

    if not fourcc == cv2.VideoWriter_fourcc("H", "2", "6", "4"):
        if fourcc == cv2.VideoWriter_fourcc("a", "v", "c", "1") or fourcc == cv2.VideoWriter_fourcc("h", "2", "6", "4"):
            ffmpeg_command = "ffmpeg -loglevel warning -i %s -crf 15 -vcodec copy -an -y %s"
        else:
            ffmpeg_command = "ffmpeg -loglevel warning -i %s -c:v libx264 -crf 15 -an -y %s"  #todo:这个是有问题的
    else:
        ffmpeg_command = "ffmpeg -loglevel warning -i %s -c:v libx264 -crf 15 -an -y %s"
    video_format = os.path.join(GlobalConfig.instance().temp_dir, code + "_format.mp4")
    ffmpeg_command = ffmpeg_command % (video_path, video_format)
    logger.info("[{}] -> ffmpeg video: {}".format(code, ffmpeg_command))
    os.system(ffmpeg_command)
    if not os.path.exists(video_format):
        raise Exception("format video error")
    ffmpeg_command = "ffmpeg -loglevel warning -i %s -ac 1 -ar 16000 -acodec pcm_s16le -y  %s"
    audio_format = os.path.join(GlobalConfig.instance().temp_dir, code + "_format.wav")
    ffmpeg_command = ffmpeg_command % (audio_path, audio_format)
    logger.info("[{}] -> ffmpeg audio: {}".format(code, ffmpeg_command))
    os.system(ffmpeg_command)
    if not os.path.exists(audio_format):
        raise Exception("format audio error")
    return (
     video_format, audio_format)


def init_wh(code, drivered_path):
    # This function seems important for preprocessing
    return op.init_wh(code, drivered_path)

