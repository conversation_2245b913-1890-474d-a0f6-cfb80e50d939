# -*-coding: utf-8 -*-
# @Time :2025-07-02
# <AUTHOR> lishengjing
# @Email : <EMAIL>
# @File : dh_processor.py

import os
import sys
import cv2
from loguru import logger
import numpy as np
from queue import Empty 
import time
import json
from y_utils.config import GlobalConfig
from landmark2face_wy.digitalhuman_interface import DigitalHumanModel


#
def logger_error(E):
    def locate_exception():
        """
        locate error filename object line
        """
        exc_type, exc_obj, exc_tb = sys.exc_info()
        fname = os.path.split(exc_tb.tb_frame.f_code.co_filename)[1]
        return "{},{},{}".format(exc_type, fname, exc_tb.tb_lineno)
    logger.error(locate_exception())
    logger.error("error {}".format(E))



class DhStreamProcessor(object):
    """
    数字人流式处理器。
    - 从feature_queue接收音频特征。
    - 根据音频特征，匹配驱动视频帧。
    - 构建批次数据，调用模型进行推理。
    - 将生成的图像放入output_imgs_queue。
    """
    def __init__(self, feature_queue, output_imgs_queue, batch_size=8, use_npy=False):
        self.work_id_for_logging = "DhStream"
        self.feature_queue = feature_queue
        self.output_imgs_queue = output_imgs_queue
        self.batch_size = batch_size
        self.use_npy = use_npy
        self.digital_human_model = DigitalHumanModel(GlobalConfig.instance().blend_dynamic, GlobalConfig.instance().chaofen_before, face_blur_detect=False,half=False)
        self.face_data_dict = None
        self.task_info = None
        self._is_running = False
        self.output_resize = 1

        self.scrfd_detector,self.scrfd_predictor,self.hp = None,None,None
        self.work_id_for_logging = "DhStream"

        self.buffer_batch_num = {
            "silence": 2,    # 2批*4帧*40ms=320ms
            "data": 4,       # 4批*4帧*40ms=640ms  
            "start": 10,
            "end": 10
        }
        
        # 背压机制配置
        self.backpressure_config = {
            "enable_backpressure": True,  # 启用背压机制
            "frame_wait_time": 0.04,     # 等待时间(25fps = 40ms)
            "max_queue_multiplier": 2    # 最大队列大小倍数
        }

        self.is_stream = False

    def init_task(self, task_info):
        # ---------------------------- 获取task_info的信息 --------------------------------
        self.task_info = task_info
        self.code = task_info.get('code')
        self.wh = task_info.get('wh', 0)
        self.speaker_id = task_info.get('speaker_id')
        self.is_train_str = str(task_info.get('is_train', False))
        self.use_npy_flag = task_info.get('use_npy', False)
        self.total_video_frame = task_info.get('total_video_frame', 0)
        self.original_frame_shape_init = task_info.get('original_frame_shape_init', (0,0))
        self.width = self.original_frame_shape_init[1]
        self.height = self.original_frame_shape_init[0]
        self.pn = task_info.get('pn', True)
        self.fps = task_info.get('fps', 25)
        self.is_stream = task_info.get('is_stream', False)

        self._init_one_session()

        logger.info(f"【DhStream初始化完成】 开始处理视频: {self.code}, wh: {self.wh}, speaker_id: {self.speaker_id}, is_train: {self.is_train_str}, use_npy: {self.use_npy_flag}, total_video_frame: {self.total_video_frame}, shape_init: {self.original_frame_shape_init}")

        # 初始化wh
        if self.wh == 0 or self.wh == -1:
            self.wh = self.digital_human_model.drivered_wh

        # ---------------------------- 初始化地址属性 --------------------------------
        self.models_dir = os.path.join('workspace', 'models', self.speaker_id) # 模型文件地址
        self.chaofen_frames_dir = os.path.join(self.models_dir, f'{"chaofen_frames_npy" if self.use_npy else "chaofen_frames"}') # 超分帧地址
        self.drivered_frames_dir = os.path.join(self.models_dir, 'drivered_frames') # 驱动帧地址
        self.img_dir = self.chaofen_frames_dir if os.path.exists(self.chaofen_frames_dir) else self.drivered_frames_dir # 如果存在超分帧地址就优选
        self.silence_img_dir = os.path.join(self.models_dir, 'silence_frames') # 静音帧地址
        os.makedirs(self.silence_img_dir,exist_ok=True)
        self.video_info_path = os.path.join(self.models_dir, 'video_info.json') # 视频信息地址

        self.suffix = "npy" if self.use_npy else "jpg"

        self.history_data_path = os.path.join(self.models_dir, 'face_data', 'face_data.json') # 历史人脸数据地址

        # ---------------------------------图片循环id列表，用于循环播放驱动帧--------------------------------
        self.current_idx = 0
        self.cycle_img_idx_list = [i for i in range(1,self.total_video_frame+1)] + [i for i in range(1,self.total_video_frame+1)][::-1]  if self.pn else [i for i in range(1,self.total_video_frame+1)]
        self.cycle_img_len = self.total_video_frame*2 if self.pn else self.total_video_frame

        # ---------------------------------加载历史人脸数据 --------------------------------
        # todo: 没有历史数据的情况没考虑
        self.history_data_path = os.path.join(self.models_dir, 'face_data', 'face_data.json')
        if os.path.exists(self.history_data_path): # 如果存在历史数据文件，则加载历史数据
            logger.info(f"【DhStream】加载历史人脸数据: {self.history_data_path}")
            try:
                with open(self.history_data_path, 'r') as f:
                    loaded_history = json.load(f)
                    # Basic validation before assigning
                    if isinstance(loaded_history, dict) and loaded_history.get('speaker_id') == self.speaker_id:
                        self.history_data = loaded_history
                        logger.info(f"【DhStream】成功加载历史人脸数据: {self.speaker_id}")
                    else:
                        logger.warning(f"【DhStream】加载历史人脸数据失败: {self.history_data_path}, 使用新历史数据.")
            except Exception as e_load: # todo 需要处理,找不到就返回失败
                logger_error(f"【DhStream】加载历史人脸数据失败: {e_load}, 使用新历史数据.")
                self.history_data['speaker_id'] = self.speaker_id # Ensure speaker_id is set
        else:
            logger.info(f"【DhStream】不存在历史人脸数据: {self.speaker_id}")

        self.start_time = time.time()


        #----------------------保存debug结果--------------------------------
        self.debug_frame_id =0
        self.debug_audio_data = []
        #----------------------------------------------------------------

        # 发送到VideoEncoder进程开始指令
        self.output_imgs_queue.put(['start', None, task_info])
        # # 流式状态下启动后先进入静音状态，这样视频流会先输出静音帧
        if self.is_stream:
            self._status = "Ready!"
        #     self.process_state(None, None)

    def _init_one_session(self):
        self.batch_num = 0
        self.infer_time = []
        self.tlast = time.time()

        self.history_data = {
            'bounding_box_list': [],
            'bounding_box_p_list': [],
            'landmarks_list': [],
            'crop_lm_list': [],
            'no_face_indices': [],
            'speaker_id':self.speaker_id
    }   #初始化

        self.init_batch_data()

        # ---------------------------- 清空上次对列数据 --------------------------------
        # while self.feature_queue.qsize() > 0:
        #     self.feature_queue.get()
        while self.output_imgs_queue.qsize() > 0:
            self.output_imgs_queue.get()


    # 初始化batch的数据
    def init_batch_data(self):
        self.drivered_fnames_list = [] # Changed from drivered_list
        self.wenet_feature_list = []
        self.batch_img_idx_list =[]  # 视频帧索引,有可能是倒叙的，
        self.batch_aud_idx_list =[]  # 音频帧索引，只会是正序
        self.batch_status = [] # 批次中每一帧的状态，data,silence,start,end
        self.batch_audio_data = [] # 批次中每一帧的音频数据

    def process_state(self, feature_data, audio_data):
        """
         self._status: 当前状态 start,data:有音频流传入；silence:静音状态，本段音频结束，没有音频流输入但是还要有视频流输出；finish:结束状态，本次任务结束，等待下次任务开始
         data_data: 音频特征数据
         audio_data: 音频原始数据
         silence: 静音数据
         start: 开始数据，没有音频流输入但是还要有视频流输出
         """
        # print(f"【DhStream】处理状态: {self._status}, data长度: {len(feature_data[0]) if feature_data and len(feature_data) > 0 else 'None'}, audio_data长度: {len(audio_data) if audio_data else 'None'}")
        
        if self._status == "data":
            self.process_data_state(feature_data, audio_data)
        elif self._status == "silence":
            self.process_silence_state(feature_data, audio_data)  # 处理当前批次剩余数据
        elif self._status == "start":  # 初始化或重新开始
            logger.info(f"【{self.work_id_for_logging}】 开始新的音频段.")
            # self.init_batch_data() # 不处理这个逻辑
        elif self._status == "end":
            logger.info(f"【{self.work_id_for_logging}】音频特征处理结束.") 
            self._is_running = False
            self.batch_inference()  # 处理当前批次剩余数据

    def run(self):
        self._is_running = True
        logger.info(f"【{self.work_id_for_logging}】 开始循环推理.")
        # print(f"★★★【DhStream.run】开始运行，初始状态: {self._status}")
        
        loop_count = 0  # 循环计数器，用于调试
        while self._is_running:
            loop_count += 1
            try:
                _status, feature_data, audio_data = self.feature_queue.get(block=True, timeout=1.0)
                # print(f"★★★【DhStream.run】正常状态获取数据: status={_status}, data={'有数据' if feature_data else 'None'}, audio_data={'有音频' if audio_data else 'None'}")
                if _status != self._status:
                    logger.info(f"【{self.work_id_for_logging} [switch status]】 从{self._status}状态切换到{_status}状态")
                self._status = _status
                self.process_state(feature_data, audio_data)
            except Empty:
                # 超时，继续循环
                logger.warning(f"【DhStream.run】Empty异常，当前状态: {self._status}")
                continue
            except Exception as e:
                logger.error(f"【DhStream.run】发生异常: {e}")
                logger_error(f"【{self.work_id_for_logging}】 循环推理错误: {e}")
                import traceback
                traceback.print_exc()
                self._is_running = False
                break
        print(f"★★★【DhStream.run】循环结束，_is_running: {self._is_running}")

    def process_data_state(self, audio_feature, audio_data):
        self.wenet_feature_list.append(audio_feature)
        self.batch_audio_data.append(audio_data)
        self.current_idx += 1
        img_idx = self.cycle_img_idx_list[self.current_idx % self.cycle_img_len]
        self.batch_img_idx_list.append(img_idx)  # 从1开始
        self.batch_aud_idx_list.append(self.current_idx)
        f_file_name = os.path.join(self.img_dir, f"frame_{img_idx:06d}.{self.suffix}")
        self.batch_status.append(self._status)
        self.drivered_fnames_list.append(f_file_name) # Changed

        # 这里有两个逻辑需要推理：
        # 1. 如果批次满了，则进行推理（包括全部都是silence的情况）
        # 2. 如果当前状态不是data，但是本批次中有data状态，则进行推理，清理完本批次数据 （这种情况基本就是刚刚切换到了静音状态，但是音频数据还在）
        if len(self.wenet_feature_list) == self.batch_size or (self._status != "data" and all(status == "data" for status in self.batch_status)):
            self.batch_inference()


    # 批次推理
    def batch_inference(self,):
        # print(f"★★★【process_batch】开始处理批次，特征数量: {len(self.wenet_feature_list)}")
        if len(self.wenet_feature_list) == 0:
            # print(f"★★★【batch_inference[{self._status}]】特征列表为空，直接返回")
            self.init_batch_data()
            return
        
        ####################################音频特征转numpy####################################################
        t_get1 = time.time()
        audio_feature_list = [np.array(w,dtype=np.float32) for w in self.wenet_feature_list]  # 将音频特征列表转换回 numpy 数组
        frameId = self.batch_aud_idx_list[-1] if len(self.batch_aud_idx_list)>0 else -1
        start_idx = 0   # 本batch的开始
        t_get2 = time.time()
        # print(f"★★★【process_batch】步骤1完成，耗时: {(t_get2 - t_get1)*1000:.3f}ms")
        
        ####################################保存/读取人脸数据####################################################
        drivered_face_dict,no_face_indices = _get_drivered_face_dict(self.history_data,self.models_dir,self.batch_img_idx_list)
        # 如果  img_list 为 None。这将由后续加载帧的 write_video 函数处理。
        face_params = _get_face_params_from_drivered_face_dict(drivered_face_dict,self.original_frame_shape_init, self.output_resize) 
        t_get3 = time.time()
        # logger.info(f"【{self.work_id_for_logging} [get face data]】: {(t_get3 - t_get2)*1000:.3f}ms")
        # print(f"★★★【process_batch】步骤2完成，耗时: {(t_get3 - t_get2)*1000:.3f}ms")
        
        ################################## 开始推理结果####################################################
        # print(f"★★★【process_batch】步骤3: 开始推理，检查静音状态")
        t6 = time.time()
        silence_find = False  # 兼容静音模式
        save_silence = False
        img_name_list = []
        if all(status == "silence" for status in self.batch_status): # 优先读取静音数据，只有所有的数据都是静音状态的情况下才读取
            # print(f"★★★【process_batch】全是静音状态，尝试读取静音数据")
            img_name_list = [os.path.basename(img_name) for img_name in self.drivered_fnames_list]
            silence_find,processed_raw_face_regions = read_silence_blend_imgs(self.silence_img_dir,img_name_list,self.use_npy)
            save_silence =  not silence_find # 找不到才保存
            # print(f"★★★【process_batch】静音数据读取结果: {silence_find}")
        
        # batch_size, start_index, blend_dynamic, params, frameId
        if not silence_find: # 正常推理
            # print(f"★★★【batch_inference[{self._status}]】开始神经网络推理")
            batch_size = len(self.batch_status)
            processed_raw_face_regions = get_blend_imgs(
                batch_size, 
                audio_feature_list, 
                drivered_face_dict, 
                GlobalConfig.instance().blend_dynamic, 
                face_params,
                self.digital_human_model, 
                frameId,
                start_idx) # 仅返回来自神经网络的原始人脸区域
            # print(f"★★★【batch_inference[{self._status}]】神经网络推理完成")

            if save_silence:
                # 写入静音数据
                write_silence_blend_imgs(self.silence_img_dir,img_name_list,processed_raw_face_regions,self.use_npy)
                silence_img_count = len(os.listdir(self.silence_img_dir))
                if silence_img_count % 20==0 or silence_img_count >= self.total_video_frame:
                    logger.info(f"★★★【batch_inference[{self._status}]】静音数据保存完成: {silence_img_count} / {self.total_video_frame}")

        ################################## 发送数据 ####################################################
        # print(f"★★★【process_batch】步骤4: 准备发送数据到输出队列")
        # 写入视频所需数据: 文件名列表、拼接参数、原始人脸区域、批次中无人脸的索引、是否使用npy格式
        t_get4 = time.time()

        # ---------------------------- 保存debug结果 --------------------------------
        debuging =False
        if debuging:
            save_data_dir = os.path.join(self.models_dir, 'face_data_result')
            save_audio_dir = os.path.join(self.models_dir, 'audio_data_result')
            os.makedirs(save_data_dir, exist_ok=True)
            os.makedirs(save_audio_dir, exist_ok=True)
            for i,img_ in enumerate(processed_raw_face_regions):
                img_name  =  os.path.join(save_data_dir,f'frame_{self.debug_frame_id:06d}.jpg')
                cv2.imwrite(img_name,img_)
                self.debug_frame_id += 1
                self.debug_audio_data.extend(self.batch_audio_data[i])
        if len(self.debug_audio_data) > 16000*10:
            import soundfile as sf
            debug_audio_data = np.array(self.debug_audio_data,dtype=np.float32)
            sf.write(os.path.join(save_audio_dir,f'audio_{self.debug_frame_id:06d}.wav'),debug_audio_data,16000)
            self.debug_audio_data = []
            

        output_package = (self.drivered_fnames_list.copy(), face_params, processed_raw_face_regions, no_face_indices, self.use_npy_flag,self.batch_audio_data.copy())
        
        # print(f"★★★【process_batch】准备put到output_imgs_queue，队列当前大小: {self.output_imgs_queue.qsize()}")

        # 在这里控制不同状态下是否需要发送,
        buffer_batch_num = self.buffer_batch_num.get(self._status,3) if self.is_stream else 8 # 流式模式下，下游数据太多了，先等待
        while self.output_imgs_queue.qsize() > buffer_batch_num:  # 下游数据太多了，先等待
            time.sleep(0.02) # 10ms

        self.output_imgs_queue.put([self._status, None, output_package]) # status_code, reason_placeholder, data_tuple
        # print(f"★★★【batch_inference[{self._status}]】成功put到output_imgs_queue，队列新大小: {self.output_imgs_queue.qsize()}")
        
        t_get5 = time.time()
        real_fps = 1/max(1e-6,(time.time() - self.tlast))
        self.tlast = time.time()
        # logger.info(f"【real FPS】:{real_fps:.2f}")
        # 仅仅统计推理的速度
        if not silence_find:
            self.infer_time.append(time.time() - t_get1)
        self.batch_num += 1
        if self.batch_num %10 == 0:
            # 计算最近200批次的fps
            count_batch = len(self.infer_time) if len(self.infer_time) < 200 else 200 
            last_200batch_fps = count_batch*4/max(1e-6,sum(self.infer_time[-count_batch:])) if not silence_find else -1
            logger.info(f'>>> 【{self.work_id_for_logging} [{self._status}]】 第 {self.batch_num} 批数据推理{len(self.drivered_fnames_list)}帧, 本批耗时 {time.time() - t_get1:.3f}s, FPS:{len(self.drivered_fnames_list)/(max(1e-6,time.time() - t_get1)):.2f}，最近{count_batch}批平均fps:{last_200batch_fps:.2f},下游数据批次：{self.output_imgs_queue.qsize()}')
        
        # 清空批次数据
        self.init_batch_data()

 
    def process_end_state(self, error=None):
        logger.info(f"[{self.work_id_for_logging}]  finishing. Putting sentinel into output queue.")
        self.output_imgs_queue.put(['end', str(error) if error else None, None])

    def process_silence_state(self,audio_feature, audio_data):
        """目前静默阶段不在生成前做等待处理，而是在process_batch生成完后，在发送之前做等待处理"""
        # 刚切换到静默状态的时候，处理完当前剩余数据
        # 如果当前状态不是data，但是本批次中有data状态，则进行推理，清理完本批次数据
        if self._status != "data" and all(status == "data" for status in self.batch_status): #
            self.batch_inference()

        self.process_data_state(audio_feature, audio_data)
  
                    



# ==================================================================================================
def _get_chaofen_src_imgs(code,is_train,img_list, digital_human_model, scrfd_detector, frameId,batch_img_idx_list , batch_aud_idx_list, speaker_id,total_video_frame,use_npy=False):
    # This function seems complex and related to data preparation. Keeping as is.
    return digital_human_model.get_chaofen_src_imgs(code,is_train,img_list, scrfd_detector, frameId,batch_img_idx_list , batch_aud_idx_list, speaker_id,total_video_frame,use_npy)

def _get_drivered_face_dict(history_data,save_data_dir,batch_img_idx_list):
    # todo: crop_img 兼容npy 和 jpg
    # 训练模式：保存关键点和人脸框信息为npy文件
    face_data_dir = os.path.join(save_data_dir, 'face_data')  # 创建face_data目录保存人脸关键数据
    os.makedirs(face_data_dir, exist_ok=True)
    crop_img_dir = os.path.join(face_data_dir, 'crop_img')   # 创建crop_img目录保存裁剪的人脸图像
    wh_file = os.path.join(save_data_dir, 'wh_value.txt')
    os.makedirs(crop_img_dir, exist_ok=True)

    # 非训练模式：尝试从已保存的数据加载并使用
    if os.path.exists(wh_file):
        drivered_face_dict = {}
        no_face_indices = []
        if len(history_data) > 0: # 从文件加载历史数据
            for i,img_idx in enumerate(batch_img_idx_list):
                drivered_face_dict[i] = history_data.get('{}'.format(img_idx),{})
                
                for k in ['bounding_box','bounding_box_p','landmarks','crop_lm']:
                    if k in drivered_face_dict[i] and isinstance(drivered_face_dict[i][k], list):
                        drivered_face_dict[i][k] = np.array(drivered_face_dict[i][k])
                if drivered_face_dict[i].get('no_face',True):
                    no_face_indices.append(i)
 
                frame_path = os.path.join(face_data_dir, 'crop_img', f'frame_{img_idx:06d}.jpg')
                if os.path.exists(frame_path):
                    img = cv2.imread(frame_path)
                    drivered_face_dict[i]['crop_img'] = img
                else:
                    drivered_face_dict[i]['crop_img'] = None
        else:
            no_face_indices = [0,1,2,3]

            
    return drivered_face_dict,no_face_indices


def _get_face_params_from_drivered_face_dict(drivered_face_dict,out_shape, output_resize):
    x1_list, x2_list, y1_list, y2_list = ([], [], [], [])
    get_face_params_time1 = time.time()
    for idx in range(len(drivered_face_dict)):
        facebox = drivered_face_dict[idx]['bounding_box']
        x1_list.append(facebox[0])
        x2_list.append(facebox[1])
        y1_list.append(facebox[2])
        y2_list.append(facebox[3])
    drivered_exceptlist = []
    get_face_params_time2 = time.time()
    frame_len = len(drivered_face_dict.keys())
    for i in range(frame_len):
        if len(drivered_face_dict[i]['bounding_box_p']) == 4:
            break
        drivered_exceptlist.append(i)
        print(drivered_exceptlist, '-------------------------------------')
    get_face_params_time3 = time.time()
    for i in drivered_exceptlist:
        drivered_face_dict[i]['bounding_box_p'] = drivered_face_dict[len(drivered_exceptlist)]['bounding_box_p']
        drivered_face_dict[i]['bounding_box'] = drivered_face_dict[len(drivered_exceptlist)]['bounding_box']
        drivered_face_dict[i]['crop_lm'] = drivered_face_dict[len(drivered_exceptlist)]['crop_lm']
        drivered_face_dict[i]['crop_img'] = drivered_face_dict[len(drivered_exceptlist)]['crop_img']
    get_face_params_time4 = time.time()
    keylist = list(drivered_face_dict.keys())
    keylist.sort()
    get_face_params_time5 = time.time()
    for it in keylist:
        if len(drivered_face_dict[it]['bounding_box_p']) != 4:
            # print(it, '++++++++++++++++++++++++++++++++++++++++++++++++++++++++++++')
            drivered_face_dict[it]['bounding_box_p'] = drivered_face_dict[it - 1]['bounding_box_p']
            drivered_face_dict[it]['bounding_box'] = drivered_face_dict[it - 1]['bounding_box']
            drivered_face_dict[it]['crop_lm'] = drivered_face_dict[it - 1]['crop_lm']
            drivered_face_dict[it]['crop_img'] = drivered_face_dict[it - 1]['crop_img']
    # get_face_params_time6 = time.time()
    face_params = [out_shape, output_resize,  y1_list, y2_list, x1_list, x2_list]
    # get_face_params_time7 = time.time()
    # logger.info(f"获取人脸参数耗时: 7-6：{get_face_params_time7 - get_face_params_time6:.6f}s，6-5：{get_face_params_time6 - get_face_params_time5:.6f}s，5-4：{get_face_params_time5 - get_face_params_time4:.6f}s，4-3：{get_face_params_time4 - get_face_params_time3:.6f}s，3-2：{get_face_params_time3 - get_face_params_time2:.6f}s，2-1：{get_face_params_time2 - get_face_params_time1:.6f}s")
    return face_params


def warp_imgs(imgs_data):
    caped_img2 = {idx: {'imgs_data':it,  'idx':idx} for it, idx in zip(imgs_data, range(len(imgs_data)))}
    return caped_img2

def write_silence_blend_imgs(silence_path,img_name_list,blend_imgs,use_npy=False): # 保存静音数据
    silence_imgs_list = os.listdir(silence_path) # 获取所有的图片名称
    for i,name in enumerate(img_name_list):
        if not  name in silence_imgs_list:
            np.save(os.path.join(silence_path,name),blend_imgs[i]) if use_npy else cv2.imwrite(os.path.join(silence_path,name),blend_imgs[i])


def read_silence_blend_imgs(silence_path,img_name_list,use_npy=False): # 直接读取静音数据
    # 读取静音数据t
    is_find,blend_imgs = True,None
    silence_imgs_list = os.listdir(silence_path) # todo 可以不用每次遍历，后面想想办法优化

    for name in img_name_list:
        if not  name in silence_imgs_list:
            is_find = False
            break
    # 读取静音数据
    if is_find:
        blend_imgs = [np.load(os.path.join(silence_path,name)) for name in img_name_list] if use_npy else [cv2.imread(os.path.join(silence_path,name)) for name in img_name_list]
    return is_find,blend_imgs


def get_blend_imgs(batch_size, audio_data, face_data_dict, blend_dynamic, params, digital_human_model, frameId, start_idx):
    # This function calls the NN model for inference.
    result_raw_face_regions = digital_human_model.inference_notraining(
        audio_data, 
        face_data_dict, 
        batch_size,
        start_idx,
        blend_dynamic,
        params,
        frameId
    )
    return result_raw_face_regions
