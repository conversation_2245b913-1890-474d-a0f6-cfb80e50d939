# uncompyle6 version 3.9.2
# Python bytecode version base 3.8.0 (3413)
# Decompiled from: Python 3.10.9 | packaged by Anaconda, Inc. | (main, Mar  1 2023, 18:18:15) [MSC v.1916 64 bit (AMD64)]
# Embedded file name: /code/y_utils/config.py
# Compiled at: 2024-06-11 11:24:42
# Size of source mod 2**32: 3601 bytes
"""
File: config.py
Author: YuFangHui
Date: 2020-11-25
Description:
"""
import configparser, os
from y_utils import config
from y_utils import tools
base_dir = "./"

def get_config():
    config = configparser.ConfigParser()
    config.read((os.path.join(base_dir, "config/config.ini")), encoding="utf-8")
    return config


class GeneralConfig:

    def __init__(self, config_path, section):
        self.conf = configparser.ConfigParser()
        self.conf.read(config_path)
        self.section = section
        print(self.conf.__dict__)

    def __getattr__(self, item):
        return self._GeneralConfig__get_option(item, self.section)

    def __get_option(self, option, section = ('app',)):
        pass
    # WARNING: Decompyle incomplete


class GlobalConfig:

    def __init__(self):
        self.server_ip = self.get_config("http_server", "server_ip", "0.0.0.0")
        self.server_port = self.get_config("http_server", "server_port", 8383)
        self.temp_dir = self.get_config("temp", "temp_dir", os.path.join(base_dir, "temp"))
        self.temp_clean_switch = self.get_config("temp", "clean_switch", 0)
        self.result_dir = self.get_config("result", "result_dir", os.path.join(base_dir, "result"))
        self.result_clean_switch = self.get_config("result", "clean_switch", 0)
        self.access_key_id = self.get_config("obs", "access_key_id", None)
        self.secret_access_key = self.get_config("obs", "secret_access_key", None)
        self.obs_server = self.get_config("obs", "obs_server", None)
        self.bucket = self.get_config("obs", "bucket", None)
        self.obs_dir = self.get_config("obs", "obs_dir", None)
        self.batch_size = self.get_config("digital", "batch_size", 8)
        self.watermark_path = self.get_config("watermark", "watermark_path", None)
        self.digital_auth_path = self.get_config("digital_human_authentication", "digital_auth_path", None)
        self.model_version = self.get_config("model", "model_version", "256v1")
        self.blend_dynamic = self.get_config("model", "blend_dynamic", "lmk")
        self.chaofen_before = self.get_config("model", "chaofen_before", 1)
        self.chaofen_after = self.get_config("model", "chaofen_after", 0)
        self.blur_threshold = float(self.get_config("model", "blur_threshold", 0.3))
        self.register_url = self.get_config("register", "url", "")
        self.register_file = self.get_config("register", "file", "/code/data/result/.reg")
        self.register_report_interval = int(self.get_config("register", "report_interval", 3600))
        self.register_enable = int(self.get_config("register", "enable", 1))

    @classmethod
    def instance(cls, *args, **kwargs):
        if not hasattr(GlobalConfig, "_instance"):
            GlobalConfig._instance = GlobalConfig(*args, **kwargs)
        return GlobalConfig._instance

    def get_config(self, section, key, default_value):
        if config.get_config().has_option(section, key):
            return config.get_config().get(section, key)
        return default_value