# 音频流式处理机制说明 (WenetStreamer适配版)

## 问题背景

1. **音频流长度不固定**：`audio_stream_queue`传入的音频数据长度不一致，有长有短
2. **WenetStreamer窗口机制**：需要满足WenetStreamer的处理要求（11520样本窗口，1280样本chunk）
3. **音视频同步需求**：每一帧视频需要对应准确的音频数据

## WenetStreamer工作机制

### 窗口参数：
```python
chunk_ms = 80      # 输出特征对应的音频时长：80ms
context_ms = 320   # 左右上下文：320ms

# 换算为样本数（16kHz采样率）
chunk_samples = 1280    # 80ms * 16000Hz / 1000 = 1280样本 (2帧)
context_samples = 5120  # 320ms * 16000Hz / 1000 = 5120样本 (8帧)
window_samples = 11520  # 5120 + 1280 + 5120 = 11520样本 (18帧)
```

### 特征提取窗口：
```
[5120样本上下文] + [1280样本当前] + [5120样本上下文] = 11520样本总窗口
     8帧              2帧              8帧           18帧
```

## 解决方案

### 1. 双重缓冲机制

```python
# 音频处理参数
sample_rate = 16000  # 采样率
chunk_samples = 1280  # WenetStreamer每次处理的音频块：2帧
samples_per_video_frame = 640  # 每视频帧音频样本数：1帧
audio_buffer = []  # 原始音频缓冲区
frame_audio_queue = []  # 视频帧音频队列
```

### 2. 数据流程

```
音频流输入 → 原始缓冲区 → 按帧切分 → 帧队列 → WenetStreamer → 特征输出
```

#### 详细流程：

```
输入：[不定长音频块]
     ↓
原始缓冲区：[累积音频数据]
     ↓
按640样本切分：
帧1：[640样本] → 帧队列
帧2：[640样本] → 帧队列
帧3：[640样本] → 帧队列
     ↓
当帧队列≥2帧时：
取出2帧(1280样本) → WenetStreamer → 输出2个[20,256]特征
```

### 3. 音频-特征对应关系

```python
# 每次给WenetStreamer 1280样本（2帧）
wenet_input = [frame1(640) + frame2(640)]  # 1280样本

# WenetStreamer输出特征（通常2帧）
features = [[20,256], [20,256]]  # 2个特征帧

# 特征与音频帧的对应关系
feature[0] ↔ frame1的音频数据
feature[1] ↔ frame2的音频数据
```

## 时序对齐

### 音频帧与视频帧的精确对应

```
时间轴:    0ms    40ms   80ms   120ms  160ms
视频帧:     1      2      3      4      5
音频帧:   [640]  [640]  [640]  [640]  [640] 样本
特征帧:   [20,256] [20,256] [20,256] [20,256] [20,256]

WenetStreamer处理:
输入: frame1+frame2 (1280样本) → 输出: 2个特征
输入: frame3+frame4 (1280样本) → 输出: 2个特征
```

## 关键改进

### 1. WenetStreamer适配
- 按照WenetStreamer的chunk_samples(1280)提供数据
- 满足其内部窗口机制的要求
- 正确处理上下文依赖

### 2. 音频-特征精确映射
```python
# 为每个特征帧分配对应的音频数据
for i in range(min(feature_frames, len(corresponding_frame_audio))):
    single_feature = features[i:i+1]
    audio_data = corresponding_frame_audio[i].tolist()
    self.feature_queue.put(("data", single_feature.tolist(), audio_data))
```

### 3. 边界处理优化
- **不足2帧时**：用静音填充至1280样本
- **flush处理**：正确处理WenetStreamer的flush输出
- **状态切换**：完善处理silence、end等状态

### 4. 内存管理
- 及时清理处理过的音频数据
- 双重队列避免数据积压

## 使用示例

```python
# 输入音频流（长度不固定）
audio_chunks = [
    bytes(np.random.randint(-32768, 32767, 400, dtype=np.int16)),  # 400样本
    bytes(np.random.randint(-32768, 32767, 800, dtype=np.int16)),  # 800样本
    bytes(np.random.randint(-32768, 32767, 1000, dtype=np.int16)), # 1000样本
]

# 处理流程：
# 1. 原始缓冲区累积: 400 → 1200 → 2200 样本
# 2. 按640样本切分帧: 3个完整帧 + 剩余200样本
# 3. WenetStreamer处理: 2帧(1280样本) → 2个特征
# 4. 输出: feature1 ↔ frame1_audio, feature2 ↔ frame2_audio
```

## 技术优势

1. **完全适配WenetStreamer**：满足其窗口和chunk要求
2. **精确音视频同步**：每个特征对应准确的音频帧
3. **处理灵活性**：适应各种长度的输入音频块
4. **上下文保持**：利用WenetStreamer的上下文机制
5. **资源效率**：双重缓冲，及时释放数据

## 关键参数

- **视频帧率**: 25 fps (可配置)
- **每视频帧音频**: 640样本 (16000Hz / 25fps)
- **WenetStreamer chunk**: 1280样本 (2视频帧)
- **特征维度**: [20, 256]
- **窗口大小**: 11520样本 (18视频帧，包含上下文)

这种设计确保了WenetStreamer能够正确工作，同时保持了音视频的精确同步，为数字人生成提供了高质量的音频特征输入。 