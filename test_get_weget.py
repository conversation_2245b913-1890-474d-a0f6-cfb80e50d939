import torch
from wenet.compute_ctc_att_bnf import get_weget,load_ppg_model
import librosa
import numpy as np


def feature_extraction_wenet(audio_file, fps, wenet_model, mfccnorm=True, section=560000):
    rate = 16000
    win_size = 20
    if type(audio_file) == str:
        sig, rate = librosa.load(audio_file, sr=rate, duration=None)
    else:
        sig = audio_file
    print("sig.shape", len(sig))
    time_duration = len(sig) / rate
    print("time_duration", time_duration)
    cnts = range(int(time_duration * fps))
    # print("cnts",cnts)
    indexs = []
    f_wenet_all = get_weget(audio_file, wenet_model, section)
    print("f_wenet_all.shape", f_wenet_all.shape)
    np.save("f_wenet_all.npy", f_wenet_all)
    f_wenet_all_list = f_wenet_all.tolist()
    # with open("f_wenet_all_list.txt", "w") as f:
    with open("f_wenet_5s_silence_list.txt", "w") as f:
        for i,item in enumerate(f_wenet_all_list):
            f.write(str(i) + "\n" + str(item) + "\n\n")
    ii = []
    for i,cnt in enumerate(cnts):
        c_count = int(cnt / cnts[-1] * (f_wenet_all.shape[0] - 20)) + win_size // 2
        # ii.append(c_count-i)
        # print(i,"c_count",c_count,c_count-i)
        indexs.append(f_wenet_all[(c_count - win_size // 2):c_count + win_size // 2, ...])
    print("indexs.shape", len(indexs))
    ii.sort()
    # print("ii",ii[0],ii[-1])
    # np.save("indexs.npy", indexs)

    # with open("indexs_list.txt", "w") as f:
    #     for item in indexs:
    #         f.write(str(item.tolist()) + "\n")

    return indexs

with torch.no_grad():
    wenet_model = load_ppg_model("wenet/examples/aishell/aidata/conf/train_conformer_multi_cn.yaml", "wenet/examples/aishell/aidata/exp/conformer/wenetmodel.pt", "cuda")
    audio_file = np.zeros(16000*5)
    feature_extraction_wenet(audio_file,25,wenet_model)
    # feature_extraction_wenet("D:/gitlab/code/digitalhuman2dtrainv2/11_16k.wav",25,wenet_model)

    # result = get_weget("D:/gitlab/code/digitalhuman2dtrainv2/11_16k.wav",wenet_model)
    # print(result.shape)
    # result_list = result.tolist()
    # with open("D:/gitlab/code/digitalhuman2dtrainv2/11_16k_10s_wenet_result.txt", "w") as f:
    #     for i,item in enumerate(result_list):
    #         f.write(str(i) + "\n" + str(item) + "\n\n")                                                                                                                                                                                                                                                                     